import { Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, HostB<PERSON>ing, HostListener, ViewEncapsulation, AfterViewChecked, ChangeDetectorRef } from '@angular/core';
import { MediaObserver } from '@angular/flex-layout';

import cloneDeep from 'lodash/cloneDeep';
import find from 'lodash/find';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { TranslateService } from '@ngx-translate/core';

import { AuthenticationService } from 'app/auth/service';
import { CoreSidebarService } from '@core/components/core-sidebar/core-sidebar.service';
import { CoreConfigService } from '@core/services/config.service';
import { CoreMediaService } from '@core/services/media.service';
import { UserService } from 'app/auth/service/user.service';
import { User } from 'app/auth/models';

import { ActivatedRoute, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import Swal from 'sweetalert2';
import { ToastrUtilsService } from 'app/main/commonService/toastr-utils.service';
import { GridSystemService } from 'app/main/commonService/grid-system.service';
import { GenWidgetService } from 'app/main/commonService/gen-widget.service';
import { dataCollectWidgetId } from 'app/main/devices/device-info/dataCollectWidgetUtils';

import { menu, careMenu } from 'app/menu/menu';
import { CoreMenuService } from '@core/components/core-menu/core-menu.service';
import { StorageService } from 'app/main/commonService/storage.service';
@Component({
  selector: 'app-navbar',
  templateUrl: './navbar.component.html',
  styleUrls: ['./navbar.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class NavbarComponent implements OnInit, OnDestroy,AfterViewChecked {
  public horizontalMenu: boolean;
  public hiddenMenu: boolean;

  public coreConfig: any;
  public currentSkin: string;
  public editMode: boolean;

  public isDisabled: boolean;

  public currentUser: User;

  public languageOptions: any;
  public navigation: any;
  public selectedLanguage: any;

  public summaryReportPermission: boolean;
  public notificationPermission: boolean;
  public personalThemePermission: boolean;

  public isDeviceInfo: boolean = false;
  public themeIndex = 0;
  public activeId: number = 0;
  public dataCollect = [];
  public widgetKeyword: string = ''

  @HostBinding('class.fixed-top')
  public isFixed = false;

  @HostBinding('class.navbar-static-style-on-scroll')
  public windowScrolled = false;

  // Add .navbar-static-style-on-scroll on scroll using HostListener & HostBinding
  @HostListener('window:scroll', [])
  onWindowScroll() {
    if (
      (window.scrollY || document.documentElement.scrollTop || document.body.scrollTop > 100) &&
      this.coreConfig.layout.navbar.type == 'navbar-static-top' &&
      this.coreConfig.layout.type == 'horizontal'
    ) {
      this.windowScrolled = true;
    } else if (
      (this.windowScrolled && window.scrollY) ||
      document.documentElement.scrollTop ||
      document.body.scrollTop < 10
    ) {
      this.windowScrolled = false;
    }
  }
  public showLanguage = false;
  public widgets;
  public formatWidgets;
  public dataCollectWidgets;
  public summaryDisplay;
  public alarmDisplay;
  public detailImageData;
  public blockUIStatus = false;
  public currentURL = false;

  // Private
  private _unsubscribeAll: Subject<void>;

  /**
   * Constructor
   *
   * @param {Router} _router
   * @param {AuthenticationService} _authenticationService
   * @param {CoreConfigService} _coreConfigService
   * @param {CoreSidebarService} _coreSidebarService
   * @param {CoreMediaService} _coreMediaService
   * @param {MediaObserver} _mediaObserver
   * @param {TranslateService} _translateService
   */
  constructor(
    private _router: Router,
    private _authenticationService: AuthenticationService,
    private _coreConfigService: CoreConfigService,
    private _coreMediaService: CoreMediaService,
    private _coreSidebarService: CoreSidebarService,
    private _mediaObserver: MediaObserver,
    public _translateService: TranslateService,
    private _toastrUtilsService: ToastrUtilsService,
    private _gridSystemService: GridSystemService,
    private _genWidgetService: GenWidgetService,
    private _userService: UserService,
    private modalService: NgbModal,
    private _route: ActivatedRoute,
    private _coreMenuService: CoreMenuService,
    private _storageService: StorageService,
    private cdr: ChangeDetectorRef,
  ) {
    this.verifyNavBarPermission();

    this.languageOptions = {
      en: {
        title: 'English',
        flag: 'us'
      },
      fr: {
        title: 'Français',
        flag: 'fr'
      },
      de: {
        title: 'Deutsch',
        flag: 'de'
      },
      pt: {
        title: 'Portuguese',
        flag: 'pt'
      },
      ch: {
        title: '简体中文',
        flag: 'ch'
      },
      tw: {
        title: '繁體中文',
        flag: 'tw'
      },
      jp: {
        title: '日本語',
        flag: 'jp'
      },
    };

    // Set the private defaults
    this._unsubscribeAll = new Subject();
    this._coreConfigService
      .getConfig()
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe(config => {
        this.currentSkin = config.layout.skin;
        this.editMode = config.layout.editMode;
      });

  }

  // Public Methods
  // -----------------------------------------------------------------------------------------------------
  /**
   * Verify NavBar permission
   */
  verifyNavBarPermission() {
    const summaryAuthority = this._authenticationService.check("addOn", "reportExport", "write");
    const notificationAuthority = this._authenticationService.check("alarm", "notificationManagement", "read");
    const personalThemeAuthority = this._authenticationService.check("addOn", "personalTheme", "write");
    // console.log('summaryAuthority:', summaryAuthority)
    // console.log('notificationAuthority:', notificationAuthority)
    // console.log('personalThemeAuthority:', personalThemeAuthority)
    this.summaryReportPermission = summaryAuthority ? true : false;
    this.notificationPermission = notificationAuthority ? true : false;
    this.personalThemePermission = personalThemeAuthority ? true : false;
  }

  /**
   * Toggle sidebar open
   *
   * @param key
   */
  toggleSidebar(key): void {
    this._coreSidebarService.getSidebarRegistry(key).toggleOpen();
  }

  /**
   * Set the language
   *
   * @param language
   */
  setLanguage(language): void {
    // Set the selected language for the navbar on change
    this.selectedLanguage = language;

    // Use the selected language id for translations
    this._translateService.use(language);

    this._coreConfigService.setConfig({ app: { appLanguage: language } }, { emitEvent: true });
  }

  /**
   * Toggle Dark Skin
   */
  toggleDarkSkin() {
    // Get the current skin
    this._coreConfigService.setConfig({ layout: { skin: this.currentSkin === 'dark' ? 'default' : 'dark' } }, { emitEvent: true });
  }

  /**
   * Enter/Leave to Edit Mode
   */
  toggleEditMode() { 
    if (this.editMode) {
      if (this._gridSystemService.notSave()) {
        Swal.fire({
          title: this._translateService.instant('CONFIRM.NOTSAVETITLE'),
          text: this._translateService.instant('CONFIRM.NOTSAVECONTENT'),
          icon: 'warning',
          showCancelButton: true,
          showCloseButton: true,
          confirmButtonText: this._translateService.instant('COMMON.SAVE'),
          cancelButtonText: this._translateService.instant('COMMON.DONTSAVE'),
          customClass: {
            confirmButton: 'btn btn-primary',
            cancelButton: 'btn btn-danger ml-1'
          }
        }).then((result) => {

          if (result.value) {
            // this.editMode = false;
            // this._genWidgetService.resetChange();
            // this._coreConfigService.setConfig({ layout: { editMode: this.editMode } });
            this._genWidgetService.save().then(() => {
              this._coreConfigService.setConfig({ layout: { editMode: false } });
              this._genWidgetService.unEditMode();
              this._toastrUtilsService.showSuccessMessage(``, this._translateService.instant('DEVICES.ACTION.SAVESuccess'));
              return result.value
            });
          } else {
            this.editMode = false;
            this._genWidgetService.resetChange();
            this._coreConfigService.setConfig({ layout: { editMode: this.editMode } });
          }
        });
      } else {
        this.editMode = false;
        this._coreConfigService.setConfig({ layout: { editMode: this.editMode } });
        this._genWidgetService.unEditMode();
      }
    } else {
      this.editMode = true;
      this._coreConfigService.setConfig({ layout: { editMode: this.editMode } });
      this._genWidgetService.editMode();
    }
  }

  capitalize(str) {
    return str && str.charAt(0).toUpperCase() + str.slice(1);
  }

  public isLocatedInDoshboard = false;

  public classMapping = {
    '5gc': '5GC'
  }

  public subClassMapping = {
    'GeneralData': 'General Data',
    'CellularSpecific': 'Cellular Specific',
    'WifiSpecific': 'WiFi Specific',
    'StatisticalAnalysis': 'Statistical Analysis',
    'AppSpecific': 'APP Specific',
    'FapSpecific': 'FAP Specific',
    'AccountAdmin': 'Account Administration',
    'DeviceAlarm': 'Device Alarm',
    'GroupAdmin': 'Group Administration',
    'SystemEvents': 'System Events',
    'DeviceStatistics': 'Device Statistics',
    'DeviceAdmin': 'Device Administration',
    'ProductAdmin': 'Product Administration',
    'SystemStatistics': 'System Statistics',
    'ProvisioningStatistics': 'Provisioning Statistics',
    'PmStatistics': 'PM Statistics',
    'ServerReport': 'Server Report',
    'AlarmManagement': 'Alarm Management',
    'NotificationManagement': 'Notification Management',
    'WorkflowSetup': 'Workflow Setup',
    'OperationSetup': 'Operation Setup',
    'PolicySetup': 'Policy Setup',
    'PowerSaving': 'Energy Saving',
    'DocsisSpecific': 'Docsis Specific',
    'FileSetup': 'File Setup',
    'AccountLog': 'Account Log',
    'AccountRole': 'Account Role',
    'SystemNodes': 'System Nodes',
    'ServerSetting': 'Server Setting',
    'ServerPreference': 'Server Preference',
    'ServerLicense': 'Server License',
    'DataModel': 'Data Model',
    'LogCollection': 'Log Collection',
    'RemoteTroubleshooting': 'Remote Troubleshooting',
    'NetworkLocation': 'Network Location',
    'SystemInformation': 'System Information',
    'PmkpiCounter':'PM KPI Counter'
  }

  public tabSubclassMapping = {
    'Wifi': 'WiFi',
    'App': 'APP',
    'Ran': 'RAN'
  }



  public labelNameMapping = {
    'ProvisioningCode': 'Provisioning Code',
    'SoftwareVersion': 'Software Version',
    'CurrentLocalTime': 'Current Local Time',
    'IPv4Status': 'IPv4 Status',
    'MemoryStatus.': 'Memory Status'
  }


  grouping(widgets) {
    if (this._route.snapshot['_routerState'].url == '/dashboard') {
      this.isLocatedInDoshboard = true
    } else {
      this.isLocatedInDoshboard = false
    }
    this.formatWidgets = [];
    this.dataCollectWidgets = [];
    let temp = {};
    // console.log(widgets)
    widgets.forEach(item => {
      if (item.toggled !== false) {
        if (item.componentId.startsWith(dataCollectWidgetId)) {
          item.labelName = item.data?.find(i => i.name === 'labelName')?.value;
          this.dataCollectWidgets.push(item);
        } else {
          // let key = this.capitalize(item.subClass)
          // let key = this.isLocatedInDoshboard ? this.capitalize(item.class) : this.subClassMapping[this.capitalize(item.subClass)]
          let key = this.capitalize(item.extension) ? this.capitalize(item.extension) : this.tabSubclassMapping[this.capitalize(item.extension)];
          temp[key] ? temp[key].push(item) : temp[key] = [item];
        }
      }
    });
    for (var key in temp) {
      let widgets = temp[key];
      this.formatWidgets.push({ key, widgets })
    }
    // this.formatWidgets[0].widgets.shift()

    this.formatWidgets = this.sortWidgets(this.formatWidgets);
    // console.log(this.formatWidgets)
    this.tempData = this.formatWidgets
    this.tempKpiData = this.dataCollectWidgets
    this.widgetKeyword = ''
    return this._userService.removeUnauthorizedWidgets(this.formatWidgets)
  }

  public tempData
  public tempKpiData
  public filterWidgetsArr = []
  public filteredFormatWidgets = []
  public originalFormatWidgets: any[] = []

  filterInput() {
    // this.blockUIStatus=true
    this.formatWidgets = this.tempData
    this.dataCollectWidgets = this.tempKpiData
    // console.log(this.dataCollectWidgets)
    if (this.dataCollectWidgets.length > 0) {
      this.originalFormatWidgets = [...this.formatWidgets, ...this.dataCollectWidgets];
    } else {
      this.originalFormatWidgets = this.formatWidgets;
    }



    // console.log('Original formatWidgets:', this.originalFormatWidgets);

    // 创建一个新的数组用于存放过滤后的数据
    const filteredFormatWidgets = [];
    const filteredFormatDataWidgets = []

    if (this.widgetKeyword && this.widgetKeyword.length > 0) {

      let widgetKeywordToLower = this.widgetKeyword.toLocaleLowerCase()



      this.originalFormatWidgets.forEach(item => {

        if (item.widgets) {

          const filteredWidgets = item.widgets.filter(widget => {
            // console.log(this.subClassMapping[widget.subClass.charAt(0).toUpperCase() + widget.subClass.slice(1)] )

            return (typeof widget.extension === 'string' && widget.extension.toLowerCase().includes(widgetKeywordToLower)) ||
              (typeof widget.name === 'string' && this._translateService.instant(widget.name).toLowerCase().includes(widgetKeywordToLower)) ||
              (typeof widget.class === 'string' && widget.class.toLowerCase().includes(widgetKeywordToLower)) ||
              (typeof widget.subClass === 'string' && widget.subClass.toLowerCase().includes(widgetKeywordToLower)) 
              // || this.subClassMapping[widget.subClass.charAt(0).toUpperCase() + widget.subClass.slice(1)].toLocaleLowerCase().includes(widgetKeywordToLower)
          });

          // 如果过滤后的 widgets 存在，则保留该 item，并更新 widgets
          if (filteredWidgets.length > 0) {
            filteredFormatWidgets.push({
              ...item,
              widgets: filteredWidgets
            });
          }
        } else {
          // console.log(item)
          // 如果没有 widgets，检查 item 的其他属性
          if (
            item.labelName.toLocaleLowerCase().includes(widgetKeywordToLower) ||
            item.class.toLowerCase().includes(widgetKeywordToLower) ||
            item.subClass.toLowerCase().includes(widgetKeywordToLower) 
            // ||
            // this.subClassMapping[item.subClass]?.toLocaleLowerCase().includes(widgetKeywordToLower)
          ) {
            // console.log('success')
            filteredFormatDataWidgets.push(item);
            // console.log(filteredFormatDataWidgets)
          }
        }
      });
      // this.filteredFormatWidgets = filteredFormatWidgets;
      this.formatWidgets = filteredFormatWidgets;
      this.dataCollectWidgets = filteredFormatDataWidgets
      this.activeId = 0
      // console.log('filter Widgets',this.formatWidgets,this.dataCollectWidgets)


    } else {
      // console.log('Keyword is empty');
      this.formatWidgets = this.tempData;
      this.dataCollectWidgets = this.tempKpiData
      this.activeId = 0
      // console.log('Restored original formatWidgets:', this.formatWidgets);
    }
    // console.log('Updated formatWidgets:', this.formatWidgets,this.dataCollectWidgets);
    // this.blockUIStatus=false
  }


  helpWidgetDocument(modal) {
    this._router.navigate(['/user-manual/personal-themes']);
    modal.dismiss('Cross click')
  }

  navigateToUserManual(page: string) {
    this._router.navigate(['/user-manual', page]);
  }


  /**
   * Edit Component theme to default
   */
  toggleWidget(editModal) {

    this.isDeviceInfo = this._genWidgetService.isDeviceInfo();
    this.activeId = 0
    this.themeIndex = 0;
    this.dataCollect = this._genWidgetService.dataCollect;

    this.widgets = this._genWidgetService.renderWidgets;
    this.grouping(this.widgets);
    this.modalService.open(editModal, {
      backdrop: 'static',
      size: 'lg',
      modalDialogClass: `modal-custom${this.isDeviceInfo ? ' modal-stepper' : ''}`,
      scrollable: true
    });
  }

  statusChange(item) {
    item.hidden = !item.hidden
    this._genWidgetService.widgetChanged([item]);
  }

  addWidget(item, i, j) {
    let widget = cloneDeep(this.formatWidgets[i]["widgets"][j]);
    let originalId = item.componentId;
    let newIndex = j + 1;
    let lastComponentIdIndex = 0;
    this.formatWidgets[i]["widgets"].forEach((w, index) => {
      if (w.componentId.startsWith(widget.componentId)) {
        let arr = w.componentId.match(/\d+$/);
        if (arr) {
          newIndex = Math.max(index + 1, newIndex);
          lastComponentIdIndex = Math.max(lastComponentIdIndex, +arr[0]);
        }
      }
    })
    widget.componentId = widget.componentId + '/Add' + (lastComponentIdIndex + 1);
    widget.copyed = true;
    this.formatWidgets[i]["widgets"].splice(newIndex, 0, widget);
    this._genWidgetService.addNewWidget({ widget, originalId });
  }

  showDetailImage(widget, modal) {
    this.detailImageData = {
      name: widget.name,
      src: widget.src[this.currentSkin === 'dark' ? 'dark' : 'light']
    }
    this.modalService.open(modal, {
      backdrop: 'static',
      size: 'lg'
    });
  }

  addDataCollectWidget(item) {
    let widget = this.widgets.find(w => w.componentId === dataCollectWidgetId + '-' + item.labelName);
    if (widget.hidden) {
      widget.hidden = false;
      this.dataCollectWidgets.push(widget);
      this._genWidgetService.addDataCollectWidget(item);
    }
  }

  /**
   * Change Personal theme to default
   */
  reset(allPage = false) {
    Swal.fire({
      title: this._translateService.instant('DEVICES.ACTION.CONFIRMReset'),
      text: this._translateService.instant(allPage ? 'DEVICES.ACTION.RESETPERSONALTHEME' : 'DEVICES.ACTION.RESETCURRENTPERSONALTHEME'),
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: this._translateService.instant('COMMON.OK'),
      cancelButtonText: this._translateService.instant('COMMON.CANCEL'),
      customClass: {
        confirmButton: 'btn btn-primary',
        cancelButton: 'btn btn-danger ml-1'
      }
    }).then((result) => {
      if (result.value) {
        this._gridSystemService.resetPersonalTheme(allPage).then(() => {
          this.editMode = false;
          this._gridSystemService.unEditMode();
          this._toastrUtilsService.showSuccessMessage(``, this._translateService.instant('DEVICES.ACTION.RESETSuccess'));
          window.location.reload();
        });
      }
    });
  };

  /**
    * openModal
    * @param modalChangePassword 
    */
  openChangePasswordModal(modalChangePassword) {
    this.modalService.open(modalChangePassword, {
      backdrop: 'static',
      size: 'sm'
    });
  }

  /**
 * rebootNode method
 */
  rebootNode(modalRebootNode) {
    this.modalService.open(modalRebootNode, {
      backdrop: 'static',
      size: 'lg'
    });
  }
  /**
 * shutDown method
 */
  shutDown(modalShutDown) {
    this.modalService.open(modalShutDown, {
      backdrop: 'static',
      size: 'lg'
    });
  }

  /**
   * Logout method
   */
  logout() {
    this._authenticationService.logout(true).finally(() => {
      this._router.navigate(['/pages/authentication/login-v2']);
      // window.location.reload();
      //刷新页面的同时确实“清空”URL
      const currentPathname = window.location.pathname;
      window.location.href = currentPathname;
    });
  }

  reRegisterMenu(canAccess) {
    this._coreMenuService.unregister('main');
    if (canAccess) {
      if (this._authenticationService.isCSR) {
        this._coreMenuService.register('main', this._userService.hiddenNoPermssionMenu(careMenu));
        this.summaryReportPermission = false;
        this.notificationPermission = false;
      }
      this._coreMenuService.register('main', this._userService.hiddenNoPermssionMenu(menu));
    } else {
      this._coreMenuService.register('main', this._userService.onlyDisplayLicenseMenu());
    }
    this._coreMenuService.setCurrentMenu('main');
  }

  loadMenu(res) {
    this._userService.validForDate = res.validForDate;
    this._userService.Token = res.Token;
    if (res.validForDate && res.Token === 1) {
      this.reRegisterMenu(true)
      this.summaryDisplay = this._userService.pageController("reports", "summary")
      this.alarmDisplay = this._userService.pageController("events", "device")
    } else {
      this.reRegisterMenu(false)
      this._router.navigate(['/system/license'])
    }
  }

  // Lifecycle Hooks
  // -----------------------------------------------------------------------------------------------------

  /**
   * On init
   */
  ngOnInit(): void {
    // console.log('_route', this._route.snapshot['_routerState'].url)
    // get the currentUser details from localStorage
    this.currentUser = this._authenticationService.currentUserValue;
    // Subscribe to the config changes
    this._coreConfigService.config.pipe(takeUntil(this._unsubscribeAll)).subscribe(config => {
      this.coreConfig = config;
      this.horizontalMenu = config.layout.type === 'horizontal';
      this.hiddenMenu = config.layout.menu.hidden === true;
      this.currentSkin = config.layout.skin;

      // Fix: for vertical layout if default navbar fixed-top than set isFixed = true
      if (this.coreConfig.layout.type === 'vertical') {
        setTimeout(() => {
          if (this.coreConfig.layout.navbar.type === 'fixed-top') {
            this.isFixed = true;
          }
        }, 0);
      }
    });

    // Horizontal Layout Only: Add class fixed-top to navbar below large screen
    if (this.coreConfig.layout.type == 'horizontal') {
      // On every media(screen) change
      this._coreMediaService.onMediaUpdate.pipe(takeUntil(this._unsubscribeAll)).subscribe(() => {
        const isFixedTop = this._mediaObserver.isActive('bs-gt-xl');
        if (isFixedTop) {
          this.isFixed = false;
        } else {
          this.isFixed = true;
        }
      });
    }

    // Set the selected language from default languageOptions
    this.selectedLanguage = find(this.languageOptions, {
      id: this._translateService.currentLang
    });

    window.onbeforeunload = function (event: BeforeUnloadEvent) {
      if (this.gridService && this.gridService.notSave()) {
        event.preventDefault();
      }
    }

    if (this.currentUser && Object.keys(this.currentUser).length > 0 && this.currentUser.constructor === Object) {
      this._userService.formatInformationChanged.pipe(takeUntil(this._unsubscribeAll)).subscribe((res: any) => {
        if (res && Object.keys(res).length > 0) {
          this.loadMenu(res)
        } else {
          if (this._authenticationService.check('analysis', 'pmStatistics')) {
            this._userService.getPmUrl()
          }
          if (this._authenticationService.check('analysis', 'provisioningStatistics')) {
            this._userService.getExternalServerUrl()
          }
          this._userService.getInformation().pipe(takeUntil(this._unsubscribeAll)).subscribe((response: any) => {
            this.loadMenu(response)
          })
        }
      });
    }

  }


  sortWidgets(widgets: any[]): any[] {
    const order = ['Device', 'User', 'Alarm', 'Analysis', 'System', 'General', 'Ran', 'Cellular', 'Wifi', 'App', 'KPI'];
    return widgets.sort((a, b) => {
      const indexA = order.indexOf(a.key);
      const indexB = order.indexOf(b.key);
      if (indexA === -1) return 1; // a.key not in order, place it at the end
      if (indexB === -1) return -1; // b.key not in order, place it at the end
      return indexA - indexB;
    });
  }

  ngAfterViewChecked() {
    let url = this._router.url;
    const csrCurrentUrl = this._storageService.getSession('csrCurrentUrl');
    // console.log('url:', url, csrCurrentUrl)
    if (url === '/care' && (csrCurrentUrl == '/care' || csrCurrentUrl == null)) {
      this.isDisabled = true;
    }else{
      this.isDisabled = false;
    }
    this.cdr.detectChanges();
  }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this._unsubscribeAll.next();
    this._unsubscribeAll.complete();
  }
}

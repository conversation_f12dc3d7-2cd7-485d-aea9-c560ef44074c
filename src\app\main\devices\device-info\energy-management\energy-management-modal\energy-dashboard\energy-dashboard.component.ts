import { Component, OnInit, Input, SimpleChanges, OnChanges, Output, EventEmitter, ChangeDetectorRef, ViewChild, ElementRef } from '@angular/core';
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import { ActivatedRoute } from '@angular/router';
import { BlockUI, NgBlockUI } from 'ng-block-ui';
import { DeviceInfoService } from 'app/main/devices/device-info/device-info.service';
import isNumber from 'lodash/isNumber';
import moment from 'moment';
import { ConsoleService } from '@ng-select/ng-select/lib/console.service';
import { ConnectableObservable, endWith } from 'rxjs';
import { DatePipe } from '@angular/common';
import { TranslateService } from '@ngx-translate/core';

import { energyPolicyTypeMapping } from 'app/main/shared/power-setting-modal/energy-policy-mapping'
import { AnalysisPmService } from 'app/main/analysis-pm/analysis-pm.service';
import { DeviceActionsService } from 'app/main/devices/device-actions/device-actions.service';
@Component({
  selector: 'app-energy-dashboard',
  templateUrl: './energy-dashboard.component.html',
  styleUrl: './energy-dashboard.component.scss'
})
export class EnergyDashboardComponent extends Unsubscribe implements OnInit {
  public serialNumber;
  public deviceId: any;
  public days: number = 1;
  public selectedIcon: string = '';
  public dayItems: any[];
  //TxPower
  public blockUIStatusTxPower: boolean;
  public txPowerIsShowNoData: boolean;
  public txPowerBlockUIStatus: boolean;
  public txPowerLatestValue: number;
  public txPowerDelta: number;
  public txPowerChartData: any[];
  public txPowerLatestTime: any;
  //throughput
  public blockUIStatusThroughput: boolean;
  public throughputIsShowNoData: boolean;
  public throughputChartData: any[];
  public throughputSummary: any
  public throughputYAxis: any[];

  //powerConsumption
  public ENERGY_POLICY_TYPE = ['Normal', 'Mild-Sleep', 'Moderate-Sleep', 'Wakeable-DeepSleep', 'DeepSleep', 'Wakeup']
  public latestPolicyStats: { name: string; latest: number; delta: number; }[];
  public normalAvg: number=50;
  public overallAvg: number;
  public totalSum: number;
  public totalCount: number;
  public normalMax: number;
  public totalTimeSec: number;
  public normalAvgInput: number;
  public savedWh: number;
  public powerConsumptionChartData: any[];
  public powerConsumptionBlockUIStatus: boolean;
  public powerConsumptionLatestValue: any;
  public powerConsumptionDelta: any;
  public powerConsumptionLatestTime: string;
  public energyPolicyTotal: any
  //Power Consumption
  public powerConsumptionIsShowNoData: boolean;
  public powerRateMaxValue: number = 50;
  public powerRateValue: { value: number; name: string; }[];
  public powerRateBlockUIStatus: boolean;
  public energyStatisticsBlockUIStatus: boolean;


  //ploicy Status
  public ploicyStatusBlockUIStatus: boolean = false;
  //PM Parameter
  public PMParamBlockUIStatus: boolean = false;
  public rrcValue;
  public ueValue;
  public downloadThroughputValue;
  public uploadThroughputValue;
  public pmStatus: string
  rrcDiff: number;
  ueDiff: number;
  downloadThroughputDiff: number;
  uploadThroughputDiff: number;
  //Energy Statistics
  powerSaveValue: { value: number; name: string; }[];
  totalSumChartData: { value: number; name: string; }[];
  avgPowerChartData: { value: number; name: string; }[];
  avgPower: number;
  comparisonResult: any;
  recentStats:  any;
  previousStats: any
  comparison: boolean = false;
  maxPowerValue: number;


  constructor(
    private _route: ActivatedRoute,
    private _deviceInfoService: DeviceInfoService,
    private cdr: ChangeDetectorRef,
    private datePipe: DatePipe,
    private _analysisPmService: AnalysisPmService,
    translateService: TranslateService,
    private _deviceActionsService: DeviceActionsService,

  ) {
    super();
    this.dayItems = this.getDaysItem().map(item => { return { key: translateService.instant(item.key), value: item.value, icons: item.icons } });
    this.selectedIcon = this.dayItems[0]?.icons;

    this.customSubscribe(_route.data, res => {
      this.serialNumber = res.basicInfo.deviceIdStruct.serialNumber
      this.deviceId = res.basicInfo.id
    });
  }

  getDaysItem() {
    return [{ key: 'DASHBOARD.AVGSESSIONS.LAST1DAY', value: 1, icons: 'twenty-four-hours' }, { key: 'DASHBOARD.AVGSESSIONS.LAST7DAYS', value: 7, icons: 'seven-days' }, { key: 'DASHBOARD.AVGSESSIONS.LAST30DAYS', value: 30, icons: 'thirty-days' }]
  }

public res:any
  getPmParameter(serialNumber){

    this.PMParamBlockUIStatus = true

    let today = new Date();
    let beginTime = today.setHours(0, 0, 0, 0);
    let endTime = today.setHours(23, 59, 59);
    let params = {
      "beginTime": (new Date(beginTime)).toISOString(),
      "endTime": (new Date(endTime)).toISOString(),
    }
    this._analysisPmService.postDeviceData(this.serialNumber, params).then(res => {
      // console.log(res)
      this.res = res
      // console.log(this.res)
  //      const mockRes = JSON.stringify({
  //   num: 2,
  //   data: [
  //     {
  //       'RRC.Rate.EstabRate': 50,
  //       'UECNTX.Rate.EstabRate': 30,
  //       'DRB.UEThpDl.sum': 100,
  //       'DRB.UEThpUl.sum': 40,
  //     },
  //     {
  //       'RRC.Rate.EstabRate': 70,
  //       'UECNTX.Rate.EstabRate': 50,
  //       'DRB.UEThpDl.sum': 80,
  //       'DRB.UEThpUl.sum': 35,
  //     }
  //   ]
  // });
      let jsonRes = JSON.parse(this.res)
      let resNum = jsonRes.num

      // get latest rrc/ue/downloadThroughput/uploadThroughput value
      const data = jsonRes.data;
      if (resNum > 0) {

        const latest = this.extractAllKeysAndValues(data[0]);
        const previous = data.length > 1 ? this.extractAllKeysAndValues(data[1]) : [];

        const getDiff = (key: string): number => {
          const latestVal = +latest.find(item => item.key === key)?.value || 0;
          const prevVal = +previous.find(item => item.key === key)?.value || 0;
          // console.log(latestVal)
          // console.log(prevVal)
          return latestVal - prevVal;
        }

        // 目前值
        this.rrcValue = this.formatNumber(latest.find(item => item.key === 'RRC.Rate.EstabRate')?.value);
        this.ueValue = this.formatNumber(latest.find(item => item.key === 'UECNTX.Rate.EstabRate')?.value);
        this.downloadThroughputValue = this.formatNumber(latest.find(item => item.key === 'DRB.UEThpDl.sum')?.value);
        this.uploadThroughputValue = this.formatNumber(latest.find(item => item.key === 'DRB.UEThpUl.sum')?.value);

        // 差值
        this.rrcDiff = getDiff('RRC.Rate.EstabRate');
        this.ueDiff = getDiff('UECNTX.Rate.EstabRate');
        this.downloadThroughputDiff = getDiff('DRB.UEThpDl.sum');
        this.uploadThroughputDiff = getDiff('DRB.UEThpUl.sum');

      }
    }).catch((err) => {
      console.error(err)
      this.pmStatus = 'noPermission'
    }).finally(() => {
      this.PMParamBlockUIStatus = false;
    });

  }

    formatNumber(num: number): string {
    if (Number.isInteger(num)) {
      return num.toString();
    } else {
      return num.toFixed(2);
    }
  }

  extractAllKeysAndValues(obj: any, currentKey: string = ''): { key: string, value: any }[] {
    const keyValues: { key: string, value: any }[] = [];
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const newKey = currentKey ? `${currentKey}.${key}` : key;
        const value = obj[key];
        if (typeof value === 'object' && !Array.isArray(value)) {
          keyValues.push(...this.extractAllKeysAndValues(value, newKey));
        } else if (Array.isArray(value)) {
          for (let i = 0; i < value.length; i++) {
            if (typeof value[i] === 'object') {
              keyValues.push(...this.extractAllKeysAndValues(value[i], `${newKey}[${i}]`));
            }
          }
        } else {
          keyValues.push({ key: newKey, value });
        }
      }
    }
    return keyValues;
  }



getPowerSavingRes() {
    this.powerConsumptionChartData = []
    this.powerConsumptionBlockUIStatus=true
    this.energyStatisticsBlockUIStatus=true
    this.powerConsumptionIsShowNoData = false
    this.comparison = false
    this.comparisonResult={}
    // console.log("this.days", this.days)
    let days = this.days
    this._deviceInfoService.getPowerSavingData(this.serialNumber, this.days*2).then(res => {
      // console.log("res", res)
      //history line
      if (res.PowerConsumption) {
        //不補2W-------start(1/2)
        // let history
        // history = res.PowerConsumption?.sort((a, b) => {
        //   return new Date(a.established).getTime() - new Date(b.established).getTime();
        // })
        //不補2W-------end(1/2)

        let fixedPowerData = res.PowerConsumption
          ?.sort((a, b) => new Date(a.established).getTime() - new Date(b.established).getTime())
          ?.map((item, index, array) => {
            const next = array[index + 1];
            const shouldUpdate =
              item.sleepStatus === 4 &&
              item.data === "0" &&
              next?.sleepStatus !== 4;

            return shouldUpdate
              ? { ...item, data: "2" }
              : item;
          }) ?? [];


        //sleepStatus == 4補2W---------start
        let history = fixedPowerData as any
        let rawData = fixedPowerData;
          //sleepStatus == 4補2W---------end



        if (history?.length > 0) {
          this.powerConsumptionLatestValue = history[history.length - 1].data;
          this.powerConsumptionLatestTime = this.datePipe.transform(history[history.length - 1].established, 'yyyy/MM/dd HH:mm:ss') || '';
          this.powerConsumptionDelta = history.length > 1
            ? (this.powerConsumptionLatestValue - history[history.length - 2].data).toFixed(2)
            : null;
        } else {
          this.powerConsumptionLatestValue = null;
          this.powerConsumptionDelta = null;
        }

        // console.log('this.powerConsumptionLatestValue',this.powerConsumptionLatestValue)
        // console.log('this.powerConsumptionDelta',this.powerConsumptionDelta)

        let lineDataArr = []
        history.forEach((item) => {
          if (item.hasOwnProperty('data') && item.hasOwnProperty('established')) {
            var keyName = 'Energy Saving (power/saving)'
            var index = lineDataArr.findIndex((item) => {
              return item.name == keyName;
            });
            if (index == -1) {
              lineDataArr.push({ name: 'Energy Saving (power/saving)', value: [[item.established, parseFloat(item.data)]] });
            } else if (index != -1) {
              lineDataArr[index].value.push([item.established, parseFloat(item.data),item.sleepStatus]);
            }
          }
        });
        // console.log("dataArr", lineDataArr)
        let lineData = []
        lineDataArr.forEach(item => {
          lineData.push({
            name: 'Power Consumption',
            data: item.value,
            // stack: 'total',
            type: 'line',
            step: 'start',
            // smooth: true,
            // sampling: 'lttb',
            showSymbol: false,
          })
        })
        this.powerConsumptionChartData = lineData;

        //其他chart的計算
        //不補2W-------start(2/2)
        // let rawData = res.PowerConsumption?.filter(item => item.data && item.established) ?? [];
        //不補2W-------end(2/2)



        

        // 篩選出完整時間範圍資料
        // rawData = this.extractCompleteUnits(rawData, this.days);
        // console.log('rawData',rawData)
        if (rawData.length < 2) {
          this.powerConsumptionChartData = [];
          this.totalSum = 0;
          this.normalAvg = 0;
          this.normalMax = 0;
          this.totalCount = 0;
          return;
        }

        let totalSum = 0;
        let totalCount = 0;
        const policyRawMap: { [name: string]: number[] } = {};
        const policyGroup: { [name: string]: Map<string, number> } = {};
        const policyTimeMap: { [name: string]: number } = {};


        for (let i = 1; i < rawData.length; i++) {

          const curr = rawData[i];
          const prev = rawData[i - 1];

          const currTime = new Date(curr.established).getTime();
          const prevTime = new Date(prev.established).getTime();
          const deltaSec = (currTime - prevTime) / 1000;

          if (deltaSec <= 0 || !curr.data) continue;

          const power = curr.data as any; // 單位：W
          //當下power(W)*(當下時間-前一筆時間)/3600秒=已使用的energy(Wh)
          const energyWh = power * deltaSec / 3600;

          totalSum += energyWh;
          totalCount++;
          // console.log(power +"*"+ deltaSec+" /"+ 3600+'='+energyWh)

          this.totalSum = +totalSum.toFixed(2);
          this.totalCount = totalCount;

          const statusName = curr.sleepStatus != null
            ? this.ENERGY_POLICY_TYPE[curr.sleepStatus]
            : 'Normal';

          // 分policy
          if (!policyRawMap[statusName]) policyRawMap[statusName] = [];
          policyRawMap[statusName].push(energyWh);//policyRawMap放入已換算的Wh

          // 累計該 policy 的時長（秒）
          policyTimeMap[statusName] = (policyTimeMap[statusName] ?? 0) + deltaSec;

          // 圖表資料policyGroup
          // Day=1分一小時
          // Day=7||30 分一天
          // const date = new Date(curr.established);
          // const key = days === 1
          //   ? new Date(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours()).toISOString()
          //   : new Date(date.getFullYear(), date.getMonth(), date.getDate()).toISOString();

          // if (!policyGroup[statusName]) policyGroup[statusName] = new Map();
          // const timeMap = policyGroup[statusName];
          // timeMap.set(key, (timeMap.get(key) ?? 0) + energyWh);
        }
        // console.log('policyRawMap', policyRawMap)
        // console.log('policyGroup', policyGroup)
        // console.log('policyTimeMap', policyTimeMap)

const totalMs = (this.days*2) * 24 * 60 * 60 * 1000;
const halfMs = totalMs / 2;
const endTime = new Date(rawData[rawData.length - 1].established).getTime();
const startTime = endTime - totalMs;
const middleTime = startTime + halfMs;
// console.log('前段時間:', new Date(startTime), '~', new Date(middleTime - 1));
// console.log('後段時間:', new Date(middleTime), '~', new Date(endTime));
const firstHalf = rawData.filter(d => {
  const time = new Date(d.established).getTime();
  return time >= startTime && time < middleTime;
});
const secondHalf = rawData.filter(d => {
  const time = new Date(d.established).getTime();
  return time >= middleTime && time <= endTime;
});

if (firstHalf.length < 2 || secondHalf.length < 2) {
  this.comparisonResult = {
    status: 'Insufficient data',
  };
  this.comparison=false
  this.recentStats = this.calculateStatsFromRawData(rawData);
} else {
  this.recentStats = this.calculateStatsFromRawData(secondHalf);
  this.previousStats = this.calculateStatsFromRawData(firstHalf);

  const totalWhDelta = +(this.recentStats.totalSum - this.previousStats.totalSum).toFixed(2);
  const avgPowerDelta = +(this.recentStats.avgPower - this.previousStats.avgPower).toFixed(2);
  const savedWhDelta = +(this.recentStats.savedWh - this.previousStats.savedWh).toFixed(2);

 
  const percent = (a: number, b: number) =>
  b === 0 ? null : +(((a - b) / b) * 100).toFixed(2);
//  console.log(this.recentStats.savedWh,'-',this.previousStats.savedWh,'/',this.previousStats.savedWh,'=',percent(this.recentStats.savedWh, this.previousStats.savedWh))
  this.comparisonResult = {
    status: 'OK',
    recent: this.recentStats,
    previous: this.previousStats,
    delta: {
      totalWh: totalWhDelta,
      avgPower: avgPowerDelta,
      savedWh: savedWhDelta,
      percent: {
        totalWh: percent(this.recentStats.totalSum, this.previousStats.totalSum),
        avgPower: percent(this.recentStats.avgPower, this.previousStats.avgPower),
        savedWh: percent(this.recentStats.savedWh, this.previousStats.savedWh)
      }
    },
  };
  this.comparison=true
}
// console.log(this.comparisonResult)

        //分割時間-------start
        // //統計normal的狀態當max power的基準值
        // // 僅保留 energy > 0 的 Normal 清單
        // const normalEnergyList = (policyRawMap['Normal'] ?? []).filter(e => e > 0);
        // const normalTotal = normalEnergyList.reduce((a, b) => a + b, 0);

        // // 回推 Normal 有效時間：只保留 energy > 0 的 index
        // let normalTimeSec = 0;
        // if (policyRawMap['Normal']) {
        //   for (let i = 1; i < rawData.length; i++) {
        //     const curr = rawData[i];
        //     const prev = rawData[i - 1];

        //     const currTime = new Date(curr.established).getTime();
        //     const prevTime = new Date(prev.established).getTime();
        //     const deltaSec = (currTime - prevTime) / 1000;

        //     const statusName = curr.sleepStatus != null
        //       ? this.ENERGY_POLICY_TYPE[curr.sleepStatus]
        //       : 'Normal';

        //     const power = curr.data as any;
        //     const energyWh = power * deltaSec / 3600;

        //     if (
        //       statusName === 'Normal' &&
        //       deltaSec > 0 &&
        //       curr.data &&
        //       +curr.data !== 0 &&
        //       energyWh > 0
        //     ) {
        //       normalTimeSec += deltaSec;

        //     }
        //   }
        // }


        // let avgPowerW = 0;

        // if (normalTotal > 0 && normalTimeSec > 0) {
        //   avgPowerW = normalTotal * 3600 / normalTimeSec;//policyRawMap中的是已換算的(W*時間)Wh，所以*3600換算回W/實際使用秒數
        // } else {
        //   //如果normal統計值是0以100為基準
        //   avgPowerW = 50; // fallback
        // }

        // this.totalTimeSec = Object.values(policyTimeMap).reduce((a, b) => a + b, 0);

        // this.normalAvg = +avgPowerW.toFixed(2); // Wh
        // this.normalMax = +(avgPowerW * this.totalTimeSec / 3600).toFixed(2); // Wh
        // this.savedWh=+(this.normalMax-this.totalSum).toFixed(2)
        // this.avgPower=+(this.totalSum/(this.totalTimeSec / 3600)).toFixed(2)
        // // console.log('normalMax', this.normalMax)
        // // console.log('normalAvg', this.normalAvg)
        // // console.log('totalTimeSec', this.totalTimeSec)

        //分割時間-------end



        // this.powerSaveValue=[
        //   {
        //     value: this.savedWh,
        //     name: 'Save Energy'
        //   },
        // ];
        // this.totalSumChartData=[
        //   {
        //     value: this.totalSum,
        //     name: 'Total Energy'
        //   },
        // ];
        // this.avgPowerChartData=[
        //   {
        //     value: this.avgPower,
        //     name: 'Avg. Power'
        //   },
        // ];
        this.powerSaveValue=[
          {
            value: this.recentStats.savedWh,
            name: 'Save Energy'
          },
        ];
        this.totalSumChartData=[
          {
            value: this.recentStats.totalSum,
            name: 'Total Energy'
          },
        ];
        this.avgPowerChartData=[
          {
            value: this.recentStats.avgPower,
            name: 'Avg. Power'
          },
        ];
        

      } else {
        this.powerConsumptionChartData = []
        // this.lineChartData = []
      }
      this.powerConsumptionBlockUIStatus = false;
    }).catch((err) => {
      console.error(err)
      this.powerConsumptionIsShowNoData = true
      this.energyStatisticsBlockUIStatus = true
    }).finally(() => {
      // this.isShowNoData = this.areAllDataArraysEmpty(this.chartData);
      this.cdr.detectChanges();
      this.powerConsumptionIsShowNoData = false
      this.energyStatisticsBlockUIStatus = false
      this.powerConsumptionBlockUIStatus=false;
    });
  }

  calculateStatsFromRawData(data: any[]): {
  normalAvg: any;
  normalMax: any;
  totalSum: number;
  avgPower: number;
  savedWh: number;
} {
  let totalWh = 0;
  let totalSec = 0;
  let normalWh = 0;
  let normalSec = 0;

  for (let i = 1; i < data.length; i++) {
    const curr = data[i];
    const prev = data[i - 1];

    const currTime = new Date(curr.established).getTime();
    const prevTime = new Date(prev.established).getTime();
    const deltaSec = (currTime - prevTime) / 1000;

    if (deltaSec <= 0 || !curr.data) continue;

    const power = +curr.data;
    const energyWh = power * deltaSec / 3600;

    totalWh += energyWh;
    totalSec += deltaSec;

    const status = curr.sleepStatus != null
      ? this.ENERGY_POLICY_TYPE[curr.sleepStatus]
      : 'Normal';

    if (
      status === 'Normal' &&
      +curr.data > 0 &&
      energyWh > 0
    ) {
      normalWh += energyWh;
      normalSec += deltaSec;
    }
  }

  let normalAvgPower = (normalWh > 0 && normalSec > 0)
    ? (normalWh * 3600 / normalSec)
    : 50;
//test normalAvg太小就改成50
    // if(normalAvgPower < 45){
    //   normalAvgPower = 50
    // }
    //normalAvg是原先計算的noraml平均，這邊先以手動輸入的max為主，如果計算結果比max高再以計算的平均為主
    let normalMax
    if(this.maxPowerValue){
      if(normalAvgPower < this.maxPowerValue){
        normalAvgPower = this.maxPowerValue
        normalMax = normalAvgPower * totalSec / 3600;
      }
    }else{
        normalMax = normalAvgPower * totalSec / 3600;
    }
  const savedWh = +(normalMax - totalWh).toFixed(2);
  const avgPower = totalSec > 0 ? totalWh / (totalSec / 3600) : 0;
  // console.log('totalSec',totalSec)
// console.log('normalAvgPower',normalAvgPower,'normalMax',normalMax,'totalWh',totalWh,'avgPower',avgPower,'savedWh',savedWh)
  return {
    normalAvg: +normalAvgPower.toFixed(2),
    normalMax:+normalMax.toFixed(2),
    totalSum: +totalWh.toFixed(2),
    avgPower: +avgPower.toFixed(2),
    savedWh,
  };
}

isUp(val: number | null): boolean {
  return val != null && val > 0;
}

isDown(val: number | null): boolean {
  return val != null && val < 0;
}

isZeroOrInvalid(val: number | null): boolean {
  return val == null || isNaN(val) || val === 0;
}

getIcon(val: number | null): string {
  if (this.isUp(val)) return 'trending-up';
  if (this.isDown(val)) return 'trending-down';
  return 'minus';
}

formatPercent(val: number | null): string {
  if (val == null || isNaN(val)) return 'N/A';
  const sign = val > 0 ? '+' : '';
  return `${sign}${val.toFixed(2)}%`;
}
  getTxPowerRes() {
    this.txPowerChartData = []
    this.txPowerBlockUIStatus = true;
    this.txPowerIsShowNoData = false
    // console.log("this.days", this.days)
    let days = this.days
    this._deviceInfoService.getTxPowerData(this.serialNumber, this.days).then(res => {
      // console.log("res", res)

      //history line

      let history = (res.TxPower ?? [])
        .sort((a, b) => new Date(a.established).getTime() - new Date(b.established).getTime())
        .map(d => ({
          ...d,
          data: +(d.data * 0.1).toFixed(2)//換算
        }));

      // ✅ 取最新值與差值
      if (history?.length > 0) {
        this.txPowerLatestValue = history[history.length - 1].data;
        this.txPowerLatestTime = this.datePipe.transform(history[history.length - 1].established, 'yyyy/MM/dd HH:mm:ss') || '';
        this.txPowerDelta = history.length > 1
          ? this.txPowerLatestValue - history[history.length - 2].data
          : 0;

        let lineDataArr = []
        history.forEach((item) => {
          if (item.hasOwnProperty('data') && item.hasOwnProperty('established')) {
            var keyName = 'TxPower'
            var index = lineDataArr.findIndex((item) => {
              return item.name == keyName;
            });
            if (index == -1) {
              lineDataArr.push({ name: 'TxPower', value: [[item.established, parseFloat(item.data)]] });
            } else if (index != -1) {
              lineDataArr[index].value.push([item.established, parseFloat(item.data)]);
            }
          }
        });
        // console.log("dataArr", lineDataArr)
        let lineData = []
        lineDataArr.forEach(item => {
          lineData.push({
            name: 'TxPower',
            data: item.value,
            // stack: 'total',
            type: 'line',
            step: 'start',
            // smooth: true,
            // sampling: 'lttb',
            showSymbol: false,
          })
        })
        this.txPowerChartData = lineData;
      } else {
        this.txPowerLatestValue = null;
        this.txPowerDelta = null;
      }

      // console.log('this.txPowerLatestValue',this.txPowerLatestValue)
      // console.log('this.txPowerDelta',this.txPowerDelta)

      this.txPowerBlockUIStatus = false;
    }).catch((err) => {
      console.error(err)
      this.txPowerIsShowNoData = true
    }).finally(() => {
      // this.isShowNoData = this.areAllDataArraysEmpty(this.chartData);
      this.cdr.detectChanges();
      this.txPowerIsShowNoData = false
    });
  }


  ulLatestValue: number;
  ulDelta: number;

  dlLatestValue: number;
  dlDelta: number;

  //Network Usage的line chart(最新一筆、差值)
  getThroughputChartData() {
    this.blockUIStatusThroughput = true
    this.throughputIsShowNoData = false
    this.throughputChartData = [];
    //即使部分請求失敗也能繼續執行後續邏輯
    Promise.allSettled([
      this._deviceInfoService.getUploadThroughput(this.serialNumber, this.days),
      this._deviceInfoService.getDownloadThroughput(this.serialNumber, this.days),
      // this._deviceInfoService.getCellRadio(this.deviceId),
      this._deviceInfoService.getUECount(this.serialNumber, this.days)
    ]).then(results => {
      const [resUl,
        resDl,
        // resCellRadio, 
        resUECount] = results;

      const uploadData = resUl.status === 'fulfilled' ? (resUl.value["RRU.PrbTotUl"] || []) : [];
      const downloadData = resDl.status === 'fulfilled' ? (resDl.value["RRU.PrbTotDl"] || []) : [];
      // const cellRadioData = resCellRadio.status === 'fulfilled' ? (resCellRadio.value["CellRadio"] || []) : [];
      const ueCountData = resUECount.status === 'fulfilled' ? (resUECount.value["UECount"] || []) : [];

      // console.log(uploadData)
      // console.log(downloadData)
      // console.log(ueCountData)
      const sortByTime = (a: any, b: any) =>
        new Date(a.established).getTime() - new Date(b.established).getTime();
      //蒐集最新的值
      const extractSummary = (data: any[], isBoolean = false) => {
        if (data.length >= 2) {
          const sorted = [...data].sort(sortByTime);
          const prev = sorted[sorted.length - 2];
          const latest = sorted[sorted.length - 1];
          const latestValue = isBoolean ? (latest.data ? 1 : 0) : latest.data;
          const prevValue = isBoolean ? (prev.data ? 1 : 0) : prev.data;
          return {
            value: latestValue,
            time: this.datePipe.transform(latest.established, 'yyyy/MM/dd HH:mm:ss'),
            delta: latestValue - prevValue
          };
        } else if (data.length === 1) {
          const val = isBoolean ? (data[0].data ? 1 : 0) : data[0].data;
          return {
            value: val,
            time: data[0].established,
            delta: 0
          };
        } else {
          return {
            value: null,
            time: null,
            delta: null
          };
        }
      };

      //最新的值-統整
      this.throughputSummary = {
        ul: extractSummary(uploadData),
        dl: extractSummary(downloadData),
        ue: extractSummary(ueCountData, true),
        // cell: extractSummary(cellRadioData, true)
      };

      // console.log('throughputSummary:', this.throughputSummary);
      const allTimes = [
        this.throughputSummary.ul.time,
        this.throughputSummary.dl.time,
        this.throughputSummary.ue.time
      ];

      const latestTime = allTimes
        .filter(t => !!t) // 避免 null
        .map(t => new Date(t).getTime())
        .sort((a, b) => b - a)[0];

      // this.latestThroughputTime = latestTime ? this.datePipe.transform(new Date(latestTime), 'yyyy/MM/dd HH:mm:ss') : "";
      // console.log('最新時間：', this.latestThroughputTime);



      const ulSeries = {
        name: 'UL PRB',
        type: 'line',
        step: 'end',
        showSymbol: false,
        yAxisIndex: 0,
        data: uploadData
          .sort((a, b) => new Date(a.established).getTime() - new Date(b.established).getTime())
          .map(item => [item.established, item.data])
      };

      const dlSeries = {
        name: 'DL PRB',
        type: 'line',
        step: 'end',
        showSymbol: false,
        yAxisIndex: 0,
        data: downloadData
          .sort((a, b) => new Date(a.established).getTime() - new Date(b.established).getTime())
          .map(item => [item.established, item.data])
      };

      // const cellRadioSeries = {
      //   name: 'Cell Status',
      //   type: 'line',
      //   step: 'end',
      //   showSymbol: false,
      //   yAxisIndex: 1,
      //   data: cellRadioData
      //     .sort((a, b) => new Date(a.established).getTime() - new Date(b.established).getTime())
      //     .map(item => [item.established, item.data ? 1 : 0])
      // };

      const ueCountSeries = {
        name: 'UE Count',
        type: 'line',
        step: 'end',
        showSymbol: false,
        yAxisIndex: 1,
        data: ueCountData
          .sort((a, b) => new Date(a.established).getTime() - new Date(b.established).getTime())
          .map(item => [item.established, item.data ? 1 : 0])
      };

      // 組裝成功取得的 series
      this.throughputChartData = [
        ...(uploadData.length ? [ulSeries] : []),
        ...(downloadData.length ? [dlSeries] : []),
        ...(ueCountData.length ? [ueCountSeries] : []),
        // ...(cellRadioData.length ? [cellRadioSeries] : [])
      ];

      this.throughputYAxis = [
        {
          type: 'value',
          name: 'PRB',
          position: 'left'
        },
        {
          type: 'value',
          name: 'Count',
          position: 'right',
          // min: 0,
          // max: 1,
          // axisLabel: {
          //   formatter: (value) => {
          //     if (Number(value).toLocaleString() == '0') {
          //               return 'Offline'
          //             } else {
          //               return 'Online'
          //             }
          //   }
          // }
        }
      ]

      // console.log('this.throughputChartData',this.throughputChartData)
      this.blockUIStatusThroughput = false;
    }).catch(err => {
      console.error(err);
      this.blockUIStatusThroughput = false;
      this.throughputIsShowNoData = true
    }).finally(() => {
      // this.isShowNoData = this.areAllDataArraysEmpty(this.chartData);
      this.cdr.detectChanges();
      this.throughputIsShowNoData = false
    });
  }


  public powerSavingStatus: string
  public currentTraffic : any
  public curPolicy
  public sleepStatus
  public energyPolicyTypeMapping
  getPowerSavingStatus() {
    this.powerRateBlockUIStatus = true
    this._deviceInfoService.getPowerSavingStatus(this.serialNumber).then(res => {
      // console.log(res)
      if (res && res.length > 0) {
        // this.powerSavingStatus = res.find(item => item.name === 'Status')?.reportedValue;
        let curPower=res.find(item => item.name === 'Power Consumption')?.reportedValue||'0'
        this.currentTraffic = Number(curPower).toFixed(2)
          // res.find(item => item.name === 'Power Consumption')?.reportedValue||'0'
        this.curPolicy=res.find(item => item.name === 'Sleep Status')?.reportedValue;
        this.sleepStatus = energyPolicyTypeMapping[this.curPolicy]
        // console.log(this.currentTraffic)
        // console.log(this.sleepStatus)
        this.powerRateValue=[
          {
            value: this.currentTraffic,
            name: 'Power Consumption'
          },
        ];
        
      }
    }).finally(() => {
      this.powerRateBlockUIStatus = false;
    });
  }
  areAllDataArraysEmpty(chartData: any[]): boolean {
    return chartData.every(item => item.data.length === 0);
  }


    getAttribute() {
    this._deviceActionsService.getAttribute(this.deviceId).then((res: any) => {
          let objData = {}
          res.forEach(item => {
            objData[item.name] = item.value
          })
          let resData = res;

          // this.maxPowerValue=resData.find(item => item.name == 'escontrol.max.power').value
        const foundItem = resData.find(item => item.name === 'escontrol.max.power');
        this.maxPowerValue = foundItem ? Number(foundItem.value) : null;
          // console.log('this.maxPowerValue',this.maxPowerValue)
        });
        
    }
  

  changeFn(item: any) {
    this.selectedIcon = item.icons;
    this.days = item.value;
    this.getTxPowerRes()
    this.getThroughputChartData()
    this.getPowerSavingRes()
  }

  emittedEvents(event: any): void {
    if (event === 'reload') {
      this.getTxPowerRes()
      this.getThroughputChartData()
      this.getPowerSavingRes()
    } else if (event.type === 'changeDay') {
      this.changeFn(event.daysItem);
    }
  }

  ngOnInit(): void {
    this.getAttribute()
    this.getTxPowerRes()
    this.getThroughputChartData()
    this.getPowerSavingRes()
    this.getPowerSavingStatus()
    this.getPmParameter(this.serialNumber)
  }
}

<core-card [actions]="[ ]">
  <div
    id="cuconfig"
    *ngIf="display===undefined||values[display]"
    class="card card-body business-card justify-content-start"
    [perfectScrollbar]>
    <div
      class="btn-group btn-group-toggle"
      ngbRadioGroup
      name="indexSwitch"
      style="align-self: center; -webkit-align-self: center;"
      [style]="'width: ' + (count > 6 ? '100%' : (count * 16 + '%'))">
      <label
        ngbButtonLabel
        rippleEffect
        [class]="i === index ? 'btn-primary' : 'btn-outline-primary'"
        class="label-parent"
        *ngFor="let i of indexOptions">
        <input
          ngbButton
          type="radio"
          (click)="index=i;subIndex=0;indexChange()"
          [value]="i">
        <span style="white-space: nowrap"><span *ngIf="count<=8">Value </span>{{i + 1}}</span>
      </label>
    </div>
    <ul class="list-group list-group-flush">
      <li
        *ngFor="let item of items; let i = index"
        class="list-group-item d-flex justify-content-end align-items-center">
        <div class="mr-1 spacePre mr-auto">
          {{ item.labelName || item.name }}
          <span
            class="badge ml-50 badge-pill badge-light-secondary ng-star-inserted cursor-default"
            [ngbPopover]="popMoreInfoContent"
            triggers="click:blur"
            container="body"
            placement="auto">
            ?
            <ng-template #popMoreInfoContent>
              <div class="table-responsive">
                <table
                  class="table table-sm table-bordered"
                  style="margin: 0px;">
                  <tbody>
                    <tr *ngFor="let path of pathObj[item.name]; let i = index;">
                      <th
                        *ngIf="i===0"
                        [rowSpan]="pathObj[item.name]?.length">
                        {{protocol==='netconf'?'xpath':'path' | titlecase}}
                      </th>
                      <td
                        class="text-info"
                        style="word-break: break-all; padding: 0.3rem 0.5rem">
                        <span>
                          <span class="mr-50">
                            <button
                              type="button"
                              style="padding: 1px;"
                              class="btn datamodel-btn-icon text-success btn-sm cursor-pointer"
                              ngbTooltip="Copy"
                              container="body"
                              (click)="copyPath(path)"
                              rippleEffect>
                              <i data-feather="copy"></i>
                            </button>
                          </span>
                          <span class="text-info">{{path}}</span>
                        </span>
                      </td>
                    </tr>
                    <tr *ngIf="item.valueType || item.type">
                      <th>Value Type</th>
                      <td class="text-warning">{{item.valueType || item.type || '-'}}</td>
                    </tr>
                    <tr>
                      <th>Description</th>
                      <td class="text-secondary">
                        <small>{{item.description}}</small>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </ng-template>
          </span>
        </div>
        <ng-template #input>
          <div
            class="d-flex align-items-end"
            style="flex-direction: column;">
            <input
              *ngIf="item.disabled || item.single; else multiInput"
              type="text"
              style="width: 200px;"
              class="form-control form-control-sm"
              [class.error]="showErrorMsg[item.name]"
              (keyup)="onItemChanged($event.target.value,item)"
              [disabled]="item.disabled"
              [(ngModel)]="values[item.name]">
            <ng-template #multiInput>
              <input
                type="text"
                style="width: 200px;"
                class="form-control form-control-sm"
                [class.error]="showErrorMsg[item.name]&&showErrorMsg[item.name][index]"
                (keyup)="onItemChanged($event.target.value,item)"
                [disabled]="item.disabled || (disableItems && disableItems.includes(index + 1))"
                [(ngModel)]="values[item.name][index]">
            </ng-template>
            <small
              class="text-danger mt-50"
              *ngIf="item.single||item.disabled?showErrorMsg[item.name]:showErrorMsg[item.name]?.[index]">
              {{getErrMsg(item)}}
            </small>
          </div>
        </ng-template>
        <div *ngIf="item.select; else input">
          <div
            *ngIf="item.type === 'boolean'; else otherOption"
            class="custom-control custom-control-primary custom-switch"
            style="min-height: 1.7rem;">
            <input
              (click)="onSwitchChange($event, item);"
              type="checkbox"
              [checked]="values[item.name][index]"
              [disabled]="item.disabled || (disableItems && disableItems.includes(index + 1))"
              class="custom-control-input"
              id="switch-{{ item.name }}">
            <label
              class="custom-control-label"
              for="switch-{{ item.name }}"></label>
          </div>
          <ng-template #otherOption>
            <div class="btn-group">
              <div
                ngbDropdown
                container="body">
                <button
                  ngbDropdownToggle
                  style="padding-top:3px;padding-bottom: 3px;"
                  class="btn btn-flat-primary btn-sm"
                  type="button"
                  [disabled]="item.disabled || (disableItems && disableItems.includes(index + 1))"
                  id="defaultProvisioningButton{{i}}"
                  rippleEffect>
                  {{values[item.name][index]}}
                </button>
                <div
                  ngbDropdownMenu
                  style="max-height: 400px;overflow: auto;"
                  aria-labelledby="defaultProvisioningButton{{i}}">
                  <a
                    *ngFor="let option of options[item.name]; let m = index"
                    (click)="onItemSelected(item, option, m)"
                    ngbDropdownItem>
                    {{ option }}
                  </a>
                </div>
              </div>
            </div>
          </ng-template>
        </div>
        <div
          class="d-flex align-items-center"
          style="width: 12%; min-width: 6rem;"
          container="body">
          <i
            class="feather font-medium-3"
            [ngClass]="{
              'icon-settings text-secondary': type === 'product' && !(item.isChanged && item.isChanged.includes(index)),
              'icon-hard-drive text-success': type === 'device' && item.isChanged && item.isChanged.includes(index),
              'icon-shopping-bag text-info': (type === 'product' && item.isChanged && item.isChanged.includes(index)) || (type === 'device' && !(item.isChanged && item.isChanged.includes(index)))
            }"></i>
          <div
            placement="top"
            container="body"
            [ngClass]="{
              'badge-light-secondary': type === 'product' && !(item.isChanged && item.isChanged.includes(index)),
              'badge-light-success': type === 'device' && item.isChanged && item.isChanged.includes(index),
              'badge-light-info': (type === 'product' && item.isChanged && item.isChanged.includes(index)) || (type === 'device' && !(item.isChanged && item.isChanged.includes(index)))
            }"
            class="text-truncate badge badge-pill badge-light-info ml-40">
            {{ type === 'product' && !(item.isChanged && item.isChanged.includes(index)) ? 'Default' : (type === 'device' && item.isChanged && item.isChanged.includes(index) ? 'Device' : 'Product') }}
          </div>
        </div>
      </li>
      <li *ngIf="subItems.length" class="mt-1">
        <div class="card card-body business-card justify-content-start">
          <div
            class="btn-group btn-group-toggle"
            ngbRadioGroup
            name="subIndexSwitch"
            style="align-self: center; -webkit-align-self: center;"
            [style]="'width: ' + (subCount > 6 ? '100%' : (subCount * 16 + '%'))">
            <label
              ngbButtonLabel
              rippleEffect
              [class]="j === subIndex ? 'btn-primary' : 'btn-outline-primary'"
              class="label-parent"
              *ngFor="let j of subIndexOptions">
              <input
                ngbButton
                type="radio"
                (click)="subIndex=j;subIndexChange()"
                [value]="j">
              <span style="white-space: nowrap"><span *ngIf="subCount<=8">Subvalue </span>{{j + 1}}</span>
            </label>
          </div>
          <ul class="list-group list-group-flush" *ngIf="subItems.length">
            <li
              *ngFor="let subItem of subItems; let j = index"
              class="list-group-item d-flex justify-content-end align-items-center">
              <div class="mr-1 spacePre mr-auto">
                {{ subItem.labelName || subItem.name }}
                <span
                  class="badge ml-50 badge-pill badge-light-secondary ng-star-inserted cursor-default"
                  [ngbPopover]="subPopMoreInfoContent"
                  triggers="click:blur"
                  container="body"
                  placement="auto">
                  ?
                  <ng-template #subPopMoreInfoContent>
                    <div class="table-responsive">
                      <table
                        class="table table-sm table-bordered"
                        style="margin: 0px;">
                        <tbody>
                          <tr *ngFor="let path of pathObj[subItem.name]; let i = index;">
                            <th
                              *ngIf="i===0"
                              [rowSpan]="pathObj[subItem.name]?.length">
                              {{protocol==='netconf'?'xpath':'path' | titlecase}}
                            </th>
                            <td
                              class="text-info"
                              style="word-break: break-all; padding: 0.3rem 0.5rem">
                              <span>
                                <span class="mr-50">
                                  <button
                                    type="button"
                                    style="padding: 1px;"
                                    class="btn datamodel-btn-icon text-success btn-sm cursor-pointer"
                                    ngbTooltip="Copy"
                                    container="body"
                                    (click)="copyPath(path)"
                                    rippleEffect>
                                    <i data-feather="copy"></i>
                                  </button>
                                </span>
                                <span class="text-info">{{path}}</span>
                              </span>
                            </td>
                          </tr>
                          <tr *ngIf="subItem.valueType || subItem.type">
                            <th>Value Type</th>
                            <td class="text-warning">{{subItem.valueType || subItem.type || '-'}}</td>
                          </tr>
                          <tr>
                            <th>Description</th>
                            <td class="text-secondary">
                              <small>{{subItem.description}}</small>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </ng-template>
                </span>
              </div>
              <ng-container *ngIf="subItem.singleIndex; else normalView">
                <div *ngIf="subItem.select; else subSingleInput">
                  <div
                    *ngIf="subItem.type === 'boolean'; else subSingleOtherOption"
                    class="custom-control custom-control-primary custom-switch"
                    style="min-height: 1.7rem;">
                    <input
                      (click)="onSwitchChange($event, subItem, true, true);"
                      type="checkbox"
                      [checked]="values[subItem.name][index * count + subIndex]"
                      [disabled]="subItem.disabled || (disableSubItems && disableSubItems.includes(subIndex + 1))"
                      class="custom-control-input"
                      id="switch-{{ subItem.name }}">
                    <label
                      class="custom-control-label"
                      for="switch-{{ subItem.name }}"></label>
                  </div>
                  <ng-template #subSingleOtherOption>
                    <div class="btn-group">
                      <div
                        ngbDropdown
                        container="body">
                        <button
                          ngbDropdownToggle
                          style="padding-top:3px;padding-bottom: 3px;"
                          class="btn btn-flat-primary btn-sm"
                          type="button"
                          [disabled]="subItem.disabled || (disableSubItems && disableSubItems.includes(subIndex + 1))"
                          id="defaultProvisioningButton{{i}}{{j}}"
                          rippleEffect>
                          {{values[subItem.name][index * count + subIndex]}}
                        </button>
                        <div
                          ngbDropdownMenu
                          style="max-height: 400px;overflow: auto;"
                          aria-labelledby="defaultProvisioningButton{{i}}{{j}}">
                          <a
                            *ngFor="let option of options[subItem.name]; let m = index"
                            (click)="onItemSelected(subItem, option, m, true, true)"
                            ngbDropdownItem>
                            {{ subItem.optionsLabel ? option.label : option }}
                          </a>
                        </div>
                      </div>
                    </div>
                  </ng-template>
                </div>
                <ng-template #subSingleInput>
                  <div
                    class="d-flex align-items-end"
                    style="flex-direction: column;">
                    <input
                        type="text"
                        style="width: 200px;"
                        class="form-control form-control-sm"
                        [class.error]="showErrorMsg?.[subItem.name]?.[index * count + subIndex]"
                        (keyup)="onItemChanged($event.target.value,subItem,true,true)"
                        [disabled]="subItem.disabled || (disableSubItems && disableSubItems.includes(subIndex + 1))"
                        [(ngModel)]="values[subItem.name][index * count + subIndex]">
                    <small
                      class="text-danger mt-50"
                      *ngIf="subItem.disabled?showErrorMsg[subItem.name]:showErrorMsg[subItem.name]?.[index * count + subIndex]">
                      {{getErrMsg(subItem)}}
                    </small>
                  </div>
                </ng-template>
                <div
                  class="d-flex align-items-center"
                  style="width: 12%; min-width: 6rem;"
                  container="body">
                  <i
                    class="feather font-medium-3"
                    [ngClass]="{
                      'icon-settings text-secondary': type === 'product' && !subItem.isChanged?.includes(index * count + subIndex),
                      'icon-hard-drive text-success': type === 'device' && subItem.isChanged?.includes(index * count + subIndex),
                      'icon-shopping-bag text-info': (type === 'product' && subItem.isChanged?.includes(index * count + subIndex)) || (type === 'device' && !subItem.isChanged?.includes(index * count + subIndex))
                    }"></i>
                  <div
                    placement="top"
                    container="body"
                    [ngClass]="{
                      'badge-light-secondary': type === 'product' && !subItem.isChanged?.includes(index * count + subIndex),
                      'badge-light-success': type === 'device' && subItem.isChanged?.includes(index * count + subIndex),
                      'badge-light-info': (type === 'product' && subItem.isChanged?.includes(index * count + subIndex)) || (type === 'device' && !subItem.isChanged?.includes(index * count + subIndex))
                    }"
                    class="text-truncate badge badge-pill badge-light-info ml-40">
                    {{ type === 'product' && !subItem.isChanged?.includes(index * count + subIndex) ? 'Default' : (type === 'device' && subItem.isChanged?.includes(index * count + subIndex) ? 'Device' : 'Product') }}
                  </div>
              </div>
              </ng-container>
              <ng-template #normalView>
                <div *ngIf="subItem.select; else subInput">
                  <div
                    *ngIf="subItem.type === 'boolean'; else subOtherOption"
                    class="custom-control custom-control-primary custom-switch"
                    style="min-height: 1.7rem;">
                    <input
                      (click)="onSwitchChange($event, subItem, true);"
                      type="checkbox"
                      [checked]="values[subItem.name][index][subIndex]"
                      [disabled]="subItem.disabled || (disableSubItems && disableSubItems.includes(subIndex + 1))"
                      class="custom-control-input"
                      id="switch-{{ subItem.name }}">
                    <label
                      class="custom-control-label"
                      for="switch-{{ subItem.name }}"></label>
                  </div>
                  <ng-template #subOtherOption>
                    <div class="btn-group">
                      <div
                        ngbDropdown
                        container="body">
                        <button
                          ngbDropdownToggle
                          style="padding-top:3px;padding-bottom: 3px;"
                          class="btn btn-flat-primary btn-sm"
                          type="button"
                          [disabled]="subItem.disabled || (disableSubItems && disableSubItems.includes(subIndex + 1))"
                          id="defaultProvisioningButton{{i}}{{j}}"
                          rippleEffect>
                          {{values[subItem.name][index][subIndex]}}
                        </button>
                        <div
                          ngbDropdownMenu
                          style="max-height: 400px;overflow: auto;"
                          aria-labelledby="defaultProvisioningButton{{i}}{{j}}">
                          <a
                            *ngFor="let option of options[subItem.name]; let m = index"
                            (click)="onItemSelected(subItem, option, m, true)"
                            ngbDropdownItem>
                            {{ option }}
                          </a>
                        </div>
                      </div>
                    </div>
                  </ng-template>
                </div>
                <ng-template #subInput>
                  <div
                    class="d-flex align-items-end"
                    style="flex-direction: column;">
                    <input
                        type="text"
                        style="width: 200px;"
                        class="form-control form-control-sm"
                        [class.error]="showErrorMsg?.[subItem.name]?.[index]?.[subIndex]"
                        (keyup)="onItemChanged($event.target.value,subItem,true)"
                        [disabled]="subItem.disabled || (disableSubItems && disableSubItems.includes(subIndex + 1))"
                        [(ngModel)]="values[subItem.name][index][subIndex]">
                    <small
                      class="text-danger mt-50"
                      *ngIf="subItem.disabled?showErrorMsg[subItem.name]:showErrorMsg[subItem.name]?.[index]?.[subIndex]">
                      {{getErrMsg(subItem)}}
                    </small>
                  </div>
                </ng-template>
                <div
                  class="d-flex align-items-center"
                  style="width: 12%; min-width: 6rem;"
                  container="body">
                  <i
                    class="feather font-medium-3"
                    [ngClass]="{
                      'icon-settings text-secondary': type === 'product' && !subItem.isChanged?.[index]?.includes(subIndex),
                      'icon-hard-drive text-success': type === 'device' && subItem.isChanged?.[index]?.includes(subIndex),
                      'icon-shopping-bag text-info': (type === 'product' && subItem.isChanged?.[index]?.includes(subIndex)) || (type === 'device' && !subItem.isChanged?.[index]?.includes(subIndex))
                    }"></i>
                  <div
                    placement="top"
                    container="body"
                    [ngClass]="{
                      'badge-light-secondary': type === 'product' && !subItem.isChanged?.[index]?.includes(subIndex),
                      'badge-light-success': type === 'device' && subItem.isChanged?.[index]?.includes(subIndex),
                      'badge-light-info': (type === 'product' && subItem.isChanged?.[index]?.includes(subIndex)) || (type === 'device' && !subItem.isChanged?.[index]?.includes(subIndex))
                    }"
                    class="text-truncate badge badge-pill badge-light-info ml-40">
                    {{ type === 'product' && !subItem.isChanged?.[index]?.includes(subIndex) ? 'Default' : (type === 'device' && subItem.isChanged?.[index]?.includes(subIndex) ? 'Device' : 'Product') }}
                  </div>
                </div>
              </ng-template>
            </li>
          </ul>
        </div>
      </li>
    </ul>
  </div>
</core-card>

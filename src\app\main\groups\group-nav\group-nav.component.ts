import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Component, ElementRef, HostListener, Input, OnInit, SimpleChanges, ViewChild } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import { UserService } from 'app/auth/service/user.service';
import { StorageService } from 'app/main/commonService/storage.service';

@UntilDestroy()
@Component({
  selector: 'app-group-nav',
  templateUrl: './group-nav.component.html',
  styleUrls: ['./group-nav.component.scss']
})
export class GroupNavComponent {
  @Input() editMode: boolean;
  @Input() navContainer: any;
  @ViewChild('navList', { static: false }) navList!: ElementRef;
  public groupId: string;
  public groupLogNavAccess;
  public groupEventsNavAccess;
  constructor(
    private route: ActivatedRoute,
    private _userService: UserService,
    private router: Router,
    private _storageService: StorageService,
  ) { }

  navItems: any
  currentUrlLabel: string = '';
  lastContainerWidth: number | null = null;
  isDropdown: boolean = false;

  ngAfterViewInit(): void {
    const savedIsDropdown = this._storageService.getSession('isDropdown');
    const savedWidth = this._storageService.getSession('lastContainerWidth');

    if (savedIsDropdown !== null) {
      this.isDropdown = JSON.parse(savedIsDropdown);
    }
    if (savedWidth !== null) {
      this.lastContainerWidth = Number(savedWidth);
    }

    setTimeout(() => this.checkNavSize(), 0);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['navContainer']) {
      this.checkNavSize();
    }
  }

  @HostListener('window:resize', [])
  onResize() {
    this.checkNavSize();
  }

  private checkNavSize(): void {
    if (!this.navList || !this.navList.nativeElement || !this.navContainer) {
      return;
    }

    const navWidth = this.navList.nativeElement.scrollWidth;
    const containerWidth = this.navContainer.clientWidth;

    if (this.lastContainerWidth === null || this.lastContainerWidth !== containerWidth) {
      this.isDropdown = navWidth > containerWidth;
      this.lastContainerWidth = containerWidth;

      this._storageService.setSession('isDropdown', JSON.stringify(this.isDropdown));
      this._storageService.setSession('lastContainerWidth', String(this.lastContainerWidth));
    }
  }

  ngOnInit(): void {
    this.groupId = this.route.snapshot.paramMap.get('id')
    this.groupLogNavAccess = this._userService.pageController("log", 'operation')
    this.groupEventsNavAccess = this._userService.pageController("events", 'device')

    this.navItems = [
      { label: 'DEVICES.INFO', link: `/groups/${this.groupId}/group-info` },
      { label: 'COMMON.ALARMS', link: `/groups/${this.groupId}/group-events` },
      { label: 'DEVICES.LOGS', link: `/groups/${this.groupId}/group-logs` },
    ];

    this.router.events.pipe(untilDestroyed(this)).subscribe(event => {
      if (event instanceof NavigationEnd) {
        this.currentUrlLabel = this.navItems.find(item => item.link === this.router.url)?.label || 'Unknown';
      }
    });
    this.currentUrlLabel = this.navItems.find(item => item.link === this.router.url)?.label || 'Unknown';
  }

}

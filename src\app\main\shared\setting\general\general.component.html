<core-card
  [actions]="[ ]"
  [componentId]="accessor?.componentId"
  [blockUIStatus]="blockUIStatus">
  <h4
    *ngIf="type=='server'"
    class="card-title">
    {{ accessor.name | translate }}
  </h4>
  <div
    id="setting_general"
    class="card card-body business-card justify-content-start"
    [perfectScrollbar]>
    <div
      class="divider divider-secondary text-left mb-0"
      *ngIf="sessionAccess"
      style="overflow: visible;">
      <div class="divider-text">
        <h5 class="text-success">
          Session
          <app-more-info [tooltip]="sessionDescription"></app-more-info>
        </h5>
      </div>
    </div>
    <ul
      class="list-group list-group-flush pl-1 pr-1"
      *ngIf="sessionAccess">
      <li class="list-group-item d-flex justify-content-between align-items-center listPadding">
        <div class="mr-1">Log Level</div>
        <div class="d-flex align-items-center">
          <div>
            <div class="btn-group">
              <div
                ngbDropdown
                container="body">
                <button
                  ngbDropdownToggle
                  style="padding-top:3px;padding-bottom: 3px;"
                  class="btn btn-flat-primary btn-sm"
                  type="button"
                  id="dropdownMenuButton100"
                  [disabled]="isDisabled"
                  rippleEffect>
                  {{findList('cwmp.session.log.level').value}}
                </button>
                <div
                  ngbDropdownMenu
                  aria-labelledby="dropdownMenuButton100">
                  <a
                    *ngFor="let item of selectLogLevel"
                    ngbDropdownItem
                    (click)="saveNotify(findList('cwmp.session.log.level'),item)">
                    {{item}}
                  </a>
                </div>
              </div>
            </div>
          </div>
          <app-setting-addition
            [type]="type"
            [readonly]="isDisabled"
            [item]="findList('cwmp.session.log.level')"
            (changeDataEvt)="changeData($event)"
            label="Log Level"
            [DeviceId]="DeviceId"
            [ProductId]="ProductId"></app-setting-addition>
        </div>
      </li>
    </ul>
    <div
      *ngIf="type=='server'"
      class="divider divider-secondary text-left mt-1 mb-0"
      style="overflow: visible;">
      <div class="divider-text">
        <h5 class="text-success">
          System
          <app-more-info [tooltip]="systemDescription"></app-more-info>
        </h5>
      </div>
    </div>
    <ul
      class="list-group list-group-flush pl-1 pr-1"
      *ngIf="type=='server'">
      <li
        class="list-group-item d-flex justify-content-between align-items-center listPadding"
        *ngIf="type=='server' && findList('system.idle-timeout')">
        <div class="mr-1">
          Logout Timeout
          <small class="text-muted">(s)</small>
        </div>
        <div class="d-flex align-items-center">
          <div
            *ngIf="findValue('system.idle-timeout')!='-'"
            class="text-primary cursor-pointer text-truncate valueColumn"
            [ngClass]="{'disabled': isDisabled}"
            (click)="isEditValue(findList('system.idle-timeout'))"
            [hidden]="findList('system.idle-timeout').isEditValue">
            {{findValue('system.idle-timeout')}}
          </div>
          <div
            *ngIf="findValue('system.idle-timeout')=='-'"
            class="text-primary cursor-pointer text-truncate"
            [ngClass]="{'disabled': isDisabled}"
            (click)="isEditValue(findList('system.idle-timeout'))"
            [hidden]="findList('system.idle-timeout').isEditValue">
            <i data-feather='edit'></i>
          </div>
          <div [hidden]="!findList('system.idle-timeout').isEditValue">
            <div class="d-flex align-items-center">
              <input
                #system_idle_timeout
                type="text"
                style="width: auto;"
                class="form-control form-control-sm"
                [value]="findList('system.idle-timeout').value">
              <button
                type="button"
                class="btn btn-icon btn-flat-success"
                style="margin: 0 5px 0 5px; padding: 8px 10px 8px 8px;"
                rippleEffect
                (click)="saveNotify(findList('system.idle-timeout'),system_idle_timeout.value);">
                <div [data-feather]="'check'"></div>
              </button>
              <button
                type="button"
                class="btn btn-icon btn-flat-danger"
                style="margin: 0;padding: 8px 10px 8px 8px;"
                rippleEffect
                (click)="isEditValue(findList('system.idle-timeout'))">
                <div [data-feather]="'x'"></div>
              </button>
            </div>
          </div>
          <app-setting-addition
            [type]="type"
            [readonly]="isDisabled"
            [item]="findList('system.idle-timeout')"
            (changeDataEvt)="changeData($event)"
            label="Idle Timeout"
            [DeviceId]="DeviceId"
            [ProductId]="ProductId"></app-setting-addition>
        </div>
      </li>
      <li class="list-group-item d-flex justify-content-between align-items-center listPadding">
        <div class="mr-1">Login Fail2ban</div>
        <div class="d-flex align-items-center">
          <div>
            <div class="custom-control custom-switch">
              <input type="checkbox" class="custom-control-input" id="login.fail2ban"
                (change)="SelectFn($event,findList('login.fail2ban'))"
                [disabled]="isDisabled"
                [checked]="(findList('login.fail2ban').value==true || findList('login.fail2ban').value=='true')?true:false">
              <label class="custom-control-label" for="login.fail2ban">
              </label>
            </div>
          </div>
          <app-setting-addition [type]="type" [readonly]="isDisabled" [item]="findList('login.fail2ban')" (changeDataEvt)="changeData($event)"
            label="login.fail2ban" [DeviceId]="DeviceId" [ProductId]="ProductId"></app-setting-addition>
        </div>
      </li>
      <li class="list-group-item d-flex justify-content-between align-items-center listPadding">
        <div class="mr-1">Swagger Enabled</div>
        <div class="d-flex align-items-center">
          <div>
            <div class="custom-control custom-switch">
              <input type="checkbox" class="custom-control-input" id="system.SwaggerUI.enabled"
                (change)="SelectFn($event,findList('system.SwaggerUI.enabled'))"
                [disabled]="isDisabled"
                [checked]="(findList('system.SwaggerUI.enabled').value==true || findList('system.SwaggerUI.enabled').value=='true')?true:false">
              <label class="custom-control-label" for="system.SwaggerUI.enabled">
              </label>
            </div>
          </div>
          <app-setting-addition [type]="type" [readonly]="isDisabled" [item]="findList('system.SwaggerUI.enabled')" (changeDataEvt)="changeData($event)"
            label="system.SwaggerUI.enabled" [DeviceId]="DeviceId" [ProductId]="ProductId"></app-setting-addition>
        </div>
      </li>
    </ul>
    <div
      *ngIf="type=='server' && reportAccess"
      class="divider divider-secondary text-left mt-1 mb-0"
      style="overflow: visible;">
      <div class="divider-text">
        <h5 class="text-success">
          Server Reports
          <app-more-info [tooltip]="ServerReportsDescription"></app-more-info>
        </h5>
      </div>
    </div>
    <ul
      *ngIf="type=='server' && reportAccess"
      class="list-group list-group-flush pl-1 pr-1">
      <li class="list-group-item d-flex justify-content-between align-items-center listPadding">
        <div class="mr-1">Enabled</div>
        <div class="d-flex align-items-center">
          <div>
            <div class="custom-control custom-switch">
              <input
                type="checkbox"
                class="custom-control-input"
                id="server_report_enabled"
                [disabled]="isDisabled"
                (change)="SelectFn($event,findList('server.report.enabled'))"
                [checked]="findList('server.report.enabled').value=='true'?true:false">
              <label
                class="custom-control-label"
                for="server_report_enabled">
              </label>
            </div>
          </div>
          <app-setting-addition
            [type]="type"
            [readonly]="isDisabled"
            [item]="findList('server.report.enabled')"
            (changeDataEvt)="changeData($event)"
            label="Server Report Enabled"
            [DeviceId]="DeviceId"
            [ProductId]="ProductId"></app-setting-addition>
        </div>
      </li>
      <li class="list-group-item d-flex justify-content-between align-items-center listPadding">
        <div class="mr-1">Email Notification</div>
        <div class="d-flex align-items-center">
          <div>
            <div
              class="custom-control custom-switch"
              [ngClass]="{'cursor-not-allowed':notifyEnable()}">
              <input
                type="checkbox"
                class="custom-control-input"
                id="server_report_notify_enabled"
                [disabled]="isDisabled"
                (change)="SelectFn($event,findList('server.report.notify.enabled'))"
                [disabled]="notifyEnable()"
                [checked]="showNotifyData()">
              <label
                class="custom-control-label"
                for="server_report_notify_enabled">
              </label>
            </div>
          </div>
          <app-setting-addition
            [type]="type"
            [readonly]="isDisabled"
            [item]="findList('server.report.notify.enabled')"
            (changeDataEvt)="changeData($event)"
            label="Server Report Notify Enabled"
            [DeviceId]="DeviceId"
            [ProductId]="ProductId"></app-setting-addition>
        </div>
      </li>
      <li class="list-group-item d-flex justify-content-between align-items-center listPadding">
        <div
          class="mr-1"
          style="min-width: 90px;">
          Report Period
        </div>
        <div class="d-flex align-items-center justify-content-end">
          <div>
            <ng-select
              class="ng-select-size-sm"
              style="max-width: 300px;"
              appendTo="body"
              [items]="selectReportPriorty"
              [multiple]="true"
              [closeOnSelect]="false"
              [searchable]="false"
              bindLabel="name"
              bindValue="value"
              placeholder="Select Period"
              [disabled]="isDisabled"
              [(ngModel)]="findList('server.report.period').value"
              (change)="saveNotify(findList('server.report.period'),findValue('server.report.period'))">
              <ng-template
                ng-label-tmp
                let-item="item"
                let-clear="clear">
                <div class="ng-value-label">{{item.name}}</div>
                <div
                  class="ng-value-icon right"
                  (click)="clear(item)"
                  aria-hidden="true">
                  ×
                </div>
              </ng-template>
            </ng-select>
          </div>
          <app-setting-addition
            [type]="type"
            [readonly]="isDisabled"
            [item]="findList('server.report.period')"
            (changeDataEvt)="changeData($event)"
            label="Server Report Period"
            [DeviceId]="DeviceId"
            [ProductId]="ProductId"></app-setting-addition>
        </div>
      </li>
      <li class="list-group-item d-flex justify-content-between align-items-center listPadding">
        <div
          class="mr-1"
          style="min-width: 120px;">
          Report Content
        </div>
        <div class="d-flex align-items-center justify-content-end">
          <div>
            <ng-select
              class="ng-select-size-sm"
              style="max-width: 300px;"
              appendTo="body"
              [items]="selectReportContent"
              [multiple]="true"
              [closeOnSelect]="false"
              [searchable]="false"
              bindLabel="name"
              placeholder="Select Content"
              [disabled]="isDisabled"
              [(ngModel)]="findList('server.report.content').value"
              (change)="saveNotify(findList('server.report.content'),findValue('server.report.content'))">
              <ng-template
                ng-label-tmp
                let-item="item"
                let-clear="clear">
                <div class="ng-value-label">{{item}}</div>
                <div
                  class="ng-value-icon right"
                  (click)="clear(item)"
                  aria-hidden="true">
                  ×
                </div>
              </ng-template>
            </ng-select>
          </div>
          <app-setting-addition
            [type]="type"
            [readonly]="isDisabled"
            [item]="findList('server.report.content')"
            (changeDataEvt)="changeData($event)"
            label="Server Report Content"
            [DeviceId]="DeviceId"
            [ProductId]="ProductId"></app-setting-addition>
        </div>
      </li>
    </ul>
  </div>
</core-card>

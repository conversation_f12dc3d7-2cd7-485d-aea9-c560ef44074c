import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Component, Input, OnInit, ViewChild, ElementRef, HostListener, AfterViewInit, SimpleChanges } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { DeviceActionsService } from '../device-actions/device-actions.service';
import { UserService } from 'app/auth/service/user.service';
import { DeviceResolveService } from '../device-resolve.service';
import { DataStoreService } from 'app/main/commonService/data-store.service';
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import { Subscription, switchMap, timer } from 'rxjs';
import { StorageService } from 'app/main/commonService/storage.service';

@UntilDestroy()
@Component({
  selector: 'app-device-nav',
  templateUrl: './device-nav.component.html',
  styleUrls: ['./device-nav.component.scss']
})
export class DeviceNavComponent extends Unsubscribe {
  @Input() editMode: boolean
  @Input() navContainer: any;
  @ViewChild('navList', { static: false }) navList!: ElementRef;
  public deviceId: string;
  public protocol: any;
  public productClass: string;
  public kpiEnable: boolean = false;
  // public telemetryAttributes = {};
  public kpiUrl = null;
  public XMPPAccess;
  public AdvancedAccess;
  public deviceEventsNavAccess;
  public deviceLogAccess;

  constructor(
    private route: ActivatedRoute,
    private _deviceActionsService: DeviceActionsService,
    private _deviceResolveService: DeviceResolveService,
    private _dataStore: DataStoreService,
    private _userService: UserService,
    private router: Router,
    private _storageService: StorageService,
  ) {
    super()
    this.route.data.pipe(untilDestroyed(this)).subscribe(res => {
      this.productClass = res.basicInfo.deviceIdStruct.productClass
      // this.kpiEnable = res.kpiInfo.kpiEnable
      // this.kpiUrl = res.kpiInfo.kpiUrl
    });
  }

  /**
   * Get Device ID
   */
  getDeviceId() {
    this.deviceId = this.route.snapshot.paramMap.get('id')
    this.protocol = this.route.snapshot.paramMap.get('protocol')
    this.startPolling();
  }

  startPolling() {
    timer(2000, 1000 * 15)
      .pipe(
        untilDestroyed(this),
        switchMap(() => this._deviceActionsService.getDevicePolling(this.deviceId))
      )
      .subscribe(data => {
        const status = this._dataStore.getDeviceInfo('status');
        if (status !== data.status) {
          this._dataStore.updateDeviceInfo(data);
        }
      });
  }

  ngOnDestroy(): void {
    this._deviceResolveService.deviceInfo.next({})
  }

  navItems: any
  currentUrlLabel: string = '';
  lastContainerWidth: number | null = null;
  isDropdown: boolean = false;

  ngAfterViewInit(): void {
    const savedIsDropdown = this._storageService.getSession('isDropdown');
    const savedWidth = this._storageService.getSession('lastContainerWidth');

    if (savedIsDropdown !== null) {
      this.isDropdown = JSON.parse(savedIsDropdown);
    }
    if (savedWidth !== null) {
      this.lastContainerWidth = Number(savedWidth);
    }

    setTimeout(() => this.checkNavSize(), 0);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['navContainer']) {
      this.checkNavSize();
    }
  }

  @HostListener('window:resize', [])
  onResize() {
    this.checkNavSize();
  }

  private checkNavSize(): void {
    if (!this.navList || !this.navList.nativeElement || !this.navContainer) {
      return;
    }

    const navWidth = this.navList.nativeElement.scrollWidth;
    const containerWidth = this.navContainer.clientWidth;

    if (this.lastContainerWidth === null || this.lastContainerWidth !== containerWidth) {
      this.isDropdown = navWidth > containerWidth;
      this.lastContainerWidth = containerWidth;

      this._storageService.setSession('isDropdown', JSON.stringify(this.isDropdown));
      this._storageService.setSession('lastContainerWidth', String(this.lastContainerWidth));
    }
  }

  ngOnInit(): void {
    this.getDeviceId();
    this.XMPPAccess = this._userService.canActivateRoute("XMPPComponent")
    this.AdvancedAccess = this._userService.canActivateRoute("AdvancedComponent")
    this.deviceEventsNavAccess = this._userService.pageController("events", 'device')
    this.deviceLogAccess = this._userService.canActivateRoute("log")


    this.navItems = [
      { label: 'DEVICES.INFO', link: `/devices/${this.deviceId}/device-info` },
      { label: 'DEVICES.MAP', link: `/devices/${this.deviceId}/device-maps` },
      { label: 'COMMON.ALARMS', link: `/devices/${this.deviceId}/device-events` },
      { label: 'DEVICES.DATAMODEL', link: `/devices/${this.deviceId}/device-data-model` },
      { label: 'DEVICES.LOGS', link: `/devices/${this.deviceId}/device-logs` },
      { label: 'DEVICES.ADVANCE', link: `/devices/${this.deviceId}/device-advance` }
    ];

    this.router.events.pipe(untilDestroyed(this)).subscribe(event => {
      if (event instanceof NavigationEnd) {
        this.currentUrlLabel = this.navItems.find(item => item.link === this.router.url)?.label || 'Unknown';
      }
    });
    this.currentUrlLabel = this.navItems.find(item => item.link === this.router.url)?.label || 'Unknown';
  }

}

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { BehaviorSubject, Observable } from 'rxjs';
import { StorageService } from 'app/main/commonService/storage.service';

@Injectable({
  providedIn: 'root'
})
export class GroupsService {
  /**
   * Constructor
   *
   * @param {HttpClient} _httpClient
   */
  constructor(
    private _httpClient: HttpClient,
    private _storageService:StorageService,
  ) {
  }

  /**
   * Resolver
   *
   * @param {ActivatedRouteSnapshot} route
   * @param {RouterStateSnapshot} state
   * @returns {Observable<any> | Promise<any> | any}
   */
  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> | Promise<any> | any {
    return new Promise<void>((resolve, reject) => {
      resolve();
    });
  }


  getGroupInfo(groupId): Promise<any[]> {
    return new Promise((resolve, reject) => {
      this._httpClient.get(`nbi/device/groupAdmin/${groupId}/groupInfo`).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: reject
      });
    });
  }


  private groupRowData: any;
  private readonly storageKey = 'row-transfer-data';
  setRowData(data: any) {
    this.groupRowData = data;
    this._storageService.setSession(this.storageKey, JSON.stringify(data));
  }
  getRowData(): any {
    if (this.groupRowData) {
      return this.groupRowData;
    }

    const fromStorage = this._storageService.getSession(this.storageKey);
    if (fromStorage) {
      this.groupRowData = JSON.parse(fromStorage);
      return this.groupRowData;
    }

    return null;
  }
  clearRowData() {
    this.groupRowData = null;
    this._storageService.removeSession(this.storageKey);
  }


}

<div class="row">
  <!-- Power Consumption count -->
  <div class="col-lg-12 col-xl-3 pb-50 px-50">
    <core-card [actions]="['reload']" [blockUIStatus]="powerRateBlockUIStatus" (events)="emittedEvents($event)"
      class="energy-modal-card">
      <h5 class="energy-modal-header card-title d-flex flex-fill  align-items-center"
        style="width: calc(100% - 100px);">
        <span class="text-truncate" container="body">
          {{'POWER.POWER_CONSUMPTION'| translate }}-<span *ngIf="recentStats">avg.{{recentStats?.normalAvg||""}}</span>
        </span>
      </h5>
      <div style="height: 130px" class="w-100">
        <div class="card h-100">
          <div class="card-body d-flex pt-0">
            <div class="w-100 h-100" #powerRateRef>
              <app-energy-gauge-echarts [chartRef]="powerRateRef" [chartData]="powerRateValue"
                [maxValue]="maxPowerValue" [styleType]="'fancy'" [unit]="'W'"></app-energy-gauge-echarts>
            </div>
          </div>
        </div>
      </div>

    </core-card>
  </div>
  <!-- count -->
  <div class="col-lg-12 col-xl-4 pb-50 ">
    <!-- policy status -->
    <div class="row ">
      <div class="col-lg-6 col-xl-6 p-0 pb-50 pr-50">
        <core-card [actions]="['reload']" [blockUIStatus]="ploicyStatusBlockUIStatus" (events)="emittedEvents($event)"
          class="energy-modal-card">
          <h5 class="energy-modal-header card-title d-flex flex-fill align-items-center"
            style="width: calc(100% - 100px)">
            <span class="text-truncate" ngbTooltip="policy status" container="body">
              Policy Status
            </span>

          </h5>
          <div style="height: 45px" class="w-100">
            <div class="card-body pt-0">
              <main class="w-100 h-100 d-flex justify-content-between align-items-center">
                <div>
                  <h4 class="card-text mb-0" [ngClass]="curPolicy ? 'text-success' : 'text-secondary'">
                    {{ sleepStatus || '-' }}
                    <!-- <small>(W)</small> -->
                  </h4>
                  <!-- <p class="card-text" [ngClass]="powerSavingStatus ? 'text-success' : 'text-secondary'">
                    {{'POWER.POWER_CONSUMPTION'| translate }}
                  </p> -->
                </div>
                <div class="avatar avatar-sm p-50 m-0" [ngClass]="curPolicy ? 'bg-light-success' : 'bg-light-secondary'">
                  <div class="avatar-content">
                    <svg class="font-medium-5">
                      <use [attr.href]="'../assets/fonts/added-icon.svg#leaf'"></use>
                    </svg>
                  </div>
                </div>
              </main>
            </div>
          </div>
        </core-card>
      </div>
      <!-- tx power count -->
      <div class="col-lg-6 col-xl-6 px-0 pb-50 ">
        <core-card [actions]="['reload']" [blockUIStatus]="ploicyStatusBlockUIStatus" (events)="emittedEvents($event)"
          class="energy-modal-card">
          <h5 class="energy-modal-header card-title d-flex flex-fill align-items-center"
            style="width: calc(100% - 100px)">
            <span class="text-truncate" ngbTooltip="policy status" container="body">
              {{'POWER.TX_POWER'| translate }}
            </span>

          </h5>
          <div style="height: 45px" class="w-100">
            <div class="card-body pt-0">
              <main class="w-100 h-100 d-flex justify-content-between align-items-center">
                <div>
                  <h3 class="font-weight-bolder mb-0" >
                    {{ txPowerLatestValue || '-' }}
                    <small>(dbm)</small>
                  </h3>
                  <!-- <p class="card-text" [ngClass]="powerSavingStatus ? 'text-success' : 'text-secondary'">
                    {{'POWER.POWER_CONSUMPTION'| translate }}
                  </p> -->
                </div>
                <!--<div class="avatar avatar-sm p-50 m-0" [ngClass]="powerSavingStatus ? 'bg-light-success' : 'bg-light-secondary'">
                  <div class="avatar-content">
                     <svg class="font-medium-5">
                      <use [attr.href]="'../assets/fonts/added-icon.svg#leaf'"></use> 
                    </svg>
                  </div>
                </div>-->
                    <!-- <span class="badge badge-light-success">
                        <span [data-feather]="'trending-up'" [class]="'text-success'"></span>
                        <span class="pl-50 font-weight-bold text-success">+10%</span>
                    </span> -->
                    <ngb-alert *ngIf="txPowerDelta > 0" class="mb-0" [type]="'success'" [dismissible]="false">
                      <div class="alert-body mb-0 px-1 py-50">
                        <span [data-feather]="'trending-up'" [class]="'text-success'"></span>
                        <span class="pl-50 font-weight-bold text-success">{{'+' +txPowerDelta}}</span>
                      </div>
                    </ngb-alert>
                    <ngb-alert *ngIf="txPowerDelta < 0" class="mb-0" [type]="'danger'" [dismissible]="false">
                      <div class="alert-body mb-0 px-1 py-50">
                        <span [data-feather]="'trending-down'" [class]="'text-danger'"></span>
                        <span class="pl-50 font-weight-bold text-danger">{{txPowerDelta}}</span>
                      </div>
                    </ngb-alert>
                    <ngb-alert *ngIf="txPowerDelta == 0" class="mb-0" [type]="'secondary'" [dismissible]="false">
                      <div class="alert-body mb-0 px-1 py-50">
                        <span [data-feather]="'activity'" [class]="'text-secondary'"></span>
                        <span class="pl-50 font-weight-bold text-secondary">{{'+' +txPowerDelta}}</span>
                      </div>
                    </ngb-alert>
                
              </main>
            </div>
          </div>
        </core-card>
      </div>


      <!-- PM Parameter -->
      <div class="col-lg-12 col-xl-12 p-0">
        <core-card [actions]="[]" [blockUIStatus]="PMParamBlockUIStatus" class="energy-modal-card">
          <h5 class="energy-modal-header card-title d-flex align-items-center">
            PM Parameter
          </h5>
          <div style="height: 45px" class="w-100">
            <div class="card-body p-0">
              <main class="w-100 h-100" #content>
                <div class="d-flex justify-content-between align-items-center">
                  <div class="d-flex justify-content-between align-items-center">
                    <div>
                    <h3 class="font-weight-bolder mb-0 text-success">
                      {{rrcValue || '0'}}
                      <small class="text-success">%</small>
                    </h3>
                    <p class="card-text text-success">RRC</p>
                    </div>
                    <div
                      [ngbTooltip]="'Δ ' + rrcDiff + ' %'"
                      container="body"
                      placement="top"
                      class="ml-1"
                    >
                      <span *ngIf="rrcDiff > 0"
                        [data-feather]=" 'trending-up'"
                        class="text-danger"></span>
                      <span *ngIf="rrcDiff < 0"
                        [data-feather]=" 'trending-down'"
                        class="text-success"></span>
                      <span *ngIf="rrcDiff === 0 "
                        [data-feather]=" 'activity'"
                        class="text-secondary"></span>
                    </div>
                  </div>
                  <div class="d-flex justify-content-between align-items-center">
                    <div>
                    <h3 class="font-weight-bolder mb-0 text-warning">
                      {{ueValue || '0'}}
                      <small class="text-warning">%</small>
                    </h3>
                    <p class="card-text text-warning">UE Context</p>
                    </div>
                    <div
                      [ngbTooltip]="'Δ ' + ueDiff + ' %'"
                      container="body"
                      placement="top"
                      class="ml-1"
                    >
                      <span *ngIf="ueDiff > 0"
                        [data-feather]=" 'trending-up'"
                        class="text-danger"></span>
                      <span *ngIf="ueDiff < 0"
                        [data-feather]=" 'trending-down'"
                        class="text-success"></span>
                      <span *ngIf="ueDiff === 0 "
                        [data-feather]=" 'activity'"
                        class="text-secondary"></span>
                    </div>
                  </div>
                  <!-- <div>
                    <h3 class="font-weight-bolder mb-0 text-info"
                      [innerHTML]="(downloadThroughputValue || '0') | kpbsToSize">
                    </h3>
                    <p class="card-text text-info">Download</p>
                  </div>-->
                  
                  <div class="d-flex justify-content-between align-items-center">
                    <div>
                      <h3 class="font-weight-bolder mb-0 text-info"
                          [innerHTML]="(downloadThroughputValue || '0') | kpbsToSize">
                      </h3>
                      <p class="card-text text-info mb-0">Download</p>
                    </div> 

                    <div
                      [ngbTooltip]="'Δ ' + downloadThroughputDiff + ' kbps'"
                      container="body"
                      placement="top"
                      class="ml-1"
                    >
                      <span *ngIf="downloadThroughputDiff > 0"
                        [data-feather]=" 'trending-up'"
                        class="text-danger"></span>
                      <span *ngIf="downloadThroughputDiff < 0"
                        [data-feather]=" 'trending-down'"
                        class="text-success"></span>
                      <span *ngIf="downloadThroughputDiff === 0 "
                        [data-feather]=" 'activity'"
                        class="text-secondary"></span>
                    </div>
                  </div>
                  <div class="d-flex justify-content-between align-items-center">
                    <div>
                      <h3 class="font-weight-bolder mb-0 text-info"
                          [innerHTML]="(uploadThroughputValue || '0') | kpbsToSize">
                      </h3>
                      <p class="card-text text-info mb-0">Upload</p>
                    </div>

                    <div
                      [ngbTooltip]="'Δ ' + uploadThroughputDiff + ' kbps'"
                      container="body"
                      placement="top"
                      class="ml-1"
                    >
                      <span *ngIf="uploadThroughputDiff > 0"
                        [data-feather]=" 'trending-up'"
                        class="text-danger"></span>
                      <span *ngIf="uploadThroughputDiff < 0"
                        [data-feather]=" 'trending-down'"
                        class="text-success"></span>
                      <span *ngIf="uploadThroughputDiff === 0 "
                        [data-feather]=" 'activity'"
                        class="text-secondary"></span>
                    </div>
                  </div>

                </div>
              </main>
            </div>
          </div>
        </core-card>
      </div>

    </div>
  </div>
  <!-- Throughput -->
  <div class="col-lg-12 col-xl-5 pb-50 px-50">
    <div class="pb-0">
      <core-card [actions]="['reload','duration']" [selectedIcon]="selectedIcon" [dayItems]="dayItems"
        [blockUIStatus]="blockUIStatusThroughput" (events)="emittedEvents($event)" class="energy-modal-card">
        <h5 class="energy-modal-header card-title d-flex flex-fill  align-items-center"
          style="width: calc(100% - 100px);">
          <span class="text-truncate" container="body">
            {{'POWER.NETWORK_USAGE'| translate }}
          </span>
        </h5>
        <div style="height: 130px" class="w-100">
          <div class="card h-100">
            <div class="card-body d-flex pt-0">
              <div class="w-100 h-100" #throughputRef>
                <app-energy-line-echarts [chartRef]="throughputRef" [chartData]="throughputChartData"
                  [chartYAxis]="throughputYAxis" [isShowNoData]="throughputIsShowNoData" [title]="'ThroughputData'"
                  [unit]="''" [chartGroup]="'powerSaving'"></app-energy-line-echarts>
              </div>
            </div>
          </div>
        </div>
      </core-card>
    </div>
  </div>
  <!-- three dount -->
  <div class="col-lg-12 col-xl-7 px-0">
    <core-card [actions]="['reload','duration']" [selectedIcon]="selectedIcon" [dayItems]="dayItems" [blockUIStatus]="energyStatisticsBlockUIStatus" (events)="emittedEvents($event)"
      class="energy-modal-card">
      <h5 class="energy-modal-header card-title d-flex flex-fill  align-items-center"
        style="width: calc(100% - 100px);">
        <span class="text-truncate" container="body">
          <!-- {{'POWER.POWER_CONSUMPTION'| translate }} -->
           Energy Statistics
        </span>
      </h5>
      <div style="height: 190px" class="w-100">
        <div class="card h-100">
          <div class="card-body d-flex  align-items-center" style="padding-top: 0.7rem;">
            <div class="w-100 h-100 position-relative" #energAnalysisAnaRef>
              <app-energy-gauge-echarts [chartRef]="energAnalysisAnaRef" [chartData]="totalSumChartData"
                [maxValue]="recentStats?.normalMax||70"  [unit]="'Wh'"></app-energy-gauge-echarts>
                <!-- gauge下面的文字 -->
              <div *ngIf="comparison" class="position-absolute w-100 text-center" style="bottom: 10px; left: 0;">
                <span class="badge"
                      [ngClass]="{
                        'badge-light-success': isDown(comparisonResult.delta.percent.totalWh),
                        'badge-light-danger': isUp(comparisonResult.delta.percent.totalWh),
                        'badge-light-secondary': isZeroOrInvalid(comparisonResult.delta.percent.totalWh)
                      }">
                  <span [data-feather]="getIcon(comparisonResult.delta.percent.totalWh)"
                        [ngClass]="{
                          'text-success': isDown(comparisonResult.delta.percent.totalWh),
                          'text-danger': isUp(comparisonResult.delta.percent.totalWh),
                          'text-secondary': isZeroOrInvalid(comparisonResult.delta.percent.totalWh)
                        }">
                  </span>
                  <span class="pl-50 font-weight-bold"
                        [ngClass]="{
                          'text-success': isDown(comparisonResult.delta.percent.totalWh),
                          'text-danger': isUp(comparisonResult.delta.percent.totalWh),
                          'text-secondary': isZeroOrInvalid(comparisonResult.delta.percent.totalWh)
                        }">
                    {{ formatPercent(comparisonResult.delta.percent.totalWh) }}
                  </span>
                </span>

              </div>
              <div *ngIf="!comparison" class="position-absolute w-100 text-center" style="top: 30px; left: 0;"
                 ngbTooltip="{{comparisonResult.status}}" container="body">
                <span class="badge badge-light-warning">
                  <span [data-feather]="'alert-triangle'"></span>
                </span>
              </div>
            </div>
            <div class="w-100 h-100 position-relative" #energAnalysisAnaRef2>
              <app-energy-gauge-echarts [chartRef]="energAnalysisAnaRef2" [chartData]="powerSaveValue"
                [maxValue]="recentStats?.normalMax||70" [unit]="'Wh'"></app-energy-gauge-echarts>
              <!-- gauge下面的文字 -->
              <div *ngIf="comparison" class="position-absolute w-100 text-center" style="bottom: 10px; left: 0;">
                <span class="badge"
                      [ngClass]="{
                        'badge-light-success': isDown(comparisonResult.delta.percent.savedWh),
                        'badge-light-danger': isUp(comparisonResult.delta.percent.savedWh),
                        'badge-light-secondary': isZeroOrInvalid(comparisonResult.delta.percent.savedWh)
                      }">
                  <span [data-feather]="getIcon(comparisonResult.delta.percent.savedWh)"
                        [ngClass]="{
                          'text-success': isDown(comparisonResult.delta.percent.savedWh),
                          'text-danger': isUp(comparisonResult.delta.percent.savedWh),
                          'text-secondary': isZeroOrInvalid(comparisonResult.delta.percent.savedWh)
                        }">
                  </span>
                  <span class="pl-50 font-weight-bold"
                        [ngClass]="{
                          'text-success': isDown(comparisonResult.delta.percent.savedWh),
                          'text-danger': isUp(comparisonResult.delta.percent.savedWh),
                          'text-secondary': isZeroOrInvalid(comparisonResult.delta.percent.savedWh)
                        }">
                    {{ formatPercent(comparisonResult.delta.percent.savedWh) }}
                  </span>
                </span>
              </div>
              <div *ngIf="!comparison" class="position-absolute w-100 text-center" style="top: 30px; left: 0;"
                 ngbTooltip="{{comparisonResult.status}}" container="body">
                <span class="badge badge-light-warning">
                  <span [data-feather]="'alert-triangle'"></span>
                </span>
              </div>
            </div>
            <div class="w-100 h-100 position-relative" #energAnalysisAnaRef3>
              <app-energy-gauge-echarts [chartRef]="energAnalysisAnaRef3" [chartData]="avgPowerChartData"
                [maxValue]="recentStats?.normalAvg"[unit]="'W'"></app-energy-gauge-echarts>
                <!-- gauge下面的文字 -->
                 <div *ngIf="comparison" class="position-absolute w-100 text-center" style="bottom: 10px; left: 0;">
                <span class="badge"
                      [ngClass]="{
                        'badge-light-success': isDown(comparisonResult.delta.percent.avgPower),
                        'badge-light-danger': isUp(comparisonResult.delta.percent.avgPower),
                        'badge-light-secondary': isZeroOrInvalid(comparisonResult.delta.percent.avgPower)
                      }">
                  <span [data-feather]="getIcon(comparisonResult.delta.percent.avgPower)"
                        [ngClass]="{
                          'text-success': isDown(comparisonResult.delta.percent.avgPower),
                          'text-danger': isUp(comparisonResult.delta.percent.avgPower),
                          'text-secondary': isZeroOrInvalid(comparisonResult.delta.percent.avgPower)
                        }">
                  </span>
                  <span class="pl-50 font-weight-bold"
                        [ngClass]="{
                          'text-success': isDown(comparisonResult.delta.percent.avgPower),
                          'text-danger': isUp(comparisonResult.delta.percent.avgPower),
                          'text-secondary': isZeroOrInvalid(comparisonResult.delta.percent.avgPower)
                        }">
                    {{ formatPercent(comparisonResult.delta.percent.avgPower) }}
                  </span>
                </span>
              </div>
              <div *ngIf="!comparison" class="position-absolute w-100 text-center" style="top: 30px; left: 0;"
                 ngbTooltip="{{comparisonResult.status}}" container="body">
                <span class="badge badge-light-warning">
                  <span [data-feather]="'alert-triangle'"></span>
                </span>
              </div>
              
            </div>
          </div>
        </div>
      </div>

    </core-card>
  </div>
  <div class="col-lg-12 col-xl-5 px-50">

    <!-- Power Consumption -->
    <div class="pb-50">
      <core-card [actions]="['reload','duration']" [selectedIcon]="selectedIcon" [dayItems]="dayItems"
        [blockUIStatus]="powerConsumptionBlockUIStatus" (events)="emittedEvents($event)" class="energy-modal-card">
        <h5 class="energy-modal-header card-title d-flex flex-fill  align-items-center"
          style="width: calc(100% - 100px);">
          <span class="text-truncate" container="body">
            {{'POWER.POWER_CONSUMPTION'| translate }}
          </span>
        </h5>

        <div style="height: 75px" class="w-100">
          <div class="card h-100">
            <div class="card-body d-flex pt-0">
              <div class="w-100 h-100" #powerConsumpionRef>
                <app-energy-line-echarts [chartRef]="powerConsumpionRef" [chartData]="powerConsumptionChartData"
                  [isShowNoData]="powerConsumptionIsShowNoData" [title]="'Power-Consumpion'" [unit]="'(W)'"
                  [chartGroup]="'Energy'"></app-energy-line-echarts>
              </div>
            </div>
          </div>
        </div>

      </core-card>
    </div>
    <!-- Tx-Power -->
    <div class="">
      <core-card [actions]="['reload','duration']" [selectedIcon]="selectedIcon" [dayItems]="dayItems"
        [blockUIStatus]="txPowerBlockUIStatus" (events)="emittedEvents($event)" class="energy-modal-card">
        <h5 class="energy-modal-header card-title d-flex flex-fill  align-items-center"
          style="width: calc(100% - 100px);">
          <span class="text-truncate" container="body">
            {{'POWER.TX_POWER'| translate }}
          </span>
        </h5>
        <div style="height: 75px" class="w-100">
          <div class="card h-100">
            <div class="card-body d-flex pt-0">
              <div class="w-100 h-100" #txPowerRef>
                <app-energy-line-echarts [chartRef]="txPowerRef" [chartData]="txPowerChartData"
                  [isShowNoData]="txPowerIsShowNoData" [title]="'Tx-Power'" [unit]="'(dBm)'"
                  [chartGroup]="'Energy'"></app-energy-line-echarts>
              </div>
            </div>
          </div>
        </div>
      </core-card>
    </div>
  </div>
</div>
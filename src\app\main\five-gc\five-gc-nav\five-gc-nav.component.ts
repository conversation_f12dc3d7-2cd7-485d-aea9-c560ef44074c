import { UntilD<PERSON>roy, untilDestroyed } from '@ngneat/until-destroy';
import { Component, Input, OnInit, ViewChild, ElementRef, HostListener, AfterViewInit, SimpleChanges } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { StorageService } from 'app/main/commonService/storage.service';

@UntilDestroy()
@Component({
  selector: 'app-five-gc-nav',
  templateUrl: './five-gc-nav.component.html',
  styleUrl: './five-gc-nav.component.scss'
})
export class FiveGcNavComponent implements OnInit, AfterViewInit {
  @Input() editMode: boolean;
  @Input() navContainer: any;
  @ViewChild('navList', { static: false }) navList!: ElementRef;


  constructor(
    private router: Router,
    private _storageService: StorageService,
  ) { }

  navItems = [
    { label: 'General', link: '/5GC/general' },
    { label: 'UE', link: '/5GC/ue' },
    { label: 'AMF', link: '/5GC/amf' },
    { label: 'SMF', link: '/5GC/smf' },
    { label: 'KPI', link: '/5GC/kpi' }
  ];
  currentUrlLabel: string = '';
  lastContainerWidth: number | null = null;
  isDropdown: boolean = false;

  ngOnInit() {
    this.router.events.pipe(untilDestroyed(this)).subscribe(event => {
      if (event instanceof NavigationEnd) {
        this.currentUrlLabel = this.navItems.find(item => item.link === this.router.url)?.label || 'Unknown';
      }
    });

    this.currentUrlLabel = this.navItems.find(item => item.link === this.router.url)?.label || 'Unknown';
  }

  ngAfterViewInit(): void {
    const savedIsDropdown = this._storageService.getSession('isDropdown');
    const savedWidth = this._storageService.getSession('lastContainerWidth');

    if (savedIsDropdown !== null) {
      this.isDropdown = JSON.parse(savedIsDropdown);
    }
    if (savedWidth !== null) {
      this.lastContainerWidth = Number(savedWidth);
    }

    setTimeout(() => this.checkNavSize(), 0);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['navContainer']) {
      this.checkNavSize();
    }
  }

  @HostListener('window:resize', [])
  onResize() {
    this.checkNavSize();
  }

  private checkNavSize(): void {
    if (!this.navList || !this.navList.nativeElement || !this.navContainer) {
      return;
    }

    const navWidth = this.navList.nativeElement.scrollWidth;
    const containerWidth = this.navContainer.clientWidth;

    if (this.lastContainerWidth === null || this.lastContainerWidth !== containerWidth) {
      this.isDropdown = navWidth > containerWidth;
      this.lastContainerWidth = containerWidth;

      this._storageService.setSession('isDropdown', JSON.stringify(this.isDropdown));
      this._storageService.setSession('lastContainerWidth', String(this.lastContainerWidth));
    }
  }
}

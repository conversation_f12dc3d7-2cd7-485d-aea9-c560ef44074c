import { Component, OnInit, Input, ViewChild, ChangeDetectorRef, Inject } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ColumnMode, DatatableComponent } from '@almaobservatory/ngx-datatable';
import { DeviceInfoService } from 'app/main/devices/device-info/device-info.service'
import { DeviceDataFormatService } from '../device-data-format.service';
import { DatatableCustomizeService } from 'app/main/commonService/datatable-customize.service';
import { BehaviorSubject, debounceTime } from 'rxjs';
import { ResizeObservableService } from 'app/main/commonService/resize-observable.service';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { ParameterDetailHelper } from 'app/main/commonService/tools/ParameterDetailHelper';
import { DOCUMENT } from '@angular/common';
import { ToastrService } from 'ngx-toastr';
@UntilDestroy()
@Component({
  selector: 'app-wifi-status',
  templateUrl: './wifi-status.component.html',
  styleUrls: ['./wifi-status.component.scss']
})
export class WifiStatusComponent implements OnInit {
  @Input() accessor: any;
  @ViewChild(DatatableComponent) tableRowDetails: DatatableComponent;
  // public tooltip: any;
  // public tooltipContent: any;
  public blockUIStatus = false;
  public wifi2GItem = []
  public wifi5GItem = []
  public WifiStatusItems: any[]

  public selectedOption = 10;
  public ColumnMode = ColumnMode;
  public selected = [];

  public tableWidth = 0;
  public gridScrollHeight = 0;
  public scrollSubject: BehaviorSubject<any> = new BehaviorSubject<any>({});
  public tableOptionStatus = false;

  constructor(
    @Inject(DOCUMENT) private document: any,
    private toastr: ToastrService,
    private route: ActivatedRoute,
    private _deviceInfoService: DeviceInfoService,
    private _datatableCustomizeService: DatatableCustomizeService,
    private _resizeObservableService: ResizeObservableService,
    private cdr: ChangeDetectorRef,
    private _deviceDataFormatService: DeviceDataFormatService,
    private detailHelper: ParameterDetailHelper
  ) { }

  deviceId: string;
  getDeviceId(): void {
    this.deviceId = this.route.snapshot.paramMap.get('id');
    // console.log(this.deviceId)
    this.getWifiStatusRes(this.deviceId)
  }

  public tooltip = [
    {
      'Utilization Color': 'DEVICES.UTILIZATION_GREEN',
      description: 'DEVICES.UTILIZATION_GREEN_DESCRIPTION',
      type: ''
    },
    {
      'Utilization Color': 'DEVICES.UTILIZATION_YELLOW',
      description: 'DEVICES.UTILIZATION_YELLOW_DESCRIPTION',
      type: ''
    },
    {
      'Utilization Color': 'DEVICES.UTILIZATION_RED',
      description: 'DEVICES.UTILIZATION_RED_DESCRIPTION',
      type: ''
    }
  ]
  public moreInfoItems = [
    {
      name: "DEVICES.BAND",
      path: "Device.WiFi.Radio.{i}.OperatingFrequencyBand"
    },
    {
      name: "DEVICES.STATUS",
      path: "Device.WiFi.Radio.{i}.Status"
    },
    {
      name: "DEVICES.CHANNEL",
      path: "Device.WiFi.Radio.{i}.Channel"
    },
    {
      name: "DEVICES.BANDWIDTH",
      path: "Device.WiFi.Radio.{i}.OperatingChannelBandwidth"
    },
    {
      name: "DEVICES.MCS",
      path: "Device.WiFi.Radio.{i}.MCS"
    },
    {
      name: "DEVICES.TXPOWER",
      path: "Device.WiFi.Radio.{i}.TransmitPower"
    },
    {
      name: "DEVICES.BEACONPERIOD",
      path: "Device.WiFi.Radio.{i}.BeaconPeriod"
    },
    {
      name: "DEVICES.UTILIZATION",
      path: "Device.WiFi.Radio.{i}.X_ASKEY_Utilization"
    },
    {
      name: "DEVICES.CLIENTS",
      path: "Device.WiFi.AccessPoint.{i}.AssociatedDeviceNumberOfEntries"
    }
  ]

  public wifiNameMapping = {
    'Band': 'DEVICES.BAND',
    'Status': 'USERS.STATUS',
    'AutoChannelEnable': 'Auto Channel',
    'Channel': 'DEVICES.CHANNEL',
    'ChannelBandwidth': 'DEVICES.BANDWIDTH',
    'MCS': 'MCS',
    // 'BytesReceived': 'DEVICES.RECEIVED',
    // 'BytesSent': 'DEVICES.SENT',
    'TransmitPower': 'DEVICES.TXPOWER',
    'BeaconPeriod': 'DEVICES.BEACONPERIOD',
    'Utilization': 'DEVICES.UTILIZATION',
    'AssociatedDeviceNumber': 'DEVICES.CLIENTS'
  }

  public columnSelectOptions = [
    { name: 'Band', prop: 'Band', translate: 'DEVICES.BAND', width: 70, flexGrow: 70, columnStatus: true, disableStatus: true },
    { name: 'Status', prop: 'Status', translate: 'DEVICES.STATUS', width: 50, flexGrow: 50, columnStatus: true },
    { name: 'Channel', prop: 'Channel', translate: 'DEVICES.CHANNEL', width: 50, flexGrow: 50, columnStatus: true },
    { name: 'Utilization', prop: 'X_ASKEY_Utilization', translate: 'DEVICES.UTILIZATION', width: 50, flexGrow: 50, columnStatus: true },
    { name: 'Transmit Power', prop: 'TransmitPower', translate: 'DEVICES.TXPOWER', width: 50, flexGrow: 50, columnStatus: true },
    { name: 'Bandwidth', prop: 'ChannelBandwidth', translate: 'DEVICES.BANDWIDTH', width: 50, flexGrow: 50, columnStatus: true },
    { name: 'Beacon Period', prop: 'BeaconPeriod', translate: 'DEVICES.BEACONPERIOD', width: 50, flexGrow: 50, columnStatus: true },
    { name: 'MCS', prop: 'MCS', translate: 'DEVICES.MCS', width: 50, flexGrow: 50, columnStatus: true },
    { name: 'Clients', prop: 'AssociatedDeviceNumber', translate: 'DEVICES.CLIENTS', width: 70, flexGrow: 70, columnStatus: true }
  ];

  onResize({ column, newValue }) {
    this.columnSelectOptions = this._datatableCustomizeService.resize(column, newValue, this.columnSelectOptions, this.accessor, this.tableRowDetails);
  }

  changeColumn(event): void {
    if (event.cloumns) {
      this.columnSelectOptions = [...event.cloumns];
    }
  }

  mCopyPath(value) {
    const selectBox = this.document.createElement('textarea');
    selectBox.style.position = 'fixed';
    selectBox.value = value;
    this.document.body.appendChild(selectBox);
    selectBox.focus();
    selectBox.select();
    this.document.execCommand('copy');
    this.document.body.removeChild(selectBox);
    this.toastr.success('', 'Copied successfully', { positionClass: 'toast-bottom-right', toastClass: 'toast ngx-toastr', closeButton: true });
  }

  emittedEvents(event: string): void {
    switch (event) {
      case 'reload':
        this.getWifiStatusRes(this.route.snapshot.paramMap.get('id'));
        break;
    }
  };

  copyPath(value) {
    this.detailHelper.copyPath(value)
  }

  addSummary(item) {
    this.detailHelper.addSummary(item)
  }

  isNumber(value) {
    if (value && /^\d+$/.test(value)) {
      return true;
    }
    return false;
  }

  /**
* get Wifi Status
*/
  getWifiStatusRes(deviceId) {
    this.blockUIStatus = true;
    this._deviceInfoService.getWifiStatus(deviceId).pipe(untilDestroyed(this)).subscribe({
      next: (res: any) => {
        // console.log(res)
        let data = res
        this.WifiStatusItems = res ? Object.values(res) : []

        this.WifiStatusItems.forEach(item => {
          item.BeaconPeriod = item.BeaconPeriod ? item.BeaconPeriod + "ms" : 'N/A';
          item.TransmitPower = item.TransmitPower ? item.TransmitPower == -1 ? 'auto' : item.TransmitPower + '%' : 0 + '%';
          for (let key in item) {
            // console.log(key)
            // console.log(item[key])
            if (key.includes('BytesReceived')) {
              item[key] = parseFloat(item[key])
            }
            else if (key.includes('BytesSent')) {
              item[key] = parseFloat(item[key])
            }
            else if (key.includes('Utilization')) {
              item[key] = parseFloat(item[key])
            }
          }

          // if (item.name=='WiFi.1.AssociatedDeviceNumber') {
          //   wifi1AssociatedDeviceNumber += item.reportedValue;
          // } else if (item.name=='WiFi.2.AssociatedDeviceNumber') {
          //   wifi2AssociatedDeviceNumber += item.reportedValue;
          // } else if (item.name=='WiFi.3.AssociatedDeviceNumber') {
          //   wifi3AssociatedDeviceNumber += item.reportedValue;
          // }
        })

        const bandOrder = { "2.4GHz": 0, "5GHz": 1, "6GHz": 2 };

        this.WifiStatusItems.sort((a, b) => bandOrder[a.Band] - bandOrder[b.Band]);




      },
      error: error => {
        console.log(error);
      },
      complete: () => {
        this.blockUIStatus = false;
      }
    })
  }

  ngOnInit(): void {
    this.tableOptionStatus = !!this._datatableCustomizeService.getTableOption(this.accessor.componentId);
    this.columnSelectOptions = this._datatableCustomizeService.mergeOption(this.accessor.componentId, this.columnSelectOptions);
    const dueTime = this.tableOptionStatus ? 400 : 0;
    this.scrollSubject.pipe(untilDestroyed(this), debounceTime(dueTime)).subscribe(res => {
      if (res.gridScrollHeight && res.tableWidth && !this._resizeObservableService.windowResizeState) {
        this.columnSelectOptions = this._datatableCustomizeService.formatColumn(this.tableRowDetails, this.columnSelectOptions, this.tableWidth, this.accessor);
      }
    })
    this.getDeviceId()
  }

  ngAfterViewChecked(): void {
    const { scrollHeight, innerWidth: tableWidth } = this.tableRowDetails.bodyComponent;
    const { scrollHeight: gridScrollHeight } = this.accessor.gridRef?.el;
    if (((gridScrollHeight && gridScrollHeight > this.gridScrollHeight) || (tableWidth && tableWidth !== this.tableWidth)) && scrollHeight) {
      this.tableWidth = tableWidth;
      this.gridScrollHeight = gridScrollHeight;
      this.scrollSubject.next({ gridScrollHeight, tableWidth });
    }
    this.cdr.detectChanges();
  }

  /**
  * On destroy
  */
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this._deviceInfoService.wifiStatusObservable = null
  }

}

import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Component, OnInit, Input, ViewEncapsulation, ViewChild, Output, EventEmitter } from '@angular/core';
import { ColumnMode, SelectionType } from '@almaobservatory/ngx-datatable';
import { ProductDataService } from 'app/main/products/products.service';
import { ToastrUtilsService } from 'app/main/commonService/toastr-utils.service';
import { UserService } from 'app/auth/service/user.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import Stepper from 'bs-stepper';
import Swal from 'sweetalert2';

import { TranslateService } from '@ngx-translate/core';
import { AuthenticationService } from 'app/auth/service';
import { Subject, fromEvent } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { provisioningTypeList, Wifi_AP_List, SmallCell_List, Enterprise_Wifi_List, Mesh_Wifi_Router_List } from '../provisioningType';
import { ImageCompressionUploadService } from 'app/main/commonService/image-compression-upload.service';
import { SanityTestService } from 'app/main/commonService/sanity-test.service';
import { InitProvisioningService } from 'app/main/shared/init-provisioning/init-provisioning.service';
import { FileDownloadService } from 'app/main/commonService/file-download.service';
import { csv2jsonArray } from 'app/main/commonService/tools/csv2json';
import { GroupListService } from 'app/main/groups/group-list/group-list.service';
import { ProvisioningTypeCheckService } from 'app/main/products/provisioning-type-check.service';
import { XlsxLoaderService } from 'app/main/commonService/xlsx-loader.service';

@UntilDestroy()
@Component({
  selector: 'app-product-add-form',
  templateUrl: './product-add-form.component.html',
  styleUrls: ['./product-add-form.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ProductAddFormComponent implements OnInit {
  @ViewChild('selectedImage') selectedImageFile;
  @Output() httpResponseEvt = new EventEmitter<boolean>();
  @Input() scopeList: any;
  @Input() productClassList: any;
  @Input() initDefaults: any;
  @Input() dataModelList: any;
  @Input() topicList: any;
  @Input() addData: any;
  @Input() isEditModal: string;
  @Input() modalConfig: any;
  @Input() groupList: any;
  // public
  // public selectedType;
  public ColumnMode = ColumnMode;
  public selectedOption = 10;
  public selected = [];
  public SelectionType = SelectionType;
  public currentSummary: any = {};
  public addSummaryData: any = {};
  public defaultImage: any = "assets/images/pages/defaultProduct.png";
  public defaultImage2: any = "assets/images/pages/defaultProduct2.png";
  public smallCellImage: any = "assets/images/product/Small Cell.png";
  public wifiApImage: any = "assets/images/product/Wifi Networking.png";
  public previewImg: any = this.defaultImage;
  public provisioningTypeList;
  public setSmallCellDefaultImage = SmallCell_List;
  public Wifi_AP_List = Wifi_AP_List;
  public selectedDataModel: any = []
  public allowedFileTypes: string = '.png, .jpeg, .jpg';
  // private
  private horizontalWizardStepper: Stepper;
  private addProductForm: any;
  private maxImgSize = 100 * 1024;
  private initTemp = {}
  private permission: any = {}
  private numReg = /^[\d\,]+$/

  public ouiDescription = [
    {
      description: 'PRODUCTS.OUIDESCRIPTION'
    },
  ]
  public productClassDescription = [
    {
      description: 'PRODUCTS.PRODUCTCLASSDESCRIPTION'
    },
  ]

  public proDeviceLimitDescription = [
    {
      description: 'PRODUCTS.PRODEVICELIMITDESCRIPTION'
    },
  ]

  public netDeviceLimitDescription = [
    {
      description: 'PRODUCTS.NETDEVICELIMITDESCRIPTION'
    },
  ]

  public provisioningTypeDescription = [
    {
      description: 'PRODUCTS.PROVISIONINGTYPEDESCRIPTION'
    },
  ]

  public proAccessControlDescription = [
    {
      name: 'Allow all ',
      description: 'PRODUCTS.ALLOWALLDESCRIPTION'
    },
    {
      name: 'Allow list ',
      description: 'PRODUCTS.PROALLOWLISTDESCRIPTION'
    },
  ]

  public netAccessControlDescription = [
    {
      name: 'Allow all ',
      description: 'PRODUCTS.ALLOWALLDESCRIPTION'
    },
    {
      name: 'Allow list ',
      description: 'PRODUCTS.NETALLOWLISTDESCRIPTION'
    },
  ]

  public sNoList = [
    { sn: 'E1D1C000013' },
    { sn: 'E1C7K000901' },
    { sn: 'E1C7K000902' },
    { sn: 'E1C84001033' },
    { sn: 'DDOUSPCWMP0000TR9971' },
    { sn: 'DD18USP0000TR001' },
    { sn: 'E1C84001019' },
    { sn: 'E4ABT000033' }
  ]

  public protocolOption = [
    // { name: 'CWMP', value: 'cwmp' },
    // { name: 'USP', value: 'usp' },
    // { name: 'Netconf', value: 'netconf' },
  ];

  public protocolType = ['cwmp', 'usp', 'netconf']
  // public noCollectionMap = [
  //   "Serial Number", 
  //   "First Install Time", 
  //   "Last Connect Event", 
  //   "Last Connect Time",
  //   "Provisioning Code",
  //   "Uptime", 
  //   "Hardware Version", 
  //   "IMEI", 
  //   "MAC Address",
  //   "Time Zone", 
  //   "Model Name"
  // ]

  public tempData = []
  public filterKeys = {
    "Serial Number": "sn"
  }

  public allowedAll = true;

  public extraSno: any;
  public summaryReportList: any;
  public _summaryReportList: any = [];
  public devicesPublish = [];
  public uspSubscribe = [];
  public modalAddSummaryStatus = 'Add';
  public typeList = [
    { key: "Allow All", value: "Allow all" },
    { key: "Allow List", value: "Allow list" }
  ]
  public searchMap = {
    sName: "",
    sProduct: "",
    sSource: "",
  }
  public sourceList = [
    { "name": "All Source Type", "value": "" },
    { "name": "ServerDefault", "value": "ServerDefault" },
    { "name": "Product", "value": "Product" }
  ]
  public productSummaryReport = {};
  public editData = { stages: {}, target: {} };
  public stepperIndex = 0
  public previousNameFilter = ""
  public previousLabelFilter = ""
  public previousSourceFilter = ""
  public loading = false;
  // public serverDefaultMap = {}
  // public productClassMap = {}
  // private nameReg = /^[a-zA-Z0-9_\-s.]{1,128}$/;
  public formError: any = {}
  public FAIL = this.translateService.instant('DEVICES.ACTION.FAIL');
  public SUCCESS = this.translateService.instant('DEVICES.ACTION.SUCCESS');
  public CROSSCLICK = this.translateService.instant('CONFIRM.CROSSCLICK');
  public SAVESRSSUCC = this.translateService.instant('CONFIRM.SAVESRSSUCC');
  public NAMEERROR = this.translateService.instant('CONFIRM.NAMEERROR');
  public ONLYNAMESYM = this.translateService.instant('CONFIRM.ONLYNAMESYM');
  public PLEASECONF = this.translateService.instant('CONFIRM.PLEASECONF');
  public ADDPRODSUCC = this.translateService.instant('CONFIRM.ADDPRODSUCC');
  public ADDRANSUCC = this.translateService.instant('CONFIRM.ADDRANSUCC');
  public ADDAPNSUCC = this.translateService.instant('CONFIRM.ADDAPNSUCC');
  public ADDMESHSUCC = this.translateService.instant('CONFIRM.ADDMESHSUCC');
  public UPDATEPRODSUCC = this.translateService.instant('CONFIRM.UPDATEPRODSUCC');
  public UPDATERANSUCC = this.translateService.instant('CONFIRM.UPDATERANSUCC');
  public UPDATEAPNSUCC = this.translateService.instant('CONFIRM.UPDATEAPNSUCC');
  public UPDATEMESHSUCC = this.translateService.instant('CONFIRM.UPDATEMESHSUCC');
  public PLEASEFILL = this.translateService.instant('CONFIRM.PLEASEFILL');
  public WARNING = this.translateService.instant('CONFIRM.WARNING');
  public IMAGEOVERSIZE = this.translateService.instant('CONFIRM.IMAGEOVERSIZE');
  public PARSINGFAILED = this.translateService.instant('PRODUCTS.PARSINGFAILED');
  public FILETYPEERROR = this.translateService.instant('PRODUCTS.FILETYPEERROR');
  public INVALIDFILETYPE = this.translateService.instant('COMMON.INVALIDFILETYPE');
  constructor(
    private _productDataService: ProductDataService,
    private _toastrUtilsService: ToastrUtilsService,
    private modalService: NgbModal,
    private translateService: TranslateService,
    private _userService: UserService,
    private authService: AuthenticationService,
    private _imageCompressionUploadService: ImageCompressionUploadService,
    private _initProvisionService: InitProvisioningService,
    private _fileDownloadService: FileDownloadService,
    private st: SanityTestService,
    private _groupListService: GroupListService,
    private ProvisioningTypeCheckService: ProvisioningTypeCheckService,
     private xlsxLoaderService: XlsxLoaderService,
  ) {
    translateService.onLangChange.pipe(untilDestroyed(this)).subscribe(() => {
      this.FAIL = this.translateService.instant('DEVICES.ACTION.FAIL');
      this.SUCCESS = this.translateService.instant('DEVICES.ACTION.SUCCESS');
      this.CROSSCLICK = this.translateService.instant('CONFIRM.CROSSCLICK');
      this.SAVESRSSUCC = this.translateService.instant('CONFIRM.SAVESRSSUCC');
      this.NAMEERROR = this.translateService.instant('CONFIRM.NAMEERROR');
      this.ONLYNAMESYM = this.translateService.instant('CONFIRM.ONLYNAMESYM');
      this.PLEASECONF = this.translateService.instant('CONFIRM.PLEASECONF');
      this.ADDPRODSUCC = this.translateService.instant('CONFIRM.ADDPRODSUCC');
      this.ADDRANSUCC = this.translateService.instant('CONFIRM.ADDRANNSUCC');
      this.ADDAPNSUCC = this.translateService.instant('CONFIRM.ADDAPNSUCC');
      this.ADDMESHSUCC = this.translateService.instant('CONFIRM.ADDMESHNSUCC');
      this.UPDATEPRODSUCC = this.translateService.instant('CONFIRM.UPDATEPRODSUCC');
      this.UPDATERANSUCC = this.translateService.instant('CONFIRM.UPDATERANSUCC');
      this.UPDATEAPNSUCC = this.translateService.instant('CONFIRM.UPDATEAPNSUCC');
      this.UPDATEMESHSUCC = this.translateService.instant('CONFIRM.UPDATEMESHSUCC');
      this.PLEASEFILL = this.translateService.instant('CONFIRM.PLEASEFILL');
      this.WARNING = this.translateService.instant('CONFIRM.WARNING');
      this.IMAGEOVERSIZE = this.translateService.instant('CONFIRM.IMAGEOVERSIZE');
      this.PARSINGFAILED = this.translateService.instant('PRODUCTS.PARSINGFAILED');
      this.FILETYPEERROR = this.translateService.instant('PRODUCTS.FILETYPEERROR');
      this.GROUPCREATESUCC = this.translateService.instant('CONFIRM.GROUPCREATESUCC');
      this.IMPORTTOGROUP = this.translateService.instant('CONFIRM.IMPORTTOGROUP');
      this.GNAMEEXIST = this.translateService.instant('CONFIRM.GNAMEEXIST');
      this.INVALIDFILETYPE = this.translateService.instant('COMMON.INVALIDFILETYPE');
    })
    this.permission.MultiPLMN = _userService.pageController('5GM', 'SL3-51')
    this.permission.MultiPLMNOneNSSAI = _userService.pageController('5GM', 'SL3-22')
    this.permission.MultiAMFIP = _userService.pageController('5GM', 'SL2-23')
  }

  public GROUPCREATESUCC = this.translateService.instant('CONFIRM.GROUPCREATESUCC');
  public IMPORTTOGROUP = this.translateService.instant('CONFIRM.IMPORTTOGROUP');
  public GNAMEEXIST = this.translateService.instant('CONFIRM.GNAMEEXIST');
  public notificationButtonAccess;
  public datacollectionButtonAccess;
  public get noPermission(): boolean {
    return !this.authService.check('device', 'productAdmin', 'write');
  }
  // Lifecycle Hooks
  // -----------------------------------------------------------------------------------------------------

  /**
   * On Init
   */
  filterByInput(event, name) {
    // Reset ng-select on search
    const val = event.target.value.toLowerCase();
    this.previousLabelFilter = name == 'slabelName' ? val : ""
    let tempData = this.filterRows(this.previousLabelFilter, this.previousSourceFilter);
    // Update The Rows
    this.summaryReportList = tempData;
    // Whenever The Filter Changes, Always Go Back To The First Page
    // this.table.offset = 0;
  }
  /**
 * filterInput
 * Filter data by user input from each column
 * @param event
 * @param columnName
 */
  filterBySource(event) {
    // Reset ng-select on search
    // this.selectedSeverity = [];

    const filter = event ? event.target.value : '';
    this.previousSourceFilter = filter;
    let tempData = this.filterRows(this.previousLabelFilter, filter);
    this.summaryReportList = tempData;
    // Whenever The Filter Changes, Always Go Back To The First Page
    // this.table.offset = 0;
  }

  /**
    * Filter Rows
    *
    * @param nameFilter
    * @param labelFilter
    * @param sourceFilter
    */
  filterRows(labelFilter, sourceFilter): any[] {
    // Reset search on select change
    labelFilter = labelFilter.toLowerCase();
    sourceFilter = sourceFilter.toLowerCase();
    return this.tempData.filter(row => {
      // const isNameMatch = (row.name && row.name.toLowerCase()).indexOf(nameFilter) > -1 || !nameFilter;
      const isLabelNameMatch = (row.labelName && row.labelName.toLowerCase()).indexOf(labelFilter) > -1 || !labelFilter;
      const isSourceMatch = row.source && row.source.toLowerCase() == sourceFilter || !sourceFilter;
      return isLabelNameMatch && isSourceMatch
    });
  }
  /**
   * **
   * Modern Horizontal Wizard Stepper Previous
   */
  modernHorizontalPrevious() {
    this.horizontalWizardStepper.previous();
  }

  /**
   * **
   * Modern Horizontal Wizard Stepper Next
   */
  modernHorizontalNext() {
    if (this.isEditModal == 'Edit') {
      if (this.checkParams(this.addData)) {
        return false
      }
    }
    this.horizontalWizardStepper.next();
  }
  /**
   * add serial Number
   */
  async productImgChange(event) {
    if (event.target.files && event.target.files[0]) {
      let file = event.target.files[0];
      if (!this._imageCompressionUploadService.isValidFileType(file, this.allowedFileTypes)) {
        this._toastrUtilsService.showWarningMessage(this.WARNING, `${this.INVALIDFILETYPE}${this.allowedFileTypes}!`);
        event.target.value = '';
        return;
      }
      if (file.type !== 'image/gif') {
        this._imageCompressionUploadService.compressionFile(file).then(res => {
          if (Math.min(file.size, res.size) > this.maxImgSize) {
            this._imageCompressionUploadService.compressionFile(file, 0.7).then(res => {
              this.saveAvatar(res);
            });
          } else {
            this.saveAvatar(file.size > res.size ? res : file);
          }
        });
      } else {
        this.saveAvatar(file);
      }
    }
  }

  saveAvatar(file) {
    if (file.size > this.maxImgSize) {
      this._toastrUtilsService.showErrorMessage(this.WARNING, this.IMAGEOVERSIZE);
      this.loading = false;
    } else {
      this._imageCompressionUploadService.fileToDataURL(file).then(res => {
        this.previewImg = res;
      })
    }
  }

  /**
   * add serial Number
   */
  addSN() {
    const { extraSno } = this
    this.sNoList.push({
      sn: extraSno
    })
    this.sNoList = this.sNoList.slice()
  }

  getSummaryDataRes(productId, protocol) {
    let deviceType = protocol == 'netconf' ? "netconf-generalType" : "generalType"
    this._productDataService.getServerDefaultSummaryReportList().pipe(untilDestroyed(this)).subscribe(data => {
      this.summaryReportList = data.filter(item => {
        return deviceType == item.deviceType
      })[0]["entities"].map(item => {
        item.isServer = true
        item.source = 'ServerDefault'
        item.collection = false
        return item
      })
      this._productDataService.getSummaryReportList(productId).then((res: any) => {
        if (res && res.entities && res.entities.length > 0) {
          this._summaryReportList = res.entities.map(item => {
            item.isServer = false;
            item.source = 'Product';
            item.collection = true
            return item
          });
        }
        this.summaryReportList = this._summaryReportList.concat(this.summaryReportList).map((item, index) => {
          item.index = index
          return item
        })
        this.tempData = this.summaryReportList
      });
    })

  }

  dataCollectChange(event, obj) {
    obj.dataCollect = event.target.checked;
    // if(this.serverDefaultMap[obj.referNode] && obj.dataCollect){
    //   this._summaryReportList.push(obj)
    // }
    // if(this.serverDefaultMap[obj.referNode] && !obj.dataCollect){
    //   this._summaryReportList = this._summaryReportList.filter(item=>{
    //     return item.referNode !== obj.referNode
    //   })
    // }
  }
  pmCollectChange(event, obj) {
    obj.pmCollect = event.target.checked;
  }

  delModel(obj) {
    this.tempData = this.tempData.filter(item => {
      return item.index != obj.index
    })
    this.summaryReportList = this.tempData
    this.searchMap = {
      sName: "",
      sProduct: "",
      sSource: "",
    }
    this._summaryReportList = this._summaryReportList.filter(item => {
      return item.index != obj.index
    })


  }


  saveSummaryReport(): Promise<any[]> {
    return new Promise((resolve, reject) => {
      let p = this._summaryReportList.map((item) => {
        if (item.name) {
          return {
            include: item.include,
            labelName: item.labelName,
            referNode: item.referNode,
            dataCollect: item.dataCollect,
            pmCollect: item.pmCollect,
            name: item.name
          }
        }
        return {
          include: item.include,
          labelName: item.labelName,
          referNode: item.referNode,
          dataCollect: item.dataCollect,
          pmCollect: item.pmCollect,
        }
      })
      let params = {
        entities: p
      }
      this._productDataService.saveSummaryReportList(this.addData.id, params).then((res: any) => {
        resolve(res)
      }).catch(err => {
        reject(err)
      });
    })
  }

  openAddSummaryModal(modalAddSummary, row: any = {}) {
    if (row.labelName) {
      this.modalAddSummaryStatus = 'Edit'
    } else {
      this.modalAddSummaryStatus = 'Add'
    }
    this.addSummaryData = Object.assign({}, row);

    this.modalService.open(modalAddSummary, {
      backdrop: 'static',
      size: 'lg',
    });
  }

  transFormDataCallback(e) {
    if (this.modalAddSummaryStatus == 'Add') {
      this.searchMap = {
        sName: "",
        sProduct: "",
        sSource: "",
      }
      this.currentSummary = e
      this.currentSummary.index = this.summaryReportList.length
      this.currentSummary.include = true
      this.currentSummary.dataCollect = false
      this.currentSummary.pmCollect = false
      this.currentSummary.isServer = false
      this.currentSummary.source = "Product"
      this.currentSummary.collection = true
      this._summaryReportList.unshift(this.currentSummary)
      this.tempData.unshift(this.currentSummary)
      this._summaryReportList = this._summaryReportList.slice()
      this.tempData = this.tempData.slice()
      this.summaryReportList = this.tempData
    } else {
      this.tempData = this.tempData.map((item) => {
        if (item.index == e.index) {
          item = { ...e }
        }
        return item
      })
      this.summaryReportList = this.tempData
      this._summaryReportList = this._summaryReportList.map((item) => {
        if (item.index == e.index) {
          item = { ...e }
        }
        return item
      })
    }
  }

  checkParams(obj) {
    this.formError["productName"] = this.st.check("name", obj.name)
    this.formError["OUI"] = this.st.check("OUI", obj.oui)
    this.formError["productClass"] = this.st.check("productClass", obj.model)
    this.formError["cpeLimit"] = this.st.check("cpeLimit", obj.cpeLimit)
    let errorMsg: string;
    for (let i in this.formError) {
      if (this.formError[i]) {
        errorMsg = `${i}:${this.formError[i]}`
        break
      }
    }
    if (!!errorMsg) {
      this._toastrUtilsService.showWarningMessage(this.FAIL, errorMsg);
    }
    return !!errorMsg
  }

  addProduct() {
    let product = this.addData.protocol == 'usp' ? this.addTopic(this.addData) : this.addData
    product.img = this.previewImg
    product.tags = this.tagArr.map(item => item.name)
    product.tags.forEach(item => {
      if (this.st.check("productTag", item) != null) {
        this.formError["tag"] = this.st.check("productTag", item.name)
      }
    })
    // console.log(product)

    if (this.checkParams(this.addData)) {
      return false
    }

    if (this.addData.protocol == 'usp') {
      Swal.fire({
        title: this.PLEASECONF,
        html: '<div style="text-align:left;">publish topic: devices/{Prod.Class}/notify</div><div style="text-align:left;">subscribe topic: usp/{Device.ID}</div>',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#7367F0',
        cancelButtonColor: '#E42728',
        confirmButtonText: this.translateService.instant('COMMON.YES'),
        cancelButtonText: this.translateService.instant('COMMON.CANCEL'),
        customClass: {
          confirmButton: 'btn btn-primary',
          cancelButton: 'btn btn-danger ml-1'
        }
      }).then((result) => {
        if (result.value) {
          this.loading = true
          if (this.addData.deploymentMode) {
            this._productDataService.addNetwork(product).then((res: any) => {
              this.httpResponseEvt.emit(true)

              if (this.addData.deploymentMode === 1) {
                this._toastrUtilsService.showSuccessMessage(this.SUCCESS, this.ADDRANSUCC)
              } else if (this.addData.deploymentMode === 2) {
                this._toastrUtilsService.showSuccessMessage(this.SUCCESS, this.ADDAPNSUCC)
              } else if (this.addData.deploymentMode === 3) {
                this._toastrUtilsService.showSuccessMessage(this.SUCCESS, this.ADDMESHSUCC)
              }

              this.updateAuthUser(this.addData.name)
              this.loading = false
              this.modalConfig.dismiss(this.CROSSCLICK)
            }).catch(err => {
              this.loading = false
              this._toastrUtilsService.showErrorMessage(this.FAIL, err.error)
            }).finally(() => {
            })
          } else {
            this._productDataService.addNewProduct(product).then((res: any) => {
              this.httpResponseEvt.emit(true)
              this._toastrUtilsService.showSuccessMessage(this.SUCCESS, this.ADDPRODSUCC)
              this.updateAuthUser(this.addData.name)
              this.loading = false
              this.modalConfig.dismiss(this.CROSSCLICK)
            }).catch(err => {
              this.loading = false
              this._toastrUtilsService.showErrorMessage(this.FAIL, err.error)
            }).finally(() => {
            })
          }

        }
      });
    } else {
      this.loading = true
      if (this.addData.deploymentMode) {
        this._productDataService.addNetwork(product).then((res: any) => {
          // console.log(product)
          this.httpResponseEvt.emit(true)
          if (this.addData.deploymentMode === 1) {
            this._toastrUtilsService.showSuccessMessage(this.SUCCESS, this.ADDRANSUCC)
          } else if (this.addData.deploymentMode === 2) {
            this._toastrUtilsService.showSuccessMessage(this.SUCCESS, this.ADDAPNSUCC)
          } else if (this.addData.deploymentMode === 3) {
            this._toastrUtilsService.showSuccessMessage(this.SUCCESS, this.ADDMESHSUCC)
          }

          this.updateAuthUser(this.addData.name)
          this.loading = false
          this.modalConfig.dismiss(this.CROSSCLICK)
        }).catch(err => {
          this._toastrUtilsService.showErrorMessage(this.FAIL, err.error)
          this.loading = false
        }).finally(() => {
        })
      } else {
        this._productDataService.addNewProduct(product).then((res: any) => {
          this.httpResponseEvt.emit(true)
          this._toastrUtilsService.showSuccessMessage(this.SUCCESS, this.ADDPRODSUCC)
          this.updateAuthUser(this.addData.name)
          this.loading = false
          this.modalConfig.dismiss(this.CROSSCLICK)
        }).catch(err => {
          this._toastrUtilsService.showErrorMessage(this.FAIL, err.error)
          this.loading = false
        }).finally(() => {
        })
      }
    }
  }

  updateAuthUser(name) {
    this.authService.updateProductName(name);
  }


  updateProduct(): Promise<any[]> {
    this.addData.tags = this.tagArr.map(item => item.name)
    this.addData.tags.forEach(item => {
      if (this.st.check("productTag", item) != null) {
        this.formError["tag"] = this.st.check("productTag", item.name)
      }
    })
    // console.log(this.addData)
    if (this.addData.deploymentMode) {
      return new Promise((resolve, reject) => {
        let params = {
          cpeLimit: this.addData.cpeLimit,
          img: this.previewImg,
          description: this.addData.description,
          type: this.addData.type,
          provisioningType: this.addData.provisioningType,
          initDefault: this.addData.initDefault,
          tags: this.addData.tags,
        }
        this._productDataService.updateSingleNetwork(this.addData.id, params).then((res: any) => {
          resolve(res)
        }).catch(err => {
          reject(err)
        })
      })
    } else {
      return new Promise((resolve, reject) => {
        let params = {
          cpeLimit: this.addData.cpeLimit,
          img: this.previewImg,
          description: this.addData.description,
          type: this.addData.type,
          provisioningType: this.addData.provisioningType,
          initDefault: this.addData.initDefault,
          tags: this.addData.tags,
        }
        this._productDataService.updateSingleProduct(this.addData.id, params).then((res: any) => {
          resolve(res)
        }).catch(err => {
          reject(err)
        })
      })
    }

  }

  publishTopicChange(value) {
    this.devicesPublish = value
  }

  subscribeTopicChange(value) {
    this.uspSubscribe = value
  }

  protocolChange({ value }) {
    if (value == 'netconf') {
      this.selectedDataModel = this.dataModelList.filter(item => item.name == 'YANG')
      this.addData.rootObject = this.selectedDataModel[0]["value"]
    } else {
      this.selectedDataModel = this.dataModelList.filter(item => item.name != 'YANG')
    }
    // this.addData.rootObject = this.selectedDataModel[0]["value"]
    let provisioningType = this.addData.provisioningType
    let protocol = this.addData.protocol
    if (this.ProvisioningTypeCheckService.isCell(provisioningType, protocol)) {
      let initDefault = this.getInitDefaultValue(this.initTemp[protocol]?.[provisioningType], this.initDefaults?.[protocol]?.[this.addData.provisioningType]?.value)
      this.addData.initDefault = initDefault
    } else {
      this.addData.initDefault = undefined
    }
  }

  addTopic(obj) {
    let publishTopic = ["devices"]
    let subscribeTopic = ["usp"]

    if (this.devicesPublish.length == 1) {
      if (this.devicesPublish[0].indexOf("OUI") > -1) {
        publishTopic.push(obj.oui)
      }
      if (this.devicesPublish[0].indexOf("Prod") > -1) {
        publishTopic.push(obj.model)
      }
    }

    if (this.uspSubscribe.length == 1) {
      if (this.uspSubscribe[0].indexOf("OUI") > -1) {
        subscribeTopic.push(obj.oui)
      }
      if (this.uspSubscribe[0].indexOf("Prod") > -1) {
        subscribeTopic.push(obj.model)
      }
    }

    if (this.devicesPublish.length == 2) {
      if (this.devicesPublish[0].indexOf("OUI") > -1) {
        publishTopic.push(obj.oui)
      }
      if (this.devicesPublish[0].indexOf("Prod") > -1) {
        publishTopic.push(obj.model)
      }
      if (this.devicesPublish[1].indexOf("OUI") > -1) {
        publishTopic.push(obj.oui)
      }
      if (this.devicesPublish[1].indexOf("Prod") > -1) {
        publishTopic.push(obj.model)
      }
    }

    if (this.uspSubscribe.length == 2) {
      if (this.uspSubscribe[0].indexOf("OUI") > -1) {
        subscribeTopic.push(obj.oui)
      }
      if (this.uspSubscribe[0].indexOf("Prod") > -1) {
        subscribeTopic.push(obj.model)
      }
      if (this.uspSubscribe[1].indexOf("OUI") > -1) {
        subscribeTopic.push(obj.oui)
      }
      if (this.uspSubscribe[1].indexOf("Prod") > -1) {
        subscribeTopic.push(obj.model)
      }
    }

    obj.publishTopic = publishTopic.length == 1 ? '' : publishTopic.join("/")
    obj.subscribeTopic = subscribeTopic.length == 1 ? '' : subscribeTopic.join("/")
    return obj
  }


  saveUpdate() {
    this.loading = true
    Promise.all([this.updateProduct(), this.saveSummaryReport()]).then(() => {
      if (this.addData.deploymentMode === 1) {
        this._toastrUtilsService.showSuccessMessage(this.SUCCESS, this.UPDATERANSUCC)
      } else if (this.addData.deploymentMode === 2) {
        this._toastrUtilsService.showSuccessMessage(this.SUCCESS, this.UPDATEAPNSUCC)
      } else if (this.addData.deploymentMode === 3) {
        this._toastrUtilsService.showSuccessMessage(this.SUCCESS, this.UPDATEMESHSUCC)
      } else {
        this._toastrUtilsService.showSuccessMessage(this.SUCCESS, this.UPDATEPRODSUCC)
      }

      this.httpResponseEvt.emit(true)
      this.loading = false
      this.modalConfig.dismiss(this.CROSSCLICK)
    }).catch(err => {
      this._toastrUtilsService.showErrorMessage(this.FAIL, err.error)
      this.loading = false
    })
    // this.updateProduct()
    // this.saveSummaryReport()
    // this.httpResponseEvt.emit(true)
  }

  addBtnStatus() {
    let { name, oui, model, rootObject, cpeLimit, type, protocol } = this.addData
    return name && oui && model && rootObject && type && cpeLimit && protocol != undefined
  }

  disableAlert() {
    Swal.fire({
      title: this.PLEASEFILL,
      html: '',
      icon: 'warning',
      confirmButtonColor: '#7367F0',
      cancelButtonColor: '#E42728',
      confirmButtonText: this.translateService.instant('COMMON.YES'),
      customClass: {
        confirmButton: 'btn btn-primary'
      }
    })
  }

  resetPic() {
    this.previewImg = this.showDefefaultImg()
    this.selectedImageFile.nativeElement.value = this.showDefefaultImg()
  }

  showDefefaultImg() {
    if (this.setSmallCellDefaultImage.includes(this.addData.provisioningType)) {
      return this.smallCellImage
    } else if (this.Wifi_AP_List.includes(this.addData.provisioningType)) {
      return this.wifiApImage
    }
    return this.defaultImage
  }

  getProtocolLicense() {
    const protocolMap = {
      'cwmp': 'CWMP (TR-069)',
      'usp': 'USP (TR-369)',
      'netconf': 'NETCONF'
    };
    this._userService.getPageAuthority('protocol').then((data: any) => {
      this.protocolOption = data.map((item) => {
        return {
          name: protocolMap[item],
          value: item
        }
      }).filter(item => Object.keys(protocolMap).includes(item.value))

      this.addData.protocol = !!this.addData.protocol ? this.addData.protocol : this.protocolOption.length >= 1 ? this.protocolOption[0].value : null
      // this.addData.rootObject = this.selectedDataModel[0]["value"]
    })
  }

  openSelectImg(modalImg) {
    this.modalService.open(modalImg, {
      backdrop: 'static',
      size: 'lg',
      modalDialogClass: 'modal-custom',
      scrollable: true
    });
  }

  selectImgObjectEvtCallback(e) {
    this.previewImg = e.image
  }
  addAlarm(row, modal) {
    this.productSummaryReport = {
      productData: this.addData,
      summaryReport: row
    }
    this.modalService.open(modal, {
      backdrop: false,
      size: 'lg',
      modalDialogClass: 'modal-custom modal-stepper',
      scrollable: true
    });
  }
  dismiss(event, modal) {
    modal.dismiss(event);
  }

  stepNavigate(ADDPRODUCTValidationForm?) {
    const index = ADDPRODUCTValidationForm ? 2 : 1;
    if (index === 1) {
      this.horizontalWizardStepper.previous()
    }
    if (index === 2) {
      if (this.checkParams(this.addData)) {
        return false
      }
      this.horizontalWizardStepper.next();
    }
  }

  inputingChange(obj, attr) {
    this[attr] = obj.text
    this.addData[attr] = obj.text
    this.formError[attr] = obj.valid ? null : obj.errorMsg
  }

  public newTag: any = ''
  public tagArr: any = [];
  public FAILRegister
  addTag(newTag) {
    this.tagArr = this.tagArr.map((item, index) => {
      return {
        index: index,
        name: item.name
      }
    })
    if (this.tagArr.some(item => item.name === this.newTag)) {
      return this._toastrUtilsService.showErrorMessage(this.FAILRegister, this.translateService.instant('DEVICES.EXISTTAG'));
    }
    this.tagArr.push({ index: this.tagArr.length, name: newTag })
    this.newTag = ''
  }

  deleteTag(item) {
    this.tagArr.splice(item.index, 1)
    this.tagArr = this.tagArr.map((item, index) => {
      return {
        name: item.name,
        index: index
      }
    });
  }

  provisioningTypeChange() {
    this.previewImg = this.addData.img ? this.addData.img : this.showDefefaultImg()
    // this.addData.description = this.addData.provisioningType
    let provisioningType = this.addData.provisioningType
    let protocol = this.addData.protocol
    if (this.ProvisioningTypeCheckService.isCell(provisioningType, protocol)) {
      let initDefault = this.getInitDefaultValue(this.initTemp[protocol]?.[provisioningType], this.initDefaults?.[protocol]?.[this.addData.provisioningType]?.value)
      this.addData.initDefault = initDefault
    } else {
      this.addData.initDefault = undefined
    }
  }

  showInitDefault(provisioningType) {
    //  console.log(this.ProvisioningTypeCheckService.isCell(provisioningType, this.addData.protocol))
    // console.log(this.addData.protocol)
    return this.ProvisioningTypeCheckService.isCell(provisioningType, this.addData.protocol)
  }

  getInitDefaultValue(productValue, defaultValue) {
    let res = {}
    if (defaultValue == null) {
      return res
    }
    if (productValue == null) {
      return Object.assign({}, defaultValue)
    }
    for (let key in defaultValue) {
      if (productValue.hasOwnProperty(key)) {
        res[key] = productValue[key]
      } else {
        res[key] = defaultValue[key]
      }
    }
    return res
  }

  openModal(modal) {
    // console.log(this.addData)
    let provisioningType = this.addData.provisioningType
    let protocol = this.addData.protocol
    if (this.ProvisioningTypeCheckService.isCell(provisioningType, protocol)) {
      let defaultObj = this.initDefaults?.[protocol]?.[provisioningType]
      let initDefault = this.getInitDefaultValue(this.addData.initDefault, defaultObj?.value)
      this._initProvisionService.init(protocol, provisioningType, initDefault, defaultObj?.value, defaultObj?.path, this.permission);
      this.modalService.open(modal, {
        backdrop: 'static',
        size: 'lg',
        modalDialogClass: 'modal-stepper',
        scrollable: true
      });
    }
  }

  saveInitDefault(modal) {
    let hasErr = this._initProvisionService.checkErrorMsg();
    if (hasErr) {
      return this.showErrAlert();
    }
    modal.dismiss('Cross click')
    this.addData.initDefault = this._initProvisionService.getValueObject(1)
    if (!this.initTemp[this.addData.protocol]) {
      this.initTemp[this.addData.protocol] = {}
    }
    this.initTemp[this.addData.protocol][this.addData.provisioningType] = this.addData.initDefault
  }

  showErrAlert() {
    Swal.fire({
      title: this.translateService.instant('PROVISIONING.INVALID_VALUE_MESSAGE'),
      html: '',
      icon: 'warning',
      confirmButtonColor: '#7367F0',
      cancelButtonColor: '#E42728',
      confirmButtonText: this.translateService.instant('COMMON.OK'),
      customClass: {
        confirmButton: 'btn btn-primary'
      }
    })
  }

  refresh() {
    Swal.fire({
      title: this.translateService.instant('PROVISIONING.RESET_CONFIRM'),
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: this.translateService.instant('COMMON.OK'),
      cancelButtonText: this.translateService.instant('COMMON.CANCEL'),
      customClass: {
        confirmButton: 'btn btn-primary',
        cancelButton: 'btn btn-danger ml-1'
      }
    }).then((result) => {
      if (result.value) {
        this._initProvisionService.reset()
      }
    });
  }

  uploadDefault(event: any) {
    if (event.target.files && event.target.files[0]) {
      let file = event.target.files[0];
      let name = file.name.toLowerCase()
      let reader = new FileReader();
      reader.onload = (e: any) => {
        if (name.endsWith('xlsx') || name.endsWith('.xls')) {
          this.xlsxLoaderService.loadXLSX().then(XLSX => {
            var workbook = XLSX.read(e.target.result, { type: "binary" });
            let data = [];
            // 遍历每张工作表进行读取（这里默认只读取第一张表）
            for (const sheet in workbook.Sheets) {
              // esline-disable-next-line
              if (workbook.Sheets.hasOwnProperty(sheet)) {
                // 利用 sheet_to_json 方法将 excel 转成 json 数据
                data = data.concat(XLSX.utils.sheet_to_json(workbook.Sheets[sheet]));
                break; // 如果只取第一张表，就取消注释这行
              }
            }
            this._initProvisionService.setValueObject(data[0])
          }).catch(error => {
            this._toastrUtilsService.showErrorMessage(this.FAIL, this.translateService.instant('PRODUCTS.PARSINGFAILED'))
          })
        } else {
          try {
            let data = csv2jsonArray(e.target.result.replace(/\t/g, ''), true)
            this._initProvisionService.setValueObject(data[0])
          } catch (error) {
            this._toastrUtilsService.showErrorMessage(this.FAIL, this.translateService.instant('PRODUCTS.PARSINGFAILED'))
          }
        }
      };
      if (name.endsWith('.xlsx') || name.endsWith('.xls')) {
        reader.readAsArrayBuffer(file)
      } else if (name.endsWith('.csv')) {
        reader.readAsText(file);
      } else {
        return this._toastrUtilsService.showErrorMessage(this.FAIL, this.translateService.instant('PRODUCTS.FILETYPEERROR'))
      }
    }
  }

  formatCSVValue(value) {
    if (typeof value === 'object') {
      if (typeof value[0] === 'object') {
        let res = '"'
        for (let i = 0, l = value.length; i < l; i++) {
            let item = value[i]
            res += '[' + item.toString() + ']'
            if (i !== l - 1) {
                res += ','
            }
        }
        res += '"'
        return res
      } else {
        let s = value.toString()
        if (this.numReg.test(s)) {
          return `"\t${s}"`
        } else {
          return `"${s}"`
        }
      }
    } else if (typeof value === 'string' && this.numReg.test(value) || (typeof value === 'number' && value > 999999999)) {
      if (typeof value === 'string' && value.includes(',')) {
        return `"\t${value}"`
      }
      return `\t${value}`
    } else if (typeof value === 'string') {
      if (value.includes(',')) {
        return `"${value}"`
      }
    }
    return value
  }

  downloadDefault(type) {
    let data = this._initProvisionService.getValueObject(2)
    if (type === 'json') {
      this._fileDownloadService.generateLocalFile(data, this.addData.name + '-init-default.json')
    } else if (type === 'csv') {
      let line1 = "Serial,Product,Label,Condition-Software Version";
      let line2 = `,${this.formatCSVValue(this.addData.name)},,`;
      for (let key in data) {
        line1 += `,${key}`
        line2 += ',' + this.formatCSVValue(data[key])
      }
      let res = line1 + '\n' + line2
      this._fileDownloadService.generateFile(res, this.addData.name + '-register-example.csv')
    } else if (type === 'xlsx') {
      let new_data = {
        Serial: '',
        Product: this.addData.name,
        Label: '',
        "Condition-Software Version": ""
      }
      for (let key in data) {
        let value = data[key]
        if (Array.isArray(value)) {
          if (Array.isArray(value[0])) {
            value = value.map(item => '[' + item.toString() + ']')
          }
          new_data[key] = value.toString()
        } else {
          new_data[key] = value
        }
      }
      this._fileDownloadService.exportToExcel([new_data], this.addData.name + '-register-example')
    }
  }

  getProductImage(value: string) {
    this._productDataService.getProductImage(value.substring(9)).then((res: any) => {
      this.previewImg = res.data
    }, err => { })
  }

  /**
   * init
   */
  ngOnInit() {
    // console.log(this.addData)
    if (this.addData.deploymentMode == 1) {
      this.provisioningTypeList = SmallCell_List
      this.previewImg = this.smallCellImage
    } else if (this.addData.deploymentMode == 2) {
      this.provisioningTypeList = Enterprise_Wifi_List
      this.previewImg = this.wifiApImage
    } else if (this.addData.deploymentMode == 3) {
      this.provisioningTypeList = Mesh_Wifi_Router_List
    } else {
      this.provisioningTypeList = provisioningTypeList
    }
    this.addProductForm = document.querySelector('#AddProductFrom')
    this.horizontalWizardStepper = new Stepper(this.addProductForm, {});
    if (this.isEditModal == 'Edit') {
      this.getSummaryDataRes(this.addData.id, this.addData.protocol)
      this.previewImg = this.addData.img ? this.addData.img : this.showDefefaultImg()
      if (this.addData.img) {
        if (this.addData.img.startsWith('database')) {
          this.getProductImage(this.addData.img)
        } else {
          this.previewImg = this.addData.img
        }
      } else {
        this.previewImg = this.showDefefaultImg()
      }
      this.initTemp[this.addData.protocol] = {
        [this.addData.provisioningType]: this.addData.initDefault
      }
    }
    this.addData.description = this.addData.description ? this.addData.description : ''
    this.getProtocolLicense()
    this.protocolChange({ value: this.addData.protocol })
    this.notificationButtonAccess = this._userService.canActivateRoute("EventsNotificationComponent")
    this.datacollectionButtonAccess = this._userService.canActivateRoute("DataCollection")
    fromEvent(this.addProductForm, 'show.bs-stepper').pipe(untilDestroyed(this)).subscribe((event: any) => {
      this.stepperIndex = event.detail.indexStep;
    })

    if (this.addData?.tags) {
      this.tagArr = this.addData.tags.map((item, index) => {
        return {
          index: index,
          name: item
        }
      })
      // console.log(this.tagArr)
    } else {
      this.tagArr = []
    }

  }

  ngOnDestroy() {
    this.horizontalWizardStepper.destroy();
  }


}


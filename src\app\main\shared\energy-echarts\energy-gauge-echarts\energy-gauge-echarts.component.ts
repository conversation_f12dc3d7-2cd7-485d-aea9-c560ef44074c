import { Component, OnInit, Input, AfterView<PERSON>nit, OnDestroy, NgZone } from '@angular/core';
import { lightTheme } from '../theme/lightTheme';
import * as echarts from 'echarts/core';
import { EchartsService } from 'app/main/shared/echarts/echarts.service';
import { FileDownloadService } from 'app/main/commonService/file-download.service';
import { chartColorArr } from 'app/main/shared/echarts/echart-color'
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import { GridSystemService } from 'app/main/commonService/grid-system.service';
import { ActivatedRoute } from '@angular/router';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
@UntilDestroy()
@Component({
  selector: 'app-energy-gauge-echarts',
  // standalone: true,
  // imports: [],
  templateUrl: './energy-gauge-echarts.component.html',
  styleUrl: './energy-gauge-echarts.component.scss'
})
export class EnergyGaugeEchartsComponent extends Unsubscribe implements OnInit {
  @Input() title: any;
  @Input() total: any;
  @Input() chartRef: any;
  @Input() chartData: any;
  @Input() unit: any;
  @Input() maxValue: any;
  @Input() styleType: any;
  public colorArr = chartColorArr
  public personalTheme: any

  constructor(
    private _echartsService: EchartsService,
    private _fileDownloadService: FileDownloadService,
    private _ngZone: NgZone,
    private route: ActivatedRoute,
    private gridService: GridSystemService,
  ) {
    super();
    this.customSubscribe(route.data, res => {
      this.personalTheme = res.pts?.personalTheme?.config?.layout?.skin || 'dark'
    });
  }

  public options: any;
  public showed = false
  public theme: any = lightTheme
  public chartHeight: any;

  public echartsInstance: echarts.ECharts;
  onChartInit(ec) {
    this.echartsInstance = ec;
    const echartsDom = this.chartRef.querySelector('[echarts]');
    if (echartsDom) {
      echartsDom.style.height = this.chartRef.offsetHeight + 'px';
      // console.log(echartsDom.style.height)
    }
    this.echartsInstance.resize()
    // this.echartsInstance.resize({ height: this.chartRef.offsetHeight })
  }

  public initOpts = {
    width: 'auto',
    height: 'auto',
  };

  initOption(chartData, maxValue, personalTheme, unit?, styleType?) {
    // console.log(personalTheme)
    // console.log(chartData)
    let ValuePercent = chartData ? ((chartData[0]?.value) / maxValue * 100).toFixed(2) : ""
    this.options = {
      animation: false,
      color: chartData?.[0]?.name=="Save Energy" ? this.colorArr[1]:this.colorArr,
      grid: {
        left: '5%',
        right: '5%',
        top: '5%',
        bottom: '5%',
      },
      tooltip: styleType === 'fancy' ? undefined : {
        trigger: 'item',
        backgroundColor: personalTheme == 'dark' ? '#161D31' : '#F2F2F2',
        // appendToBody: true,
        extraCssText: 'opacity: 0.8',
        borderColor: personalTheme == 'dark' ? "rgb(115, 131, 152);" : 'lightgray',
        textStyle: {
          color: personalTheme == 'dark' ? 'lightgray' : '#5e5873',
          fontSize: 12
        },
        padding: [5, 5],
        appendToBody: true,
        formatter: function (param) {
          // console.log(param)
          let paramName = param.name;
          let paramValue = null;
          chartData.forEach(dataItem => {
            if (dataItem.name === paramName) {
              paramValue = dataItem.value;
              if (dataItem.formattedValue) {
                paramValue = dataItem.formattedValue;
              }
            }
          });
          if (param.formattedValue) {
            return `${param.marker}${param.name} : ${param.formattedValue} (${ValuePercent}%)`;
          } else {
            return param.marker + paramName + ': ' + paramValue + unit + `(${ValuePercent}%)`;
          }
        },
      },
      // ...(styleType === 'fancy' ? {
      //   axisLine: {
      //     lineStyle: {
      //       width: 30,
      //       color: [
      //         [0, '#67e0e3'],
      //         [0.9, '#37a2da'],
      //         [1, '#fd666d']
      //       ]
      //     }
      //   },
      //   pointer: { itemStyle: { color: 'auto' } },
      //   axisTick: { distance: -30, length: 8, lineStyle: { color: '#fff', width: 2 } },
      //   splitLine: { distance: -30, length: 30, lineStyle: { color: '#fff', width: 4 } },
      //   axisLabel: { color: 'inherit', distance: 40, fontSize: 20 }
      // }
      //   : {}
      // ),
      series: [
        {
          type: 'gauge',
          radius:styleType === 'fancy' ? '145%':'100%',
          startAngle: styleType === 'fancy' ? 190 : 230,
          endAngle: styleType === 'fancy' ? -10 : 310,
          center: styleType === 'fancy' ?['50%', '75%']:['50%', '50%'],
          min: 0,
          max: maxValue,
          pointer: styleType === 'fancy'
            ? { itemStyle: { color: 'auto' }, width : 4 }
            : { show: false },
          progress: styleType === 'fancy'
            ? undefined
            : { show: true, overlap: false, roundCap: true, clip: false },
          axisLine: styleType === 'fancy'
            ? {
              lineStyle: {
                width: 12,
                color: [
                  [0, '#9DA0AA'],
                  [0.5, '#63e6be'],
                  [0.8, '#ffd43b'],
                  [1, '#fd666d']
                ]
              }
            }
            : {
              roundCap: true,
              lineStyle: { width: 12 }
            },
          axisTick: styleType === 'fancy'
            ? {
              distance: -10,
              length: 3,
              lineStyle: { color: '#fff', width: 1 }
            }
            : { show: false },
          splitLine: styleType === 'fancy'
            ? {
              distance: -10,
              length: 10,
              lineStyle: { color: '#fff', width: 2 }
            }
            : { show: false, distance: 5, length: 6 },
          axisLabel: styleType === 'fancy'
            ? { color: 'inherit', distance: 18, fontSize: 12 }
            : { show: false, distance: 10, color: personalTheme === 'dark' ? 'lightgray' : '#5e5873', fontSize: 10 },
          data: [
            {
              value: chartData?.[0]?.value ?? 0,
              name: chartData?.[0]?.name ?? '',
              ...(styleType === 'fancy'
                ? {
                  title: {
                    show: false,
                    offsetCenter: [0, 24],
                    color: 'inherit',
                    fontSize: 12
                  },
                  detail: {
                    offsetCenter: [0, 16],
                    formatter: '{value} W',
                    color: 'inherit',
                    fontSize: 16
                  }
                }
                : {
                  title: {
                    show: true,
                    offsetCenter: [0, '-20%'],
                    color: personalTheme === 'dark' ? 'lightgray' : '#5e5873',
                    fontSize: 14
                  },
                  detail: {
                    fontSize: 24,
                    offsetCenter: [0, '10%'],
                    color: 'inherit',
                    formatter: '{value} Wh'
                  }
                })
            }
          ]
        }
      ]
    };
  }


  download(chartData) {
    let structList = chartData;
    let data = [['Name', 'Value']];
    structList.map(item => {
      var _structInfoArry = [];
      _structInfoArry.push(item.name.toString());
      _structInfoArry.push(item.value.toString());
      return _structInfoArry;
    }).forEach(struct => {
      data.push(struct);
    });
    this._fileDownloadService.generateFileToXLSX({
      data,
      name: this.title
    })
  }

  private resizeObserver!: ResizeObserver;

  ngAfterViewInit() {
    // 使用 ResizeObserver 監聽容器大小變化
    this.resizeObserver = new ResizeObserver(() => {
      this.resizeChart();
    });
    this.resizeObserver.observe(this.chartRef);
    this.initOption(this.chartData,this.maxValue,this.personalTheme,this.unit)
  }

  ngOnChanges(): void {
    this.initOption(this.chartData, this.maxValue, this.personalTheme, this.unit, this.styleType)
  }

  private resizeChart() {
    if (this.echartsInstance) {
      // 調整圖表大小
      this.echartsInstance.resize({ height: this.chartRef.offsetHeight })
    }
  }

  ngOnDestroy(): void {
    // 清理 ResizeObserver
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }

    this.echartsInstance = echarts.init(this.chartRef, {
      logLevel: 'silent'
    });
    this._ngZone.run(() => {
      if (this.echartsInstance) {
        this.echartsInstance.dispose();
        this.echartsInstance = null
      }
    });
  }


  ngOnInit(): void {
    this.gridService.personalTheme$.pipe(untilDestroyed(this)).subscribe(theme => {
      if (theme) {
        this.customSubscribe(this.route.data, res => {
          this.personalTheme = res.pts?.personalTheme?.config?.layout?.skin || 'dark'
          // console.log(this.personalTheme)
          this.initOption(this.chartData, this.maxValue, this.personalTheme, this.unit, this.styleType)
        });
      }
    });
  }

}

import { Component, OnInit, Input, EventEmitter, Output, ViewEncapsulation, ViewChild, OnDestroy, ChangeDetectorRef, ElementRef, AfterViewChecked } from '@angular/core';
import { ColumnMode, DatatableComponent, SelectionType } from '@almaobservatory/ngx-datatable';
import { NgbDateStruct, NgbTimeStruct, NgbDate } from '@ng-bootstrap/ng-bootstrap';
import { DragulaService } from 'ng2-dragula';
import Stepper from 'bs-stepper';
import moment from 'moment'
import isEmpty from 'lodash/isEmpty';
import cloneDeep from 'lodash/cloneDeep';
import isNumber from 'lodash/isNumber';
import { ToastrUtilsService } from 'app/main/commonService/toastr-utils.service';
import { SwalUtilsService } from 'app/main/commonService/swal-utils.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { AuthenticationService } from 'app/auth/service/authentication.service';
import { UserService } from 'app/auth/service/user.service';
import { WorkFlowsService } from 'app/main/provisioning/work-flows/work-flows.service';
import { OperationsService } from 'app/main/provisioning/operations/operations.service';
import { ProductDataService } from 'app/main/products/products.service';
import { GroupListService } from 'app/main/groups/group-list/group-list.service';
import { TranslateService } from '@ngx-translate/core';
import { fromEvent, forkJoin } from 'rxjs';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { cwmpSelectRpc, uspSelectRpc, cwmpComponentMap, uspComponentMap, netconfComponentMap, netconfSelectRpc } from 'app/main/provisioning/operations/components/utils';
import { SanityTestService } from 'app/main/commonService/sanity-test.service';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';

@UntilDestroy()
@Component({
  selector: 'app-work-flow-edit-form',
  templateUrl: './work-flow-edit-form.component.html',
  styleUrls: ['./work-flow-edit-form.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class WorkFlowEditFormComponent implements OnInit, OnDestroy, AfterViewChecked {
  @ViewChild('stepperModal') private stepperModal: ElementRef;
  @ViewChild('stepperHeader') private stepperHeader: ElementRef;
  @Output() dismissEvt = new EventEmitter<string>();
  @Output() refreshEvt = new EventEmitter<string>();
  @Input() editData: any;
  @Input() mode: any;
  @Input() displayType: 'workflow' | 'configuration' = 'workflow';
  // public
  public selectedOption = 5;
  public ColumnMode = ColumnMode;
  public DELIMITER = '/';
  public productData = [];
  public productList = [];
  // Basic Date Picker
  public startDatedata: NgbDateStruct;
  public endDatedata: NgbDateStruct;
  // Basic Time Picker
  public startTime: NgbTimeStruct;
  public endTime: NgbTimeStruct;
  // public selectedType;
  public typeList = [];
  // public selectedProduct;
  public groupsFullData;
  public filterGroupsDataByProtocol;
  public selectedGroup;
  public datatimeState = false;
  public weekState = false;
  public everyDay = false;
  public weekList = [
    { name: 'Sunday', value: false, number: 7, translation: 'PROVISIONING.SUNDAY' },
    { name: 'Monday', value: false, number: 1, translation: 'PROVISIONING.MONDAY' },
    { name: 'Tuesday', value: false, number: 2, translation: 'PROVISIONING.TUESDAY' },
    { name: 'Wednesday', value: false, number: 3, translation: 'PROVISIONING.WEDNESDAY' },
    { name: 'Thursday', value: false, number: 4, translation: 'PROVISIONING.THURSDAY' },
    { name: 'Friday', value: false, number: 5, translation: 'PROVISIONING.FRIDAY' },
    { name: 'Saturday', value: false, number: 6, translation: 'PROVISIONING.SATURDAY' },
  ];
  public get isUspConfiguration(): boolean {
    return this.editData.type == 'usp';
  };
  // public operations: any = {};
  public newStage: any;
  public id = 0;
  public rpcId = 0;
  public configurationOperationRequestRpc = [];
  public rpcToAdd = '';
  public selectedRpc: any = {};
  public selectedStage: any = {};
  public loading = false;
  public stepperIndex = 0;
  public stageData: any;

  public selectYangList = [];
  public selectRpcselected: any;
  public selectRpc = [];
  public options: any;
  public componentMap: any;
  public netconfOptions = {
    yangRpc: {},
    namespace: '',
    parentNodeList: [],
    childNodeList: {},
    nodeList: [],
    moduleName: '',
    yangNamespace: ''
  };
  public yangModuleStatus = false;
  public operationLoading = false;
  public autoFillData = {};
  public moduleData = {};
  public newTag: string = '';
  public tagArr: any[] = [];
  public formError: any = {};
  // private
  private modernWizardStepper: Stepper;
  private workflowEditStepper: any;

  public groupAccess;
  public configurationData: any;
  public get noPermission(): boolean {
    return !this._authenticationService.check('provisioning', 'workflowSetup', 'write');
  }
  public modeList = [
    { value: 0, name: 'Passive' },
    { value: 1, name: 'Active' }
  ]

  public booleanList = [
    { value: false, name: false },
    { value: true, name: true }
  ]

  @ViewChild('acc') acc: any;
  @ViewChild('accRpc') accRpc: any;
  @ViewChild('tableRowDetails') tableRowDetails: any;
  @ViewChild('priorityElement') priorityElement: ElementRef;
  public WORKFLOW = this.translateService.instant('CONFIRM.WORKFLOW');
  public CONFIGURATION = this.translateService.instant('CONFIRM.CONFIGURATION');
  public ENDGREATER = this.translateService.instant('CONFIRM.ENDGREATER');
  public STARTHAVEVALUE = this.translateService.instant('CONFIRM.STARTHAVEVALUE');
  public ENDGREATERSTART = this.translateService.instant('CONFIRM.ENDGREATERSTART');
  public STARTENDALUE = this.translateService.instant('CONFIRM.STARTENDALUE');
  public EXACTLYEQUAL = this.translateService.instant('CONFIRM.EXACTLYEQUAL');
  public ADDFAIL = this.translateService.instant('CONFIRM.ADDFAIL');
  public NAMEEXIST = this.translateService.instant('CONFIRM.NAMEEXIST');
  public DOGROUP = this.translateService.instant('CONFIRM.DOGROUP');
  public BADREQ = this.translateService.instant('CONFIRM.BADREQ');
  public PARAMNEED = this.translateService.instant('CONFIRM.PARAMNEED');
  public FLOWSUCC = this.translateService.instant('CONFIRM.FLOWSUCC');
  public CONFIGURATIONSUCC = this.translateService.instant('CONFIRM.CONFIGURATIONSUCC');
  public FLOWID = this.translateService.instant('CONFIRM.FLOWID');
  public CONFIGURATIONID = this.translateService.instant('CONFIRM.CONFIGURATIONID');
  public WASUPDATE = this.translateService.instant('CONFIRM.WASUPDATE');
  public CROSSCLICK = this.translateService.instant('CONFIRM.CROSSCLICK');
  public FLOWADDSUCC = this.translateService.instant('CONFIRM.FLOWADDSUCC');
  public WASADD = this.translateService.instant('CONFIRM.WASADD');
  public FORMFAIL = this.translateService.instant('CONFIRM.FORMFAIL');
  public CONFSTAGE = this.translateService.instant('CONFIRM.CONFSTAGE');
  public CONFOPER = this.translateService.instant('CONFIRM.CONFOPER');
  public STAGE = this.translateService.instant('CONFIRM.STAGE');
  public CONFACTIONRE = this.translateService.instant('CONFIRM.CONFACTIONRE');
  public OPERATION = this.translateService.instant('CONFIRM.OPERATION');

  constructor(
    private _authenticationService: AuthenticationService,
    private _userService: UserService,
    private _workFlowsService: WorkFlowsService,
    private _operationsService: OperationsService,
    private _groupListService: GroupListService,
    private _productDataService: ProductDataService,
    private _toastrUtilsService: ToastrUtilsService,
    private _swalUtilsService: SwalUtilsService,
    private modalService: NgbModal,
    private translateService: TranslateService,
    private st: SanityTestService,
    private dragulaService: DragulaService,
    private cdr: ChangeDetectorRef
  ) { }
  /**
   * Modern Horizontal Wizard Stepper Next
   */
  modernHorizontalNext(data, form) {
    if (this.editData.readOnly) {
      if (this.displayType === 'workflow') {
        this.modernWizardStepper.next();
      } else {
        this.modernWizardStepper.to(3);
      }
      if (form === 'scheduleForm' && this.editData.type === 'netconf') {
        this.getYangModuleListRes();
      }
    } else {
      if (data.controls.modernStartDate && this.checkSchedule()) return;
      data.onSubmit()
      if (data.form.valid === true) {
        if (form === 'scheduleForm' && this.editData.type === 'netconf') this.getYangModuleListRes();
        if (this.displayType === 'workflow') {
          this.modernWizardStepper.next();
        } else {
          this.modernWizardStepper.to(3);
        }
      }
    }
  }
  /**
   * Modern Horizontal Wizard Stepper Previous
   */
  modernHorizontalPrevious() {
    if (this.displayType === 'workflow') {
      this.modernWizardStepper.previous();
    } else {
      this.modernWizardStepper.to(1);
    }
  }
  stepNavigate(detailsForm?, scheduleForm?) {
    const index = scheduleForm ? 3 : (detailsForm && !scheduleForm) ? 2 : 1;
    // console.log(index)
    if (this.editData.readOnly) {
      this.modernWizardStepper.to(index);
      if (index === 3 && this.editData.type === 'netconf') {
        this.getYangModuleListRes();
      }
    } else {
      if (detailsForm && scheduleForm) {
        detailsForm.onSubmit()
        if (detailsForm.form.valid === true) {
          if (scheduleForm.controls.modernStartDate && this.checkSchedule()) {
            this.modernWizardStepper.to(2);
          } else {
            scheduleForm.onSubmit()
            if (scheduleForm.form.valid === true) {
              if (this.editData.type === 'netconf') this.getYangModuleListRes();
              this.modernWizardStepper.to(3);
            } else {
              this.modernWizardStepper.to(2);
            }
          }
        }
      } else if (detailsForm) {
        detailsForm.onSubmit()
        if (detailsForm.form.valid === true) {
          this.modernWizardStepper.to(2);
        }
      } else {
        this.modernWizardStepper.to(1);
      }
    }
  }

  changeTouchspin(value, item) {
    this.editData[item] = null;
    setTimeout(() => {
      this.editData[item] = +value || 0;
      if (this.editData.priority > 100) {
        this.editData.priority = 100;
      }
    }, 0)
  }
  checkSchedule() {
    const name = this.editData.name;
    const startDatedata = NgbDate.from(this.startDatedata);
    const endDatedata = NgbDate.from(this.endDatedata);
    const todayDatedata = NgbDate.from({
      year: moment().year(),
      month: moment().month() + 1,
      day: moment().date(),
    });
    const windowStart = this.startTime && this.startTime.hour.toString().padStart(2, '0');
    const windowStartMinute = this.startTime && this.startTime.minute.toString().padStart(2, '0');
    const windowEnd = this.endTime && this.endTime.hour.toString().padStart(2, '0');
    const windowEndMinute = this.endTime && this.endTime.minute.toString().padStart(2, '0');
    let title = (this.displayType === 'workflow' ? this.WORKFLOW : this.CONFIGURATION) + ` ${name}`
    // if (endDatedata && endDatedata.before(todayDatedata)) {
    //   this._toastrUtilsService.showErrorMessage(title, this.ENDGREATER);
    //   return true;
    // } else 
    if ((startDatedata && !endDatedata) || (!startDatedata && endDatedata)) {
      this._toastrUtilsService.showErrorMessage(title, this.STARTHAVEVALUE);
      return true;
    } else if (startDatedata && startDatedata.after(endDatedata)) {
      this._toastrUtilsService.showErrorMessage(title, this.ENDGREATERSTART);
      return true;
    } else if ((!isEmpty(windowStart) && isEmpty(windowEnd)) || (isEmpty(windowStart) && !isEmpty(windowEnd))) {
      this._toastrUtilsService.showErrorMessage(title, this.STARTENDALUE);
      return true;
    } else if (startDatedata && startDatedata.equals(endDatedata) && windowStart > windowEnd) {
      this._toastrUtilsService.showErrorMessage(title, this.ENDGREATERSTART);
      return true;
    }
    // else if (startDatedata && startDatedata.equals(endDatedata) && windowStart === windowEnd && windowStartMinute >= windowEndMinute) {
    //   this._toastrUtilsService.showErrorMessage(title, this.ENDGREATERSTART);
    //   return true;
    // } 
    else if (!isEmpty(windowStart) && !isEmpty(windowEnd) && !isEmpty(windowStartMinute) && !isEmpty(windowEndMinute) && windowStart === windowEnd && windowStartMinute === windowEndMinute) {
      this._toastrUtilsService.showErrorMessage(title, this.EXACTLYEQUAL);
      return true;
    }
  }
  selectProduct(event) {
    if (this.editData.productName.slice(-1)[0] === 'ALL') {
      this.editData.productName = ['ALL'];
    } else if (this.editData.productName.indexOf('ALL') === 0) {
      this.editData.productName = this.editData.productName.slice(1);
    }
  }
  /**
   * @description: init Form Data
   * @return {*}
   */
  initFormData() {
    this.startDatedata = null;
    this.endDatedata = null;
    this.startTime = null;
    this.endTime = null;
    this.weekState = false;
    this.weekList.map(item => {
      item.value = false;
    });
    this.editData.oneShot = null;
  }
  changeDatatimeState(datatimeState) {
    this.datatimeState = !datatimeState;
    if (!this.datatimeState) {
      this.initFormData();
    }
  }
  changeEveryDay() {
    this.weekList.map(item => {
      item.value = !!this.everyDay;
    });
  }
  changeWeek(day) {
    this.everyDay = this.weekList.every(item => item.value);
  }
  /**
   * Row Details Toggle
   * @param row
   */
  rowDetailsToggleExpand(row) {
    this.tableRowDetails.rowDetail.toggleExpandRow(row);
  }
  /**
   * Format row detail 
   * @param row 
   */
  formatJSON(row) {
    let data = [];
    if (row.actions && Object.keys(row.actions).length) {
      for (const [key, item] of Object.entries(row.actions)) {
        data.push(item[0].messages[0]);
      }
    } else if (row.messages && row.messages.length) {
      data = row.messages;
    }
    return data;
  }
  isStageAlreadyExist(stage, isEdit = false, id = null) {
    if (isEdit) {
      var temp = this.configurationOperationRequestRpc.filter((item) => {
        return item.id != id
      });
      return temp.filter((item) => {
        return item.messageType == stage
      }).length > 0;
    } else {
      return this.configurationOperationRequestRpc.filter((item) => {
        return item.messageType == stage
      }).length > 0;
    }

  };
  isCommandKeyAvailable(rpc) {
    const rpcNames = ['ScheduleInform', 'Reboot', 'Download', 'Upload', 'ScheduleDownload', 'CancelTransfer', 'ChangeDUState'];
    return rpcNames.filter((item) => {
      return item == rpc
    }).length > 0;
  };
  editStageName(event, item, modal) {
    event.stopPropagation();
    this.stageData = JSON.parse(JSON.stringify(item));
    this.modalService.open(modal, {
      backdrop: false,
      size: 'lg',
      modalDialogClass: 'modal-second',
      scrollable: true
    });
  }
  dismiss(event, modal) {
    modal.dismiss(event);
  }
  changeStageName(e) {
    let oldStageName;
    if (!this.isStageAlreadyExist(e.messageType)) {
      this.configurationOperationRequestRpc.map((item) => {
        if (item.id === e.id) {
          oldStageName = item.messageType;
          item.messageType = e.messageType
        }
      })
    } else {
      this._toastrUtilsService.showErrorMessage(this.ADDFAIL, this.NAMEEXIST);
    }
  }
  addStage(stageForm, newStage) {
    stageForm.onSubmit();
    if (stageForm.form.valid === true) {
      if (newStage != null && newStage.length != 0) {
        if (!this.isStageAlreadyExist(newStage)) {
          const { sync, isActive } = this.editData;
          let stageId = this.id++
          const stage = { id: stageId, messageType: newStage, message: {}, children: [], isStage: true, enabled: true, options: this.options, isSync: sync === 1, isActive };
          this.newStage = null;
          this.configurationOperationRequestRpc.push(stage);
          const deepData = cloneDeep(this.configurationOperationRequestRpc);
          this.configurationOperationRequestRpc.splice(0, this.configurationOperationRequestRpc.length);
          this.configurationOperationRequestRpc = [...deepData];

          // console.log(this.configurationOperationRequestRpc)
          this.clickStage(this.configurationOperationRequestRpc[this.configurationOperationRequestRpc.length - 1], this.configurationOperationRequestRpc[this.configurationOperationRequestRpc.length - 1].id)
          stageForm.resetForm();
        } else {
          this._toastrUtilsService.showErrorMessage(this.ADDFAIL, this.NAMEEXIST);
        }
      }
    }
  }
  delStage(id, stageId) {
    if (!this.editData.isActive) {
      let elementForRemoval;
      this.configurationOperationRequestRpc.filter((entry) => {
        if (entry.id === id) {
          elementForRemoval = entry.messageType;
        }
      });
      this._swalUtilsService.commonSwal(this.CONFSTAGE, this.DOGROUP + '<span class="h3 text-danger">' + elementForRemoval + '</span>' + this.STAGE, 'warning', () => {
        this.configurationOperationRequestRpc = this.configurationOperationRequestRpc.filter((entry) => {
          return entry.id !== id;
        });
        this.selectedRpc = this.configurationOperationRequestRpc[0];
        this.showStage = false;
        this.showActions = false;
      })
    }
  };
  filterEmpty(data) {
    for (let single in data) {
      if (Array.isArray(data[single].trigger.notifyParamsList)) {
        data[single].trigger.notifyParamsList = data[single].trigger.notifyParamsList.map((item) => {
          if (item) {
            if (item.type == "Event") {
              if (item.parameters && item.parameters.length > 0) {
                if (item.parameters[0].key == "" || item.parameters[0].value == "") {
                  let newItem = {}
                  // delete item.parameters
                  newItem["type"] = item.type
                  newItem["eventName"] = item.eventName
                  return newItem
                } else {
                  return item
                }
              } else {
                return item
              }
            } else {
              let newItem = {}
              for (let i in item) {
                if (item[i]) {
                  newItem[i] = item[i]
                }
              }
              return newItem
            }
          }
        })
      }
    }
    return data
  }
  toModel(date: NgbDateStruct | null): string | null {
    return date ? date.year + this.DELIMITER + date.month + this.DELIMITER + date.day : null;
  }
  prepareScheduleToJsonFormat() {
    if (this.displayType != 'configuration') {
      let schedule: any = {};
      schedule.validFrom = this.startDatedata && (new Date(this.toModel(this.startDatedata))).toJSON();
      schedule.validUntil = this.endDatedata && (new Date(this.toModel(this.endDatedata))).toJSON();
      schedule.windowStart = this.startTime && this.startTime.hour.toString().padStart(2, '0');
      schedule.windowStartMinute = this.startTime && this.startTime.minute.toString().padStart(2, '0');
      schedule.windowEnd = this.endTime && this.endTime.hour.toString().padStart(2, '0');
      schedule.windowEndMinute = this.endTime && this.endTime.minute.toString().padStart(2, '0');
      if ((this.startDatedata && this.endDatedata) || (this.startTime && this.endTime)) {
        schedule.standardTime = (new Date(new Date().toLocaleDateString())).toJSON();
      }
      schedule.offset = new Date().getTimezoneOffset();
      schedule.weekday = this.weekState ? this.weekList.filter(item => item.value).map(item => item.number) : null;
      this.editData.schedule = this.datatimeState ? schedule : null;
    }
  }
  checkParams(params) {
    let formError = {}
    let errorMsg: string;
    formError["name"] = this.st.check("name", params.name)
    formError["version"] = params.target.softwareVersion ? this.st.check("softwareVersion", params.target.softwareVersion) : null
    formError["serialNumber"] = params.target.serialNumber ? this.st.check("name", params.target.serialNumber) : null
    this.tagArr.forEach(item => {
      if (this.st.check("workflowTag", item.name) != null) {
        formError["tag"] = this.st.check("workflowTag", item.name)
      }
    })
    for (let i in formError) {
      if (formError[i]) {
        errorMsg = `${i}:${formError[i]}`
        break
      }
    }
    if (!!errorMsg) {
      this._toastrUtilsService.showWarningMessage(this.OPERATION, errorMsg);
    }
    return !!errorMsg
  }
  save(dismiss?, event?) {
    let tempStages = [];
    let isError = false;
    let rpcF = false;
    this.configurationOperationRequestRpc.map((item) => {
      let entry: any = {};
      if (typeof item.getStageEntry == 'function') {
        entry = item.getStageEntry();
      } else {
        entry = {};
        entry.isError = false;
        entry.stage = {};
        entry.stage.enabled = item.enabled;
        entry.stage.name = item.messageType;
        entry.stage.trigger = {};
        if (this.isUspConfiguration) {
          if (item.message.notifyParamsList !== undefined) {
            entry.stage.trigger.notifyParamsList = item.message.notifyParamsList;
          }
          if (item.message.informParameters !== undefined) {
            entry.stage.trigger.informParameters = item.message.informParameters;
          }
          if (item.message.deviceParameters !== undefined) {
            entry.stage.trigger.deviceParameters = item.message.deviceParameters;
          }
        } else {
          if (item.message.informEvents !== undefined) {
            entry.stage.trigger.informEvents = item.message.informEvents;
          }
          entry.stage.trigger.operator = item.message.operator;
          if (item.message.deviceOperator) {
            entry.stage.trigger.deviceOperator = item.message.deviceOperator;
          }
          if (item.message.deviceParameters !== undefined || (item.message.deviceParameters && item.message.deviceParameters.length != 0)) {
            // entry.stage.trigger.deviceOperator = item.message.deviceOperator;
            entry.stage.trigger.deviceParameters = item.message.deviceParameters;
          }
          if (item.message.informOperator) {
            entry.stage.trigger.informOperator = item.message.informOperator;
          }
          if (item.message.informParameters !== undefined || (item.message.informParameters && item.message.informParameters.length != 0)) {
            // entry.stage.trigger.informOperator = item.message.informOperator;
            entry.stage.trigger.informParameters = item.message.informParameters;
          }
        }
        if (item.isSync) {
          const keys = Object.keys(entry.stage.trigger);
          keys.forEach(key => {
            if (['informEvents', 'informParameters', 'notifyParamsList', 'parameterList'].includes(key)) {
              entry.stage.trigger[key] = [];
            }
          });
        }
        if (this.editIsActive && !item.isSync) {
          if (this.editData.type === 'cwmp' && (typeof item.message.informEvents === 'undefined' || item.message.informEvents.length == 0)) {
            entry.isError = true;
            entry.errorMessage = "Please add " + item.messageType + " event list before save!";
          } else if (this.editData.type === 'usp' && (typeof item.message.notifyParamsList === 'undefined' || item.message.notifyParamsList.length == 0)) {
            entry.isError = true;
            entry.errorMessage = "Please add " + item.messageType + " event list before save!";
          } else if (this.editData.type === 'netconf' && (typeof item.message.informEvents === 'undefined' || item.message.informEvents.length == 0)) {
            entry.isError = true;
            entry.errorMessage = "Please add " + item.messageType + " trigger events before save!";
          }
        }
      }
      if (entry.isError) {
        if (event) event.preventDefault();
        isError = true;
        this._toastrUtilsService.showErrorMessage(item.messageType, entry.errorMessage);
      } else {
        let stage = entry.stage;
        let actions = {};
        const checkActions = this.checkActions(item);
        if (checkActions.isError) {
          if (event) event.preventDefault(); // 防止點擊事件的預設行為
          isError = true;
          return false;
        } else {
          stage.messages = checkActions.actions;
        }
        if (tempStages.length != 0) {
          let previousStage = tempStages[tempStages.length - 1];
          previousStage.nextStage = stage.name;
          tempStages[tempStages.length - 1] = previousStage;
        }
        tempStages.push(stage);
      }
    });
    if (tempStages.length != 0) {
      this.editData.firstStage = tempStages[0].name;
    }
    let stages = {};
    tempStages.forEach((tempStage) => {
      tempStage.enabled = true;
      stages[tempStage.name] = tempStage;
    });
    if (this.isUspConfiguration) {
      this.editData.stages = this.filterEmpty(stages);
    } else {
      this.editData.stages = stages;
    }
    this.prepareScheduleToJsonFormat();
    if (!isError) {
      if (rpcF) {
        this._toastrUtilsService.showErrorMessage(this.BADREQ, this.PARAMNEED);
        return false
      }
      if (this.checkParams(this.editData)) {
        return false
      }
      this.loading = true;
      // this.editData.oneShot = !!this.editData.oneShot;
      this.editData.target.tags = this.tagArr.map(item => item.name)
      if (this.editData.id) {
        if (this.editIsActive != '') {
          if (this.editIsActive == 'false') {
            this.editData.isActive = false
            this.editData.readOnly = false
          } else {
            this.editData.isActive = true
            this.editData.readOnly = true
          }
        } else {
          this.editData.isActive = this.editData.isActive
        }
        this.editData.readOnly = this.mode == 'device' ? true : this.editData.isActive;
        this.editData = this.filterInvalidAttr(this.editData)
        this._workFlowsService.setWorkFlow(this.editData.id, this.editData, this.displayType).pipe(untilDestroyed(this)).subscribe({
          next: res => {
            this._toastrUtilsService.showSuccessMessage(this.displayType === 'configuration' ? this.CONFIGURATIONSUCC : this.FLOWSUCC, (this.displayType === 'configuration' ? this.CONFIGURATIONID : this.FLOWID) + this.editData.id + this.WASUPDATE);
            if (dismiss) {
              this.dismissEvt.emit(this.CROSSCLICK);
            } else {
              this.refreshEvt.emit('true')
            }
          },
          error: error => {
            this.loading = false;
            this._toastrUtilsService.showErrorMessage(this.WORKFLOW, error.error);
          },
          complete: () => {
            this.loading = false;
          }
        })
      }
      else {
        if (this.editIsActive != '') {
          if (this.editIsActive == 'false') {
            this.editData.isActive = false
            this.editData.readOnly = false
          } else {
            this.editData.isActive = true
            this.editData.readOnly = true
          }
        } else {
          this.editData.isActive = false
          this.editData.readOnly = false
        }
        this.editData.readOnly = this.mode == 'device' ? true : this.editData.isActive;
        this.editData = this.filterInvalidAttr(this.editData)
        this._workFlowsService.addWorkFlow(this.editData).pipe(untilDestroyed(this)).subscribe({
          next: res => {
            this._toastrUtilsService.showSuccessMessage(this.FLOWADDSUCC, this.FLOWID + res + this.WASADD);
            // if (dismiss) {
            //   this.dismissEvt.emit(this.CROSSCLICK);
            // } else {
            //   this.refreshEvt.emit('true')
            // }
            this.dismissEvt.emit(this.CROSSCLICK);
          },
          error: error => {
            this.loading = false;
            this._toastrUtilsService.showErrorMessage(this.WORKFLOW, error.error);
          },
          complete: () => {
            this.loading = false;
          }
        })
      }
    }
  }

  filterInvalidAttr(editData) {
    if (editData.type == 'usp') {
      for (let i in editData.stages) {
        let stages = editData.stages[i]
        stages.messages.forEach(messages => {
          if (messages.messageType == 'Add' && messages.hasOwnProperty("createObjs")) {
            messages.createObjs.forEach(objs => {
              objs.paramSettings = objs.paramSettings.filter(item => !(["unsignedInt", "Int"].includes(item.type) && item.value === ""));
              objs.paramSettings.forEach(item => {
                delete item.type;
              });
            })
          }
        })
      }
    }

    if (this.displayType == 'configuration') {
      editData = this.removeNullOrUndefined(editData);
    }
    return editData
  }

  removeNullOrUndefined(obj) {
    const newObj = {}; // 创建一个新的对象
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        // 只将非 null 和非 undefined 的属性添加到新对象中
        if (obj[key] !== null && obj[key] !== undefined && !(Array.isArray(obj[key]) && obj[key].length === 0)) {
          if (typeof obj[key] === 'object' && !Array.isArray(obj[key])) {
            const cleanedObject = this.removeNullOrUndefined(obj[key]);
            // 只在 cleanedObject 不是空对象时才添加到 newObj
            if (Object.keys(cleanedObject).length > 0) {
              newObj[key] = cleanedObject;
            }
          } else {
            newObj[key] = obj[key];
          }
        }
      }
    }
    return newObj; // 返回新对象
  }
  /**
   * On Submit
   */
  onSubmit(TDValidationForm) {
    if (!TDValidationForm.form.valid) {
      this._toastrUtilsService.showWarningMessage('Warning!', this.FORMFAIL)
    } else {
      this.save(true);
    }
  }
  /**
   * @description: prepare Schedule Data
   * @return {*}
   */
  prepareScheduleData() {
    function formatHours(hour, diffHour) {
      hour = Number(hour);
      if (isNumber(hour)) {
        hour = hour - diffHour;
        if (hour < 0) {
          hour = 24 + hour;
        } else if (hour > 23) {
          hour = hour - 24;
        }
      }
      return hour;
    }
    if (this.editData.schedule) {
      this.datatimeState = true;
      const schedule = this.editData.schedule;
      if (schedule.validFrom && schedule.validFrom != null) {
        this.startDatedata = {
          year: moment(schedule.validFrom).year(),
          month: moment(schedule.validFrom).month() + 1,
          day: moment(schedule.validFrom).date()
        }
      }
      if (schedule.validUntil && schedule.validUntil != null) {
        this.endDatedata = {
          year: moment(schedule.validUntil).year(),
          month: moment(schedule.validUntil).month() + 1,
          day: moment(schedule.validUntil).date()
        }
      }
      this.startTime = {
        hour: +schedule.windowStart,
        minute: +schedule.windowStartMinute,
        second: 0
      }
      this.endTime = {
        hour: +schedule.windowEnd,
        minute: +schedule.windowEndMinute,
        second: 0
      }
      this.weekState = !!schedule.weekday;
      this.weekList.forEach(item => {
        item.value = schedule.weekday && schedule.weekday.includes(item.number);
      });
      this.everyDay = this.weekList.every(item => item.value);
      if (schedule.standardTime || schedule.validFrom) {
        const defaultZone = (schedule.standardTime && new Date(schedule.standardTime)) ||
          (schedule.validFrom && new Date(schedule.validFrom));
        const currentZone = new Date((new Date((new Date().toLocaleDateString()))).toJSON());
        const diffHour = currentZone.getUTCHours() - defaultZone.getUTCHours();
        this.startTime.hour = +formatHours(schedule.windowStart, diffHour);
        this.endTime.hour = +formatHours(schedule.windowEnd, diffHour);
      }
      if (isEmpty(schedule.windowStart)) this.startTime = null;
      if (isEmpty(schedule.windowEnd)) this.endTime = null;
    } else {
      this.datatimeState = false;
    }
  }
  prepareConfigurationData(configuration) {
    this.tagArr = configuration?.target?.tags ? configuration.target.tags.map((item, index) => ({ index, name: item })) : [];
    if (configuration.productName && configuration.productName.includes("ADMIN")) configuration.productName[configuration.productName.map((x, i) => [i, x]).filter(x => x[1] == 'ADMIN')[0][0]] = 'ALL';
    var firstStage = configuration.firstStage;
    var stageMap = {};
    if (this.isUspConfiguration) {
      configuration.stages = this.supplementalData(configuration.stages)
    }
    for (var name in configuration.stages) {
      var stage = configuration.stages[name];
      var stageId = this.id++;
      let rpcId = this.rpcId;
      var children = [];
      if (stage.hasOwnProperty('messages')) {
        var messages = stage.messages;
        for (let messageId in messages) {
          let message = messages[messageId];
          if (message.content) {
            message.content = message.content.replace(/&lt;/g, "<").replace(/&gt;/g, ">")
          }
          const newRpc = { id: rpcId++, messageType: message.messageType, fileType: message.fileType, delaySeconds: message.delaySeconds, url: message.url, message: message, isStage: false, stageId: stageId, commandKey: message.commandKey, scriptName: message.scriptName, productName: configuration.productName };
          children.push(newRpc);
        }
      }
      var newStage = { id: stageId, messageType: stage.name, message: stage.trigger, children: children, isStage: true, enabled: stage.enabled, nextStage: stage.nextStage, isSync: configuration.sync === 1, isActive: configuration.isActive };
      if (!stageMap[stage.name]) {
        stageMap[stage.name] = {};
      }
      stageMap[stage.name] = newStage;
    }
    if (stageMap[firstStage]) {
      this.configurationOperationRequestRpc.push(stageMap[firstStage]);
      var nextStage = stageMap[firstStage].nextStage;
      while (nextStage) {
        var stageNew = stageMap[nextStage];
        this.configurationOperationRequestRpc.push(stageNew);
        nextStage = stageNew.nextStage;
      }
      this.configurationOperationRequestRpc.forEach((stage) => {
        delete stage.nextStage;
      });
    }
    this.onTypeChange(configuration.type);
  };
  supplementalData(stages) {
    for (var i in stages) {
      if (Array.isArray(stages[i].trigger.notifyParamsList)) {
        stages[i].trigger.notifyParamsList = stages[i].trigger.notifyParamsList.map((item) => {
          if (item.type == "Event") {
            if (!item.parameters) {
              item.parameters = [
                {
                  key: '',
                  condition: '',
                  paramType: '',
                  value: ''
                }
              ]
            }
          } else {
            let newItem = {};
            if (item.type == "ValueChange") {
              newItem = {
                condition: "",
                paramPath: "",
                paramType: "",
                paramValue: "",
                type: ""
              }
            } else if (item.type == "OnBoardRequest") {
              newItem = {
                oui: "",
                productClass: "",
                serialNumber: "",
                agentSupportedProtocolVersions: "",
                type: ""
              }
            }
            for (var j in item) {
              if (item[j]) {
                newItem[j] = item[j]
              }
            }
            item = newItem
          }
          return item
        })
      }
    }
    return stages
  }
  loadingStatus(event) {
    this.operationLoading = false;
  }
  selectOperation(modalSelectOperation, item) {
    this.operationLoading = true;
    this.selectedStage = item;
    this.autoFillData = {
      productName: this.editData.productName,
      version: '1.0'
    };
    this.modalService.open(modalSelectOperation, {
      backdrop: false,
      size: 'lg',
      modalDialogClass: 'modal-eighty',
      scrollable: true
    });
  }
  operationSelect(event) {
    let list = [];
    event.forEach(item => {
      list.push(...item.children);
    });
    this.configurationOperationRequestRpc.forEach(item => {
      if (item.id === this.selectedStage.id) {
        let id = item.children.length + 1;
        list.forEach(message => {
          if (message.content) {
            message.content = message.content.replace(/&lt;/g, "<").replace(/&gt;/g, ">")
          }
          const newRpc = { id: id++, messageType: message.messageType, fileType: message.fileType, delaySeconds: message.delaySeconds, url: message.url, message: message.message, isStage: false, stageId: item.id, commandKey: message.commandKey, scriptName: message.scriptName, productName: this.editData.productName };
          item.children.push(newRpc);
        })
      }
    })
    if (this.editData.type === 'netconf') { this.formatNetconf() };
  }
  /**
   * @description: get Work Flow Group Response
   * @return {*}
   */
  getWorkFlowGroupRes() {
    this.groupAccess = this._userService.canActivateRoute("GroupListComponent")
    if (this.groupAccess) {
      this._groupListService.getGroupNameList().pipe(untilDestroyed(this)).subscribe(res => {
        this.groupsFullData = res.sort((a, b) => {
          if (a.name.toLowerCase() < b.name.toLowerCase()) return -1
          return 1
        });
        this.onGroupsChange(this.editData.type)
      }), error => {
        console.log(error);
      };
    }
  }

  onGroupsChange(protocol?) {
    if (!protocol) {
      this.filterGroupsDataByProtocol = Object.assign([], this.groupsFullData);
    } else {
      this.filterGroupsDataByProtocol = Object.assign([], this.groupsFullData?.filter(item => item.protocol === protocol) || []);
    }
  }
  /**
   * @description: change type select
   * @param {*} event
   * @return {*}
   */
  onChangeType(event) {
    this.editData.productName = [];
    this.editData.target.groupId = [];
    this.filterProductList(event?.value);
    this.onTypeChange(event?.value);
  }


  filterProductList(type) {
    this.productList = [].concat(this.productData.filter(item => item.protocol && item.protocol === type));
    this.productList.sort((a, b) => {
      if (a.name.toLowerCase() < b.name.toLowerCase()) return -1
      return 1
    });
    if (this._authenticationService.isAdmin) {
      this.productList.unshift({
        name: 'ALL',
        value: 'ADMIN'
      });
    }
  }
  getProductListDataRes() {
    this._productDataService.getProductList().pipe(untilDestroyed(this)).subscribe(res => {
      this.productData = res;
      if (this.editData.type) {
        this.filterProductList(this.editData.type);
      }
    })
  }
  getProtocolLicense() {
    const protocolMap = {
      'cwmp': 'CWMP (TR-069)',
      'usp': 'USP (TR-369)',
      'netconf': 'NETCONF'
    };
    this._userService.getPageAuthority('protocol').then((data: any) => {
      this.typeList = data.map((item) => {
        return {
          name: protocolMap[item],
          value: item
        }
      }).filter(item => Object.keys(protocolMap).includes(item.value));
    })
  }
  checkActions(stage) {
    let isError = false;
    let actions = [];
    stage.children.forEach((item, index) => {
      let rpc: any = {};
      if (typeof item.getRpcEntry == 'function') {
        rpc = item.getRpcEntry();
        if (!rpc.isError && this.isCommandKeyAvailable(rpc.message.messageType)) {
          rpc.message.commandKey = item.commandKey;
        }
      } else {
        rpc = {};
        rpc.isError = false;
        rpc.message = item.message;
        rpc.message.messageType = item.messageType;
        if (this.isCommandKeyAvailable(rpc.message.messageType)) {
          rpc.message.commandKey = item.commandKey;
        }
      }
      if (rpc.isError) {
        isError = true;
        this._toastrUtilsService.showErrorMessage(item.messageType, rpc.errorMessage);
      } else {
        actions.push(rpc.message);
      }
    })
    return {
      isError: isError,
      actions: actions
    };
  }
  onAddRpc(rpc, stage) {
    let rpcId = stage.children.length;
    this.configurationOperationRequestRpc.forEach(item => {
      if (item.id === stage.id) {
        const { name: rpcName, moduleName } = rpc;
        this.netconfOptions.moduleName = moduleName || '';
        this.netconfOptions.yangRpc = this.moduleData[this.netconfOptions.moduleName]?.rpc;
        this.netconfOptions.yangNamespace = this.moduleData[this.netconfOptions.moduleName]?.namespace;
        item.children.push({
          id: rpcId++,
          messageType: rpcName,
          message: {},
          options: this.options,
          isAddStatus: true,
          netconfOptions: Object.assign({}, this.netconfOptions),
          productName: this.editData.productName
        })
        this.clickActions(item.children[item.children.length - 1], item.id, item.children[item.children.length - 1].id)
      }
    })

    this.selectRpcselected = null;
  }
  onRemoveRpc(stage, rpc, i) {
    this._swalUtilsService.commonSwal(this.CONFACTIONRE, this.DOGROUP + '<span class="h3 text-danger">' + rpc.messageType + '</span>' + this.OPERATION, 'warning', () => {
      this.configurationOperationRequestRpc.forEach(item => {
        if (item.id === stage.id) {
          item.children.splice(i, 1);
          this.showStage = false;
          this.showActions = false;
        }
      })
    }, '40rem')
  }
  onTypeChange(type) {
    switch (type) {
      case 'cwmp':
        this.selectRpc = cwmpSelectRpc;
        this.componentMap = cwmpComponentMap;
        if (this.displayType === 'workflow') {
          this.selectRpc = [
            ...this.selectRpc,
            {
              group: 'System Management',
              name: 'SyncGnbIP'
            },
            {
              group: 'System Management',
              name: 'SyncGPSLocation'
            }
          ];
        }
        this.getUspDataModelRes('cwmp');
        break;
      case 'usp':
        this.selectRpc = uspSelectRpc;
        this.componentMap = uspComponentMap;
        this.getUspDataModelRes('usp');
        break;
      case 'netconf':
        this.selectRpc = netconfSelectRpc;
        this.componentMap = netconfComponentMap;
        break;
      default:
        this.selectRpc = [];
        this.componentMap = {};
        break;
    }
    this.onGroupsChange(type)
  }
  getUspDataModelRes(type) {
    this._operationsService.getTempDataModel(type).then(res => {
      this.options = res;
      this.configurationOperationRequestRpc.forEach(item => {
        item.options = this.options;
        item.children.forEach((i) => {
          if (i) i.options = this.options;
        })
      })
    });
  }
  formatNetconf() {
    const yangComponentMap = {};
    const yangModuleRpcComponent = this.componentMap['YangModuleRpc'];
    this.configurationOperationRequestRpc.forEach(stage => {
      stage.children.forEach(item => {
        item.netconfOptions = { ...this.netconfOptions };
        item.netconfOptions.namespace = item.message.namespace;
        item.netconfOptions.moduleName = item.message.moduleName;
        item.netconfOptions.yangRpc = this.moduleData[item.message.moduleName]?.rpc;
        if (!this.componentMap[item.messageType]) {
          yangComponentMap[item.messageType] = yangModuleRpcComponent;
        }
      })
    })
    this.componentMap = Object.assign(this.componentMap, yangComponentMap);
  }
  formatNodeList(data, namespace, moduleName) {
    const caseNames = [];
    const ignoreList = ['choice', 'case'];
    let nodeList = [];
    const writable = {};
    for (let k in data) {
      if (k.indexOf('.') === -1) writable[k] = !(data[k].writable === false);
      if (data[k].type === 'case') caseNames.push(`${k.split('.').slice(-2).join('.')}.`);
    }
    for (let k in data) {
      const key = k.split('.')[0];
      data[k].writable = writable[key];
      nodeList.push(Object.assign({ name: k, label: k, namespace, moduleName }, data[k]));
    }
    const isWritable = nodeList.some(item => item.writable === true);
    const parentNodeList = [];
    const childNodeList = {};
    nodeList.forEach(item => {
      caseNames.forEach(c => {
        item.name = item.name.replace(c, '');
      })
      if (item.type === 'list') {
        parentNodeList.push(item);
        if (!childNodeList[item.name]) childNodeList[item.name] = [];
        nodeList.forEach(i => {
          if (i.label.startsWith(`${item.label}.`) && !ignoreList.includes(i.type)) {
            childNodeList[item.name].push(Object.assign({}, i, { name: i.label.replace(`${item.label}.`, ''), parentNode: item.name }));
          }
        })
      }
    })
    nodeList = nodeList.filter(item => !ignoreList.includes(item.type));
    return { isWritable, parentNodeList, childNodeList, nodeList };
  }
  getYangModuleListRes() {
    this._operationsService.getNetconfData(JSON.stringify(this.editData.productName)).pipe(untilDestroyed(this)).subscribe((res: any) => {
      this.moduleData = res;
      if (this.netconfOptions.nodeList.length === 0) {
        this.formatNetconfData(res);
      }
    })
  }
  formatNetconfData(moduleList) {
    for (let yang in moduleList) {
      const module = moduleList[yang];
      if (module.rpc && Object.keys(module.rpc).length) {
        this.formatYangRpcList(yang, module.rpc);
      }
      if (module.data && Object.keys(module.data).length) {
        const { isWritable, parentNodeList, childNodeList, nodeList } = this.formatNodeList(module.data, module.namespace, yang);
        this.netconfOptions.nodeList.push(...nodeList);
        this.netconfOptions.parentNodeList.push(...parentNodeList);
        this.netconfOptions.childNodeList = Object.assign(this.netconfOptions.childNodeList, childNodeList);
      }
    }
    this.netconfOptions.nodeList.sort((a, b) => a.name.localeCompare(b.name));
    this.netconfOptions.parentNodeList.sort((a, b) => a.name.localeCompare(b.name));
    this.formatNetconf();
  }
  formatYangRpcList(yang, rpc) {
    const baseRpcList = this.selectRpc.filter(item => item.group === 'Base').map(item => item.name);
    const yangRpc = [];
    const yangComponentMap = {};
    const yangModuleRpcComponent = this.componentMap['YangModuleRpc'];
    for (let key in rpc) {
      if (rpc[key].type === 'rpc' && !baseRpcList.includes(key)) {
        yangRpc.push({
          group: 'Yang Module',
          name: key,
          moduleName: yang,
        })
        yangComponentMap[key] = yangModuleRpcComponent;
      }
    }
    this.selectRpc = this.selectRpc.concat(yangRpc);
    this.componentMap = Object.assign(this.componentMap, yangComponentMap);
  }
  subDragula() {
    this.dragulaService.createGroup(this.editData.id, {
      moves: (el, source, handle, sibling) => {
        const cardHeader = el.querySelector('.card-header');
        return cardHeader && cardHeader.contains(handle);
      },
    });
  }

  activeStepAfterSuccessDisable(e) {
    this.editData.stepAfterSuccess = e.target.checked
  }
  activeSyncDisable(e) {
    this.editData.sync = e.target.checked ? 1 : 0;
    this.configurationOperationRequestRpc.forEach(item => {
      item.isSync = this.editData.sync === 1;
    });
    this._workFlowsService.syncSubject.next({
      hasChanged: true,
      isSync: this.editData.sync === 1
    });
  }
  inputingChange(obj, attr) {
    this[attr] = obj.text
    this.formError[attr] = obj.valid ? null : obj.errorMsg
  }

  addTag(newTag) {
    this.tagArr = this.tagArr.map((item, index) => {
      return {
        index: index,
        name: item.name
      }
    })
    this.tagArr.push({ index: this.tagArr.length, name: newTag })
    this.newTag = ''
  }

  deleteTag(item) {
    this.tagArr.splice(item.index, 1)
    this.tagArr = this.tagArr.map((item, index) => {
      return {
        name: item.name,
        index: index
      }
    });
  }
  /**
   * On Init
   */
  ngOnInit() {
    // console.log(this.editData)
    this.getProtocolLicense();
    this.editData.readOnly = this.mode == 'device' || this.noPermission || this.editData.isActive;
    this.editData.priority = Number.isFinite(+this.editData.priority) ? +this.editData.priority : 50;
    this.editData.target = this.editData.target ? this.editData.target : {}
    if (this.displayType === 'workflow') {
      this.editData.stepAfterSuccess = this.editData.stepAfterSuccess ? this.editData.stepAfterSuccess : false;
      this.editData.sync = this.editData.sync ? this.editData.sync : 0;
    } else {
      this.configurationData = Object.assign({}, this.editData);
    }
    this.prepareScheduleData();
    this.getProductListDataRes();
    this.getWorkFlowGroupRes();
    this.prepareConfigurationData(this.editData);
    this.subDragula();
    this.workflowEditStepper = document.querySelector('#workflowEditStepper');
    this.modernWizardStepper = new Stepper(this.workflowEditStepper, {});
    fromEvent(this.workflowEditStepper, 'show.bs-stepper').pipe(untilDestroyed(this)).subscribe((event: any) => {
      this.stepperIndex = event.detail.indexStep;
    })
    if (this.displayType === 'configuration') {
      this.modernWizardStepper.to(3);
    }
  }

  public stepperContentHeight
  ngAfterViewChecked() {
    this.stepperContentHeight = this.stepperModal.nativeElement.offsetHeight - this.stepperHeader.nativeElement.offsetHeight;
    // 手動觸發變更檢測
    this.cdr.detectChanges();
  };

  ngOnDestroy() {
    this.modernWizardStepper.destroy();
    this.dragulaService.destroy(this.editData.id);
    this._workFlowsService.syncSubject.next({});
  }

  public clickedStage
  public showStage: boolean = false;
  selectedIndices = [];
  clickStage(item, parentIndex): void {
    // console.log(item)
    if (this.showStage && this.clickedStage?.id === item.id) return;
    this.clickedStage = item;
    this.showStage = false;
    this.cdr.detectChanges();
    this.showStage = true;
    this.showActions = false;

    this.selectedIndices = []
    this.selectedIndices = [parentIndex, null];
  }
  public clickedActions
  public showActions: boolean = false;
  clickActions(item, parentIndex, childIndex): void {
    this.clickedActions = item;
    this.showActions = false;
    this.cdr.detectChanges();
    this.showActions = true;
    this.showStage = false;

    this.selectedIndices = []
    this.selectedIndices = [parentIndex, childIndex];
  }

  arraysAreEqual(arr1, arr2) {
    return arr1.length === arr2.length && arr1.every((element, index) => element === arr2[index]);
  }

  drop(event: CdkDragDrop<string[]>, item) {
    if (!this.editData.readOnly) {
      moveItemInArray(item, event.previousIndex, event.currentIndex);
    }
  }

  public CONF = this.translateService.instant('CONFIRM.CONF');
  public DOWANT = this.translateService.instant('CONFIRM.DOWANT');
  public THEFLOW = this.translateService.instant('CONFIRM.THEFLOW');
  public SUCC = this.translateService.instant('CONFIRM.SUCC');
  public SAVE = this.translateService.instant('COMMON.SAVE');
  public AND = this.translateService.instant('COMMON.AND');
  public editIsActive: string = ''
  public activeToggle;
  activeDisable(event, row, detailsForm) {
    if (!row.isActive) {
      if (!detailsForm.form.valid) {
        this._toastrUtilsService.showWarningMessage('Warning!', this.FORMFAIL)
        event.preventDefault(); // 防止點擊事件的預設行為
        return
      }
      else if (this.checkSchedule()) {
        event.preventDefault(); // 防止點擊事件的預設行為
        return
      }
      else {
        this.editIsActive = 'true'
        this.save(null, event);
      }
    } else {
      this.editIsActive = 'false'
      this.save();
    }
    this.priorityElement['disabledValueDecrement'] = this.editData.readOnly
    this.priorityElement['disabledValueIncrement'] = this.editData.readOnly
  }


  addOperationModalOpen(modal) {
    this.modalService.open(modal, {
      backdrop: false,
      size: 'lg',
      modalDialogClass: 'modal-second',
      scrollable: true
    });
  }



  ngOnChanges() {
    this.showStage = false;
    this.showActions = false;
  }
}

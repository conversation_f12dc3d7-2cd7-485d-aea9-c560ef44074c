import { Component, OnInit, } from '@angular/core';
import { Ng<PERSON><PERSON>odal, Ng<PERSON>DateStruct, Ng<PERSON><PERSON>imeStruct, <PERSON><PERSON><PERSON>ate, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@ng-bootstrap/ng-bootstrap';
import { CareService } from 'app/main/care/care.service';
import { ToastrUtilsService } from 'app/main/commonService/toastr-utils.service';
import { TranslateService } from '@ngx-translate/core';
import { ColumnMode, SelectionType } from '@almaobservatory/ngx-datatable';
import { Unsubscribe } from 'app/layout/components/base/Unsubscribe';
import { getWidgets } from '../careWidgetUtils';
import cloneDeep from 'lodash/cloneDeep';
import { UserService } from 'app/auth/service/user.service';
import { GenWidgetService } from 'app/main/commonService/gen-widget.service';
import { Widget } from 'app/layout/widget';
import { GridSystemService } from 'app/main/commonService/grid-system.service';
import { CoreConfigService } from '@core/services/config.service';
import { StorageService } from 'app/main/commonService/storage.service';

@Component({
  selector: 'app-care-toolbar',
  templateUrl: './care-toolbar.component.html',
  styleUrls: ['./care-toolbar.component.scss']
})
export class CareToolbarComponent extends Unsubscribe implements OnInit {
  public searchDuration = { name: 'Serial', value: 'serial' };
  public selectDuration = [
    { name: 'Serial', value: 'serial' },
    { name: 'Tag', value: 'tag' },
    { name: 'Label', value: 'label' },
    { name: 'IMEI', value: 'imei' },
  ]
  public inputNumber = "";
  public blockUIStatus = false;
  public ERRORSTCARE = this.translateService.instant('CARE.ERRORSTCARE');
  public provisioningType = "";
  public productName = "";
  public widgets;

  //device list
  public filesList: any;
  public ColumnMode = ColumnMode;

  public editMode: boolean;

  constructor(
    private modalService: NgbModal,
    private _careService: CareService,
    private _toastrUtilsService: ToastrUtilsService,
    private translateService: TranslateService,
    private _userService: UserService,
    private _gridService: GridSystemService,
    private _genWidgetService: GenWidgetService,
    private _coreConfigService: CoreConfigService,
    private _storageService: StorageService,
  ) {
    super()
    this.customSubscribe(_genWidgetService.onRenderingWidgetChanged, res => {
      this.widgets = res
    });
    this.customSubscribe(_coreConfigService.config, config => {
      let editMode = config.layout.editMode
      if (editMode !== this.editMode) {
        this.editMode = editMode
      }
    })
  }


  deviceId: string;
  searchDetails(val, modalDetail) {
    // console.log(val,this.searchDuration.value)
    this.blockUIStatus = true;
    this._careService.getDeviceId(val, this.searchDuration.value).then((res: any) => {
      //tag搜索可能存在多条数据，进行二次选择
      if (res.length > 1) {
        this.openDetailModal(modalDetail, res)
      } else {
        this._careService.careDeviceChanged.next(res[0])
        this.deviceId = res[0].id;
        this.provisioningType = res[0].provisioningType;
        this.productName = res[0].productName
        this._gridService.productName = this.productName
        this.updateUI()
      }
    }).catch((err) => {
      const errorMsg = err && err.error && err.error.replace(/imei/g, 'IMEI');
      this._toastrUtilsService.showErrorMessage(this.ERRORSTCARE, errorMsg);
      this._storageService.setSession('csrCurrentUrl', '/care');
    }).finally(() => {
      this.blockUIStatus = false;
    });
  }

  //解决不同provisioningType类型显示多少widget的问题
  updateUI() {
    this._gridService.productName = this.productName
    let personalTheme = this._gridService.tempPersonalTheme;
    // console.log('personalTheme', personalTheme)
    const currentUrl = this._genWidgetService.getPersonalThemeUrl(this.productName);
    const csrCurrentUrl = '';
    this._storageService.setSession('csrCurrentUrl', currentUrl);
    const samePageUrl = personalTheme.find(pt => pt.pageUrl == currentUrl);
    if (samePageUrl) {
      const widgetPreference = samePageUrl.widgets;
      this._genWidgetService.widgetChanged(widgetPreference, true)
    } else {
      const provisioningWidget = getWidgets(this.provisioningType);
      this._genWidgetService.widgetChanged(provisioningWidget, true)
    }
    this._genWidgetService.refreshUI()
  }

  clearSessionStorage() {
    this._storageService.clearSession();
  }


  openDetailModal(modalDetail, row) {
    this.modalService.open(modalDetail, {
      backdrop: 'static',
      size: 'lg',
      scrollable: true
    });

    this.filesList = row.map(item => {
      return {
        ...item.deviceIdStruct,
        productName: item.productName,
        provisioningType: item.provisioningType,
        id: item.id,
        ip: item.ip,
      }
    })
  }

  selectFile(row) {
    this._careService.careDeviceChanged.next(row)
    // console.log('selectFile', row)
    this.productName = row.productName
    this.provisioningType = row.provisioningType
    this._gridService.productName = this.productName
    this.updateUI()
  }



  ngOnInit(): void {
    this.clearSessionStorage();
  }

}

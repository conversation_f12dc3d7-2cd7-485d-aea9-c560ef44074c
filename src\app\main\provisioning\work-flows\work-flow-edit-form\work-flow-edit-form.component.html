<div class="modal-header">
  <h4
    class="modal-title"
    id="myModalLabelEdit">
    <span [ngClass]="{'modal-title-name': editData.id}">
      {{ (mode=='device' ? '' : editData.id ? 'COMMON.EDIT' : 'COMMON.ADD') | translate }} {{ displayType === 'workflow' ? WORKFLOW : CONFIGURATION }}
    </span>
    <span
      class="ml-75 badge badge-light-info "
      *ngIf="editData.id&&editData.name">
      {{ editData.name ?  editData.name : '' }}
    </span>
  </h4>
  <button
    type="button"
    class="close"
    (click)="dismissEvt.emit()"
    aria-label="Close">
    <span aria-hidden="true">&times;</span>
  </button>
</div>
<div
  class="modal-body h-100 mb-1"
  tabindex="0"
  ngbAutofocus>
  <section class="modern-horizontal-wizard h-100">
    <div
      id="workflowEditStepper"
      class="bs-stepper wizard-modern modern-wizard-example h-100"
      #stepperModal>
      <!-- stepper-header -->
      <div
        class="bs-stepper-header d-flex"
        #stepperHeader>
        <!-- stepper -->
        <div
          class="step cursor-pointer"
          (click)="stepNavigate()"
          data-target="#work-flow-details-modern">
          <button
            type="button"
            class="step-trigger">
            <span class="bs-stepper-box">1</span>
            <span class="bs-stepper-label">
              <span class="bs-stepper-title">{{ 'COMMON.DETAILS' | translate }}</span>
              <span class="bs-stepper-subtitle">
                {{ 'PROVISIONING.SETUPDETAILS' | translate }}
              </span>
            </span>
          </button>
        </div>
        <div
          class="line"
          [ngClass]="{'hidden': displayType === 'configuration'}">
          <i
            data-feather="chevron-right"
            class="font-medium-2"></i>
        </div>
        <div
          class="step cursor-pointer"
          [ngClass]="{'hidden': displayType === 'configuration'}"
          (click)="stepNavigate(detailsForm)"
          data-target="#work-flow-schedule-modern">
          <button
            type="button"
            class="step-trigger">
            <span class="bs-stepper-box">2</span>
            <span class="bs-stepper-label">
              <span class="bs-stepper-title">{{ 'ALARMS.SCHEDULE' | translate }}</span>
              <span class="bs-stepper-subtitle">
                {{ 'PROVISIONING.SETUP_SCHEDULE_DESCRIPTION' | translate }}
              </span>
            </span>
          </button>
        </div>
        <div class="line">
          <i
            data-feather="chevron-right"
            class="font-medium-2"></i>
        </div>
        <div
          class="step cursor-pointer"
          (click)="stepNavigate(detailsForm, scheduleForm)"
          data-target="#work-flow-actions-modern">
          <button
            type="button"
            class="step-trigger">
            <span class="bs-stepper-box">{{displayType === 'workflow' ? 3 : 2}}</span>
            <span class="bs-stepper-label">
              <span class="bs-stepper-title">
                {{ 'PROVISIONING.STAGE' | translate }} / {{ 'PROVISIONING.OPERATIONS' | translate }}
              </span>
              <span class="bs-stepper-subtitle">
                {{ (displayType === 'configuration' ? 'PROVISIONING.EDIT_CONFIGURATION_STAGE_OPERATIONS_DESCRIPTION' : 'PROVISIONING.EDIT_STAGE_OPERATIONS_DESCRIPTION') | translate }}
              </span>
            </span>
          </button>
        </div>
        <!-- active toggle -->
        <div
          *ngIf="mode!='device' && !noPermission"
          style="margin-left: auto;">
          <div class="d-flex align-items-center">
            <div class="mr-50">{{ 'DEVICES.ENABLE' | translate }}</div>
            <div class="custom-control custom-control-primary custom-switch">
              <input
                (click)="activeDisable($event,editData,detailsForm);"
                type="checkbox"
                [checked]="editData.isActive"
                class="custom-control-input"
                id="customSwitch">
              <label
                class="custom-control-label"
                for="customSwitch"></label>
            </div>
          </div>
        </div>
      </div>
      <!-- stepper-content -->
      <div
        class="bs-stepper-content"
        [ngStyle]="{'height.px': stepperContentHeight}">
        <form
          (ngSubmit)="(TDValidationForm.form.valid)"
          #TDValidationForm="ngForm"
          class="form h-100">
          <!-- Details -->
          <div
            id="work-flow-details-modern"
            class="content">
            <form
              #detailsForm="ngForm"
              class="form">
              <div class="row">
                <div class="form-group col-md-6">
                  <label
                    class="form-label"
                    for="modernType">
                    {{ 'DEVICES.PROTOCOL' | translate }}
                    <span class="text-warning ml-50">*</span>
                  </label>
                  <ng-select
                    class="column-select-filter"
                    [items]="typeList"
                    [(ngModel)]="editData.type"
                    bindLabel="name"
                    bindValue="value"
                    labelForId="modernType"
                    [readonly]="editData.id || editData.readOnly || displayType === 'configuration'"
                    (change)="onChangeType($event)"
                    [class.error]="!modernTypeRef.valid && detailsForm.submitted"
                    #modernTypeRef="ngModel"
                    required
                    name="modernType"
                    placeholder="{{ 'COMMON.SELECTTYPE' | translate }}">
                    <ng-template
                      ng-label-tmp
                      let-item="item"
                      let-clear="clear">
                      <span class="ng-value-label">{{ item.name }}</span>
                      <span
                        class="ng-value-icon right"
                        (click)="clear(item);editData.type = null"
                        aria-hidden="true">
                        ×
                      </span>
                    </ng-template>
                  </ng-select>
                  <small
                    class="form-text text-danger"
                    *ngIf="modernTypeRef.invalid && detailsForm.submitted">
                    {{ 'COMMON.FIELDREQUIRED' | translate }}
                  </small>
                </div>
                <div class="form-group col-md-6">
                  <label
                    class="form-label"
                    for="modernName">
                    {{ 'COMMON.NAME' | translate }}
                    <span class="text-warning ml-50">*</span>
                  </label>
                  <input
                    type="text"
                    trim="blur"
                    id="modernName"
                    class="form-control"
                    #modernNameRef="ngModel"
                    required
                    name="modernName"
                    [disabled]="editData.readOnly"
                    [(ngModel)]="editData.name"
                    [class.error]="!modernNameRef.valid && detailsForm.submitted && !editData.readOnly"
                    placeholder="{{ 'COMMON.NAME' | translate }}">
                  <small
                    class="form-text text-danger"
                    *ngIf="modernNameRef.invalid && detailsForm.submitted">
                    {{ 'COMMON.FIELDREQUIRED' | translate }}
                  </small>
                </div>
                <div
                  class="form-group col-md-6"
                  *ngIf="editData.type != 'pm'">
                  <label
                    class="form-label"
                    for="modernProduct">
                    {{ 'PROVISIONING.TARGETPRODUCT' | translate }}
                    <span class="text-warning ml-50">*</span>
                  </label>
                  <ng-select
                    [items]="productList"
                    [multiple]="true"
                    [closeOnSelect]="false"
                    [searchable]="false"
                    appendTo="body"
                    bindLabel="name"
                    bindValue="name"
                    labelForId="modernProduct"
                    name="modernProduct"
                    [readonly]="editData.readOnly || displayType === 'configuration'"
                    [class.error]="!modernProductRef.valid && detailsForm.submitted"
                    #modernProductRef="ngModel"
                    required
                    placeholder="{{ 'COMMON.SELECTPRODUCT' | translate }}"
                    (change)="selectProduct($event)"
                    [(ngModel)]="editData.productName">
                    <ng-template
                      ng-label-tmp
                      let-item="item"
                      let-clear="clear">
                      <span class="ng-value-label">{{ item.name }}</span>
                      <span
                        class="ng-value-icon right"
                        (click)="clear(item)"
                        aria-hidden="true">
                        ×
                      </span>
                    </ng-template>
                  </ng-select>
                  <small
                    class="form-text text-danger"
                    *ngIf="modernProductRef.invalid && detailsForm.submitted">
                    {{ 'COMMON.FIELDREQUIRED' | translate }}
                  </small>
                </div>
                <div
                  class="form-group col-md-6"
                  *ngIf="groupAccess">
                  <label
                    class="form-label"
                    for="modernGroupName">
                    {{ 'PROVISIONING.TARGETGROUP' | translate }}
                  </label>
                  <ng-select
                    class="column-select-filter"
                    appendTo="body"
                    [items]="filterGroupsDataByProtocol"
                    [(ngModel)]="editData.target.groupId"
                    bindLabel="name"
                    bindValue="id"
                    labelForId="modernGroupName"
                    [readonly]="editData.readOnly || displayType === 'configuration'"
                    name="modernGroupName"
                    placeholder="{{ 'GROUPS.SELECTGROUP' | translate }}">
                    <ng-template
                      ng-label-tmp
                      let-item="item"
                      let-clear="clear">
                      <span class="ng-value-label">{{ item.name }}</span>
                      <span
                        class="ng-value-icon right"
                        (click)="clear(item);"
                        aria-hidden="true">
                        ×
                      </span>
                    </ng-template>
                  </ng-select>
                </div>
                <div
                  class="form-group col-md-6"
                  *ngIf="displayType === 'workflow'">
                  <label
                    class="form-label"
                    for="modernExMode">
                    {{ 'PROVISIONING.EXMODE' | translate }}
                  </label>
                  <ng-select
                    class="column-select-filter"
                    appendTo="body"
                    [items]="modeList"
                    [(ngModel)]="editData.sync"
                    bindLabel="name"
                    bindValue="value"
                    labelForId="modernExMode"
                    [readonly]="editData.readOnly"
                    name="modernExMode"
                    placeholder="{{ 'PM.PLESESELECT' | translate }}">
                    <ng-template
                      ng-label-tmp
                      let-item="item"
                      let-clear="clear">
                      <span class="ng-value-label">{{ item.name }}</span>
                      <span
                        class="ng-value-icon right"
                        (click)="clear(item);"
                        aria-hidden="true">
                        ×
                      </span>
                    </ng-template>
                  </ng-select>
                </div>
                <div class="form-group col-md-6">
                  <label
                    class="form-label"
                    for="modernSerialNumber">
                    {{ 'PROVISIONING.TARGETSN' | translate }}
                  </label>
                  <input
                    type="text"
                    trim
                    id="modernSerialNumber"
                    class="form-control"
                    name="modernSerialNumber"
                    placeholder="{{ 'COMMON.SERIAL_NUMBER' | translate }}"
                    [disabled]="editData.readOnly || displayType === 'configuration'"
                    [(ngModel)]="editData.target.serialNumber">
                </div>
                <!-- <div class="form-group col-md-6">
                  <label
                    class="form-label"
                    for="modernOui">
                    OUI
                  </label>
                  <input
                    type="text"
                    id="modernOui"
                    class="form-control"
                    name="modernOui"
                    placeholder="OUI"
                    [disabled]="editData.readOnly"
                    [(ngModel)]="editData.target.oui">
                </div> -->
                <div
                  class="form-group col-md-6"
                  *ngIf="editData.type != 'pm'">
                  <label
                    class="form-label"
                    for="modernSoftwareVersion">
                    {{ 'PROVISIONING.TARGETSV' | translate }}
                  </label>
                  <input
                    type="text"
                    trim
                    id="modernSoftwareVersion"
                    class="form-control"
                    name="modernSoftwareVersion"
                    placeholder="{{ 'COMMON.SOFTVERSION' | translate }}"
                    [disabled]="editData.readOnly"
                    [(ngModel)]="editData.target.softwareVersion">
                </div>
                <!-- Tag -->
                <div class="col-md-6 tagInput">
                  <label>
                    {{ 'ALARMS.TARGETDEVICETAG' | translate }}
                  </label>
                  <div class="form-group">
                    <div class="input-group">
                      <app-custom-input
                        [content]="newTag"
                        [errMsgType]="'outside'"
                        [validatorItem]="'workflowTag'"
                        [trimAllowed]="true"
                        [customName]="'workflowTag'"
                        [placeholder]="'GROUPS.INPUTTAGNAME' | translate"
                        [disabled]="editData.readOnly || tagArr.length"
                        (checkValidEvt)="inputingChange($event,'newTag')"></app-custom-input>
                      <div class="input-group-append">
                        <button
                          class="btn btn-primary btn-sm"
                          type="button"
                          rippleEffect
                          [disabled]="editData.readOnly || newTag == '' || newTag == null || newTag == undefined || formError?.newTag"
                          (click)="addTag(newTag)">
                          <span [data-feather]="'plus'"></span>
                        </button>
                      </div>
                    </div>
                  </div>
                  <div>
                    <div
                      *ngFor="let item of tagArr ; let i = index"
                      class="badge badge-light-success"
                      style="margin: 0px 10px 10px 0px;">
                      <span class="mr-50">{{item.name}}</span>
                      <a
                        [disabled]="editData.readOnly"
                        [class.disabled]="editData.readOnly"
                        (click)="editData.readOnly ? $event.preventDefault() : deleteTag(item)">
                        <span
                          [data-feather]="'trash-2'"
                          class="text-danger"></span>
                      </a>
                    </div>
                  </div>
                </div>
                <div class="form-group col-md-6">
                  <label
                    class="form-label"
                    for="modernPriority">
                    {{ 'COMMON.PRIORITY' | translate }}
                    <span class="text-warning ml-50">*</span>
                  </label>
                  <core-touchspin
                    id="modernPriority"
                    #priorityElement
                    class="d-block"
                    [numberValue]="editData.priority"
                    [minValue]="0"
                    [maxValue]="100"
                    [disable]="editData.readOnly"
                    (onChange)="changeTouchspin($event, 'priority')"></core-touchspin>
                </div>
                <div
                  class="form-group col-md-6"
                  *ngIf="displayType === 'workflow'">
                  <label
                    class="form-label"
                    for="modernStepAfterSuccess">
                    {{ 'PROVISIONING.STEPAFTERSUCCESS' | translate }}
                  </label>
                  <ng-select
                    class="column-select-filter"
                    appendTo="body"
                    [items]="booleanList"
                    [(ngModel)]="editData.stepAfterSuccess"
                    bindLabel="name"
                    bindValue="value"
                    labelForId="modernStepAfterSuccess"
                    [readonly]="editData.readOnly"
                    name="modernStepAfterSuccess"
                    placeholder="{{ 'PM.PLESESELECT' | translate }}">
                    <ng-template
                      ng-label-tmp
                      let-item="item"
                      let-clear="clear">
                      <span class="ng-value-label">{{ item.name }}</span>
                      <span
                        class="ng-value-icon right"
                        (click)="clear(item);"
                        aria-hidden="true">
                        ×
                      </span>
                    </ng-template>
                  </ng-select>
                </div>
                <!-- <div
                  class="form-group col-md-6"
                  *ngIf="displayType === 'workflow'">
                  <label
                    class="form-label"
                    for="modernSync">
                    Sync
                  </label>
                  <div class="custom-control custom-control-primary custom-switch">
                    <input
                      type="checkbox"
                      [disabled]="editData.readOnly"
                      (click)="activeSyncDisable($event);"
                      [checked]="editData.sync === 1"
                      class="custom-control-input"
                      id="customSyncSwitch">
                    <label
                      class="custom-control-label"
                      for="customSyncSwitch"></label>
                  </div>
                </div> -->
              </div>
            </form>
          </div>
          <!-- Schedule -->
          <div
            id="work-flow-schedule-modern"
            class="content">
            <form
              #scheduleForm="ngForm"
              autocomplete="off">
              <div class="row mb-50">
                <div class="form-group col-md-auto">
                  <div class="custom-control custom-radio">
                    <input
                      type="radio"
                      id="modernDatatimeAlways"
                      name="modernDatatimeAlways"
                      [checked]="!datatimeState"
                      [disabled]="editData.readOnly"
                      (change)="changeDatatimeState(datatimeState)"
                      class="custom-control-input">
                    <label
                      class="custom-control-label"
                      for="modernDatatimeAlways">
                      {{ editData.sync ? ('PROVISIONING.EXIMMEDIATELY' | translate) : ('PROVISIONING.ALWAYSACTIVE' | translate) }}
                    </label>
                  </div>
                </div>
                <div class="form-group col-md-auto">
                  <div class="custom-control custom-radio">
                    <input
                      type="radio"
                      id="modernDatatimeSelect"
                      name="modernDatatimeSelect"
                      [checked]="datatimeState"
                      [disabled]="editData.readOnly"
                      (change)="changeDatatimeState(datatimeState)"
                      class="custom-control-input">
                    <label
                      class="custom-control-label"
                      for="modernDatatimeSelect">
                      {{ 'PROVISIONING.SELECTACTIVEDATERANGE' | translate }}
                    </label>
                  </div>
                </div>
              </div>
              <div
                *ngIf="datatimeState"
                class="pl-1">
                <div class="row mb-1">
                  <div class="col-md-6">
                    <h5 class="mb-50">
                      {{ 'PROVISIONING.ACTIVEDATERANGE' | translate }}
                    </h5>
                    <div class="form-group">
                      <label
                        class="form-label"
                        for="modernStartDate">
                        {{ 'ALARMS.STARTDATE' | translate }}
                      </label>
                      <div class="input-group">
                        <input
                          id="modernStartDate"
                          trim
                          class="form-control"
                          placeholder="yyyy-mm-dd"
                          name="modernStartDate"
                          [disabled]="editData.readOnly"
                          [(ngModel)]="startDatedata"
                          readonly
                          (click)="startDate.toggle()"
                          ngbDatepicker
                          container="body"
                          #startDate="ngbDatepicker">
                        <div class="input-group-append">
                          <button
                            *ngIf="startDatedata && !editData.readOnly"
                            class="btn btn-outline-secondary feather icon-x"
                            (click)="startDatedata = null"
                            type="button"
                            rippleEffect></button>
                          <button
                            class="btn btn-outline-secondary feather icon-calendar"
                            (click)="startDate.toggle()"
                            type="button"
                            rippleEffect></button>
                        </div>
                      </div>
                    </div>
                    <div class="form-group">
                      <label
                        class="form-label"
                        for="modernEndDate">
                        {{ 'ALARMS.ENDDATE' | translate }}
                      </label>
                      <div class="input-group">
                        <input
                          id="modernEndDate"
                          trim
                          class="form-control"
                          placeholder="yyyy-mm-dd"
                          name="modernEndDate"
                          [disabled]="editData.readOnly"
                          [(ngModel)]="endDatedata"
                          readonly
                          (click)="endDate.toggle()"
                          ngbDatepicker
                          container="body"
                          #endDate="ngbDatepicker">
                        <div class="input-group-append">
                          <button
                            *ngIf="endDatedata && !editData.readOnly"
                            class="btn btn-outline-secondary feather icon-x"
                            (click)="endDatedata = null"
                            type="button"
                            rippleEffect></button>
                          <button
                            class="btn btn-outline-secondary feather icon-calendar"
                            (click)="endDate.toggle()"
                            type="button"
                            rippleEffect></button>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <h5 class="mb-50">
                      {{ 'PROVISIONING.ACTIVETIMERANGE' | translate }}
                    </h5>
                    <div class="row">
                      <div class="form-group col-md-6">
                        <label
                          class="form-label"
                          for="modernStartTime">
                          {{ 'ALARMS.STARTTIME' | translate }}
                        </label>
                        <ngb-timepicker
                          [(ngModel)]="startTime"
                          [disabled]="editData.readOnly"
                          name="startTime"></ngb-timepicker>
                      </div>
                      <div class="form-group col-md-6">
                        <label
                          class="form-label"
                          for="modernEndTime">
                          {{ 'ALARMS.ENDTIME' | translate }}
                        </label>
                        <ngb-timepicker
                          [(ngModel)]="endTime"
                          [disabled]="editData.readOnly"
                          name="endTime"></ngb-timepicker>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="row mb-50">
                  <div class="form-group col-md-auto">
                    <div class="custom-control custom-checkbox">
                      <input
                        type="checkbox"
                        class="custom-control-input"
                        [(ngModel)]="weekState"
                        [disabled]="editData.readOnly"
                        id="modernWeek"
                        name="modernWeek">
                      <label
                        class="custom-control-label"
                        for="modernWeek">
                        {{ 'PROVISIONING.DAYOFWEEK' | translate }}
                      </label>
                    </div>
                  </div>
                </div>
                <div
                  class="row mb-1 pl-1"
                  *ngIf="weekState">
                  <div class="form-group col-md-auto">
                    <div class="custom-control custom-checkbox">
                      <input
                        type="checkbox"
                        class="custom-control-input"
                        [disabled]="editData.readOnly"
                        [(ngModel)]="everyDay"
                        (change)="changeEveryDay()"
                        id="modernEveryDay"
                        name="modernEveryDay">
                      <label
                        class="custom-control-label"
                        for="modernEveryDay">
                        {{ 'PROVISIONING.EVERYDAY' | translate }}
                      </label>
                    </div>
                  </div>
                  <div
                    class="form-group col-md-auto"
                    *ngFor="let item of weekList">
                    <div class="custom-control custom-checkbox">
                      <input
                        type="checkbox"
                        class="custom-control-input"
                        [disabled]="editData.readOnly"
                        [(ngModel)]="item.value"
                        (change)="changeWeek(item)"
                        id="modern{{ item.name }}"
                        name="modern{{ item.name }}">
                      <label
                        class="custom-control-label"
                        for="modern{{ item.name }}">
                        {{ item.translation | translate }}
                      </label>
                    </div>
                  </div>
                </div>
                <div class="row" *ngIf="!editData.sync">
                  <div class="form-group col-md-auto">
                    <div class="custom-control custom-checkbox">
                      <input
                        type="checkbox"
                        class="custom-control-input"
                        [(ngModel)]="editData.oneShot"
                        [disabled]="editData.readOnly"
                        id="modernOnce"
                        name="modernOnce">
                      <label
                        class="custom-control-label"
                        for="modernOnce">
                        {{ 'PROVISIONING.WITHONLYONCE' | translate }}
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>
          <!-- Actions -->
          <div
            id="work-flow-actions-modern"
            class="content h-100">
            <form
              #actionsForm="ngForm"
              class="d-flex flex-wrap h-100">
              <!-- Actions Request -->
              <div
                class="col-lg-5 col-md-12 h-100"
                style="padding-left: 0px;">
                <div class="h-100">
                  <div
                    class="card card-transaction overflow-hidden h-100"
                    style="padding: 5px;">
                    <h6>
                      {{ 'PROVISIONING.BUILD' | translate }} {{ 'PROVISIONING.STAGE' | translate }} / {{ 'PROVISIONING.OPERATIONS' | translate }}
                    </h6>
                    <!-- stage name -->
                    <div style="cursor:default;">
                      <form
                        #stageForm="ngForm"
                        class="w-100">
                        <div class="d-flex">
                          <div class="flex-fill mr-1">
                            <input
                              type="text"
                              trim
                              placeholder="{{ 'PROVISIONING.ENTERSTAGENAME' | translate }}"
                              class="form-control form-control-sm"
                              [(ngModel)]="newStage"
                              name="newStage"
                              #stegeNameRef="ngModel"
                              required
                              [class.error]="!stegeNameRef.valid && stageForm.submitted && !editData.readOnly"
                              [disabled]="editData.readOnly">
                          </div>
                          <button
                            type="button"
                            class="btn btn-sm btn-primary"
                            [disabled]="editData.readOnly"
                            (click)="addStage(stageForm, newStage)"
                            rippleEffect>
                            <span
                              [data-feather]="'plus'"
                              [class]="'mr-25'"></span>
                            {{ 'PROVISIONING.STAGE' | translate }}
                          </button>
                          <small
                            class="col form-text text-danger"
                            *ngIf="stegeNameRef.invalid && stageForm.submitted">
                            {{ 'COMMON.FIELDREQUIRED' | translate }}
                          </small>
                        </div>
                      </form>
                    </div>
                    <hr style="margin: 10px 0;">
                    <!-- Actions -->
                    <div
                      class="mh-1px"
                      style="padding:5px;cursor:default;"
                      [perfectScrollbar]>
                      <div
                        class="row"
                        *ngIf="configurationOperationRequestRpc.length"
                        #container>
                        <ol
                          cdkDropList
                          (cdkDropListDropped)="drop($event,configurationOperationRequestRpc)"
                          class="w-100"
                          style="list-style-type: none;padding:0 10px;">
                          <li
                            cdkDrag
                            [cdkDragDisabled]="editData.readOnly"
                            [cdkDragBoundary]="container"
                            [cdkDragPreviewContainer]="container"
                            [cdkDragPreviewClass]="dragList"
                            id="{{i}}"
                            *ngFor="let item of configurationOperationRequestRpc; let i = index"
                            style="cursor: pointer;"
                            class="w-100 mb-50">
                            <div
                              class="w-100 d-flex justify-content-between align-items-center mb-50 actionList badge badge-pill badge-light-success"
                              (click)="clickStage(item,i);"
                              [ngClass]="{'badge-light-warning': arraysAreEqual([i,null],selectedIndices)}">
                              <span class="alignCenter">
                                <svg
                                  width="14px"
                                  height="14px"
                                  [ngStyle]="{'display': editData.readOnly ? 'none' : 'block'}">
                                  <use
                                    _ngcontent-ovv-c484
                                    href="./../assets/fonts/added-icon.svg#movable"></use>
                                </svg>
                                <span class="stageTitle ml-50">
                                  {{ 'PROVISIONING.STAGE' | translate }} :
                                </span>
                                <span class="ml-50">{{item.messageType}}</span>
                              </span>
                              <div>
                                <!-- add operation btn -->
                                <button
                                  type="button"
                                  href="javascript:void(0);"
                                  ngbTooltip="{{ 'COMMON.ADDOPERATION' | translate }}"
                                  container="body"
                                  placement="top"
                                  style="padding: 2px;"
                                  class="btn btn-icon btn-primary mr-50 svgIcon"
                                  [disabled]="editData.readOnly"
                                  (click)="addOperationModalOpen(addOpeartionModal)"
                                  rippleEffect>
                                  <i data-feather="plus"></i>
                                </button>
                                <button
                                  href="javascript:void(0);"
                                  type="button"
                                  style="padding: 2px;"
                                  class="btn btn-icon btn-flat-primary svgIcon"
                                  [disabled]="editData.readOnly"
                                  (click)="delStage(item.id, item.stageId);$event.stopPropagation();"
                                  rippleEffect>
                                  <i data-feather="trash-2"></i>
                                </button>
                              </div>
                            </div>
                            <!-- children -->
                            <ol
                              class="w-100 mb-1"
                              style="list-style-type: none;padding-left: 20px;">
                              <li
                                class="w-100"
                                *ngFor="let rpc of item.children; let j = index">
                                <div
                                  class="w-100 d-flex justify-content-between mb-50 operationList align-items-center badge badge-pill badge-light-secondary"
                                  [ngClass]="{'badge-light-warning': arraysAreEqual([i,j],selectedIndices)}"
                                  (click)="clickActions(rpc,i,j);">
                                  <span class="alignCenter">
                                    <span class="stageTitle">
                                      {{ 'PROVISIONING.OPERATIONS' | translate }} :
                                    </span>
                                    <span class="ml-50">{{rpc.messageType}}</span>
                                  </span>
                                  <!-- delete btn -->
                                  <button
                                    href="javascript:void(0);"
                                    type="button"
                                    style="padding: 2px;"
                                    class="btn btn-icon btn-flat-primary svgIcon"
                                    [disabled]="editData.readOnly"
                                    (click)="onRemoveRpc(item, rpc, j);$event.stopPropagation();"
                                    rippleEffect>
                                    <i data-feather="trash-2"></i>
                                  </button>
                                </div>
                              </li>
                            </ol>
                          </li>
                        </ol>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- Settings -->
              <div
                class="col-lg-7 col-md-12 h-100"
                style="padding-left: 0px;padding-right: 0;">
                <div class="removeCardHeader card h-100">
                  <!-- <h4 class="card-title">Settings</h4> -->
                  <div
                    *ngIf="!showStage && !showActions || configurationOperationRequestRpc.length==0"
                    style="padding:5px;margin-top: 20px;height: 100%;">
                    <ngb-alert
                      *ngIf="configurationOperationRequestRpc.length==0"
                      [type]="'warning'"
                      [dismissible]="false">
                      <div class="alert-body">
                        Add Stage first to build Workflows containing Stage/Operations.
                      </div>
                    </ngb-alert>
                    <ngb-alert
                      *ngIf="configurationOperationRequestRpc.length>0"
                      [type]="'warning'"
                      [dismissible]="false">
                      <div class="alert-body">Select Stage/Operations for editing.</div>
                    </ngb-alert>
                  </div>
                  <!-- actions -->
                  <div
                    *ngIf="showStage && configurationOperationRequestRpc.length>0"
                    class="h-100 card card-transaction overflow-hidden"
                    style="padding: 5px">
                    <h6>
                      {{ 'COMMON.EDIT' | translate }} {{ 'PROVISIONING.STAGE' | translate }} : {{ clickedStage.messageType}}
                      <!-- edit btn -->
                      <button
                        type="button"
                        href="javascript:void(0);"
                        ngbTooltip="{{ 'COMMON.EDITSTAGE' | translate }}"
                        container="body"
                        placement="top"
                        style="padding: 1px;"
                        class="btn btn-icon btn-flat-primary svgIcon"
                        [disabled]="editData.readOnly"
                        (click)="editStageName($event, clickedStage, modalEditName)"
                        rippleEffect>
                        <i data-feather="edit"></i>
                      </button>
                    </h6>
                    <div
                      class="h-100"
                      [perfectScrollbar]>
                      <!-- trigger -->
                      <app-work-flow-trigger-cwmp
                        *ngIf="showStage && editData.type === 'cwmp'"
                        [accessor]="clickedStage"
                        [readOnly]="editData.readOnly">
                      </app-work-flow-trigger-cwmp>
                      <app-work-flow-trigger-usp
                        *ngIf="showStage && editData.type === 'usp'"
                        [accessor]="clickedStage"
                        [readOnly]="editData.readOnly"></app-work-flow-trigger-usp>
                      <app-work-flow-trigger-netconf
                        *ngIf="showStage && editData.type === 'netconf'"
                        [accessor]="clickedStage"
                        [readOnly]="editData.readOnly"></app-work-flow-trigger-netconf>
                    </div>
                  </div>
                  <div
                    *ngIf="showActions && configurationOperationRequestRpc.length>0"
                    class="h-100 card card-transaction overflow-hidden"
                    style="padding: 5px">
                    <h6>
                      {{ 'COMMON.EDIT' | translate }} {{ 'PROVISIONING.OPERATIONS' | translate }} : {{ clickedActions.messageType}}
                    </h6>
                    <div
                      class="h-100"
                      [perfectScrollbar]>
                      <ng-template
                        appRpc
                        [component]="clickedActions.messageType"
                        [data]="clickedActions"
                        [readOnly]="editData.readOnly"
                        [componentMap]="componentMap"></ng-template>
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </form>
      </div>
    </div>
  </section>
</div>
<div class="modal-footer">
  <div class="w-100 d-flex justify-content-between">
    <button
      class="btn btn-prev"
      [ngClass]="stepperIndex === 0 ? 'btn-outline-secondary' : 'btn-primary'"
      [disabled]="stepperIndex === 0"
      (click)="modernHorizontalPrevious()"
      rippleEffect>
      <i
        data-feather="arrow-left"
        class="align-middle mr-sm-25 mr-0"></i>
      <span class="align-middle d-sm-inline-block d-none">{{ 'COMMON.PREVIOUS' | translate }}</span>
    </button>
    <button
      *ngIf="stepperIndex === 0 || (stepperIndex === 1 && displayType !== 'configuration')"
      class="btn btn-primary btn-next"
      (click)="modernHorizontalNext(stepperIndex === 0 ? detailsForm : scheduleForm, stepperIndex === 0 ? 'detailsForm' : 'scheduleForm')"
      rippleEffect>
      <span class="align-middle d-sm-inline-block d-none">{{ 'COMMON.NEXT' | translate }}</span>
      <i
        data-feather="arrow-right"
        class="align-middle ml-sm-25 ml-0"></i>
    </button>
    <button
      *ngIf="(stepperIndex === 2 && mode != 'device') || (stepperIndex === 1 && displayType === 'configuration')"
      type="submit"
      class="btn btn-success btn-submit"
      [disabled]="editData.readOnly"
      rippleEffect
      (click)="onSubmit(actionsForm)">
      <span
        *ngIf="loading"
        class="spinner-border spinner-border-sm mr-1"></span>
      {{ 'USERS.SUBMIT' | translate }}
    </button>
    <button
      *ngIf="stepperIndex === 2 && mode == 'device'"
      type="button"
      class="btn btn-primary"
      rippleEffect
      (click)="dismissEvt.emit()">
      {{ 'COMMON.CLOSE' | translate }}
    </button>
  </div>
</div>
<!-- Modal -->
<!-- modalSelectOperation -->
<ng-template
  #modalSelectOperation
  let-modal>
  <app-operation-select
    class="modal-content"
    [type]="editData.type"
    [autoFillData]="autoFillData"
    (dismissEvt)="modal.dismiss('Cross click')"
    (operationSelectEvt)="operationSelect($event)"
    (loadingEvt)="loadingStatus($event)"></app-operation-select>
</ng-template>
<!-- modalEditName -->
<ng-template
  #modalEditName
  let-modal>
  <app-work-flow-stage-name
    class="modal-content"
    (dismissEvt)="dismiss($event, modal)"
    (changStageNameEvt)="changeStageName($event, old)"
    [stageData]="stageData"></app-work-flow-stage-name>
</ng-template>
<!-- addOpeartionModal -->
<ng-template
  #addOpeartionModal
  let-modal>
  <div class="modal-header">
    <h5
      class="modal-title"
      id="myModalLabel160">
      {{ 'COMMON.ADDOPERATION' | translate }}
    </h5>
    <button
      type="button"
      class="close"
      (click)="modal.dismiss('Cross click')"
      aria-label="Close">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div
    class="modal-body h-100"
    tabindex="0"
    ngbAutofocus>
    <div
      class="w-100"
      *ngIf="showStage && configurationOperationRequestRpc.length>0"
      style="padding:5px;cursor:default;">
      <div class="d-flex">
        <button
          *ngIf="!operationLoading"
          type="button"
          ngbTooltip="{{ 'DEVICES.OPERATION_ACTION.SELECT_OPERATION' | translate }}"
          container="body"
          class="btn btn-sm btn-icon btn-primary mr-1"
          [disabled]="editData.readOnly || operationLoading"
          (click)="selectOperation(modalSelectOperation, clickedStage)"
          rippleEffect>
          <i
            [data-feather]="'file-text'"
            [size]="14"></i>
        </button>
        <button
          *ngIf="operationLoading"
          type="button"
          class="btn btn-sm btn-icon btn-primary mr-1"
          [disabled]="editData.readOnly || operationLoading"
          rippleEffect>
          <span class="spinner-border spinner-border-sm"></span>
        </button>
        <div class="flex-fill mr-1">
          <ng-select
            class="column-select-filter ng-select-size-sm"
            name="modernRpc"
            [items]="selectRpc"
            appendTo="body"
            bindLabel="name"
            groupBy="group"
            [(ngModel)]="selectRpcselected"
            [readonly]="editData.readOnly"
            placeholder="{{ 'COMMON.SELECTACTION' | translate }}"
            [clearable]="false">
            <ng-template
              ng-optgroup-tmp
              let-item="item">
              {{ item.group || 'Unnamed group' }}
            </ng-template>
          </ng-select>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button
      type="button"
      class="btn btn-primary"
      (click)="onAddRpc(selectRpcselected, clickedStage);modal.close('Close click')">
      {{ 'COMMON.ADD' | translate }}
    </button>
  </div>
</ng-template>
<!-- / Modal -->

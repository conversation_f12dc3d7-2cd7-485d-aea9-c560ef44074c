export const locale = {
  lang: 'jp',
  data: {
    MENU: {
      HOME: 'ホーム',
      SAMPLE: 'サンプル'
    },
    DASHBOARD: {
      TITLE: 'ダッシュボード',
      AVGSESSIONS: {
        TITLE: '平均セッション',
        VIEWDETAILS: '詳細を見る',
        LAST1DAY: '過去24時間',
        LAST7DAYS: '過去7日間',
        LAST15DAYS: '過去15日間',
        LAST28DAYS: '過去28日間',
        LAST30DAYS: '過去30日間',
        LASTMONTH: '先月',
        LASTYEAR: '去年',
      },
      MAP: '位置',
      SESSIONDURATION: 'セッション期間',
      SESSIONDURATIONDESCRIPTION: 'すべてのデバイスにおけるセッション期間の平均の履歴チャート。セッション期間：デバイスとAMPの間でセッションに費やされた総時間。',
      SESSIONRATE: 'セッションレート',
      SESSIONRATEDESCRIPTION: 'オンラインデバイスのCWMPによるリクエスト頻度の履歴チャート。',
      LATENCY: 'リクエスト遅延',
      LATENCYDESCRIPTION: 'すべてのデバイスにおけるリクエスト遅延の平均の履歴チャート。リクエスト遅延：デバイスとAMPの間でリクエストに費やされた総時間。',
      REGISTERED_COUNT_DISTRIBUTION: '登録デバイス',
      REGISTERED_COUNT_DISTRIBUTION_DESCRIPTION: '各製品ごとの登録デバイスの分布チャート。',
      PROVISIONINGTYPEDISTRIBUTION: "供給タイプ割当",
      PROVISIONINGTYPEDISTRIBUTION_DESCRIPTION: "登録製品ごとに選択されたすべての構成タイプの分布。",
      ONLINE_COUNT_DISTRIBUTION: 'オンラインデバイス',
      ONLINE_COUNT_DISTRIBUTION_DESCRIPTION: '各製品ごとのオンラインデバイス数の分布チャート。',
      HISTORYONLINEDEVICE: 'オンラインデバイス',
      ONLINEDEVICEDESCRIPTION: '各製品ごとのオンラインデバイス数の履歴チャート。',
      SOFTWARE_VERSION_DISTRIBUTION: 'ソフトウェアバージョン',
      SOFTWARE_VERSION_DISTRIBUTION_DESCRIPTION: 'オンラインデバイスのソフトウェアバージョンの分布チャート。',
      PROVISIONING_CODE_DISTRIBUTION: 'プロビジョニングコード',
      PROVISIONING_CODE_DISTRIBUTION_DESCRIPTION: 'オンラインデバイスのプロビジョニングコードの分布チャート。',
      XMPP_STATUS_DISTRIBUTION: 'XMPPステータス',
      XMPP_STATUS_DISTRIBUTION_DESCRIPTION: 'オンラインデバイスのXMPPステータスの分布チャート。',
      IMS_STATUS_DISTRIBUTION: 'IMSステータス分布',
      IMS_STATUS_DISTRIBUTION_DESCRIPTION: 'オンラインデバイスのIMS登録ステータスの分布チャート。',
      SIM_STATUS_DISTRIBUTION: 'SIMステータス',
      SIM_STATUS_DISTRIBUTION_DESCRIPTION: 'オンラインデバイスのSIM接続ステータスの分布チャート。',
      IPSEC_STATUS_DISTRIBUTION: 'IPSecステータス',
      IPSEC_STATUS_DISTRIBUTION_DESCRIPTION: 'オンラインデバイスのIPSecトンネル接続ステータスの分布チャート。',
      TOTAL: '合計',
      ONLINE: 'オンライン',
      ONLINE_DEVICE: 'オンラインデバイス',
      ONLINE_DEVICE_DESCRIPTION: 'オンラインデバイスの総数。',
      GROUPS_COUNT: 'グループ',
      GROUPS_COUNT_DESCRIPTION: 'グループの総数。',
      ONLINE_USERS: 'オンラインユーザー',
      ONLINE_USERS_DESCRIPTION: '過去30分以内にログインしているユーザーの総数。',
      UE_COUNT: 'ユーザー機器（UE）',
      UE_COUNT_DESCRIPTION: 'スモールセルに接続しているユーザー機器（UE）の総数。',
      ALARMS_TOTAL: 'アラーム総数',
      ALARMS_TOTAL_DESCRIPTION: 'デバイスによって報告されたアラームの総数。',
      ALARMS_SERVERITY: 'アラームの重大度',
      ALARMS_SERVERITY_DESCRIPTION: '重大、重要、軽微、警告といった異なる重大度のアラーム総数。',
      GROUP_LIST: 'グループリスト',
      GROUP_LIST_DESCRIPTION: 'デバイス数、重大度の異なるアラーム数などの詳細情報を含む作成されたグループのリスト。',
      COVERMAP: 'カバレッジマップ',
      COVERMAP_DESCRIPTION: 'グループ内のデバイスの位置と無線カバレッジを表示します。',
      ALARM_LIST: 'アラームリスト',
      ALARM_LIST_DESCRIPTION: 'デバイスによって報告されたすべてのアラームをリスト化。クリア済み、未クリアのアラーム、重大度、イベント時刻、および考えられる原因を表示。',
      SYSTEM_EVENT_LIST: 'システムイベント',
      SYSTEM_EVENT_LIST_DESCRIPTION: '通信とアクセスの失敗に関連するすべてのログイベントをリスト化。',
      STSTEM_INFORMATIONS: 'システム情報',
      STSTEM_INFORMATIONS_DESCRIPTION: 'AMPシステム情報またはサーバーレポート内容。',
      TOTAL_CLIENTS: 'WiFi RSSI分布の総数',
      TOTAL_CLIENTS_DESCRIPTION: '異なるRSSIレベルのWiFiクライアントの分布チャート。',
      TOTAL_CLIENTS_COUNT: 'WiFiクライアント',
      TOTAL_CLIENTS_COUNT_DESCRIPTION: 'WiFi APに接続されたWiFiクライアントの総数。',
      EXCELLENT_CLIENTS: '優秀',
      GOOD_CLIENTS: '良好',
      POOR_CLIENTS: '不良',
      STATISTICSOFTOTALCLIENTS: 'WiFi RSSI分布の履歴',
      STATISTICSOFTOTALCLIENTS_DESCRIPTION: '異なるRSSIレベルのWiFiクライアントの履歴チャート。',
      GROUPNAME: 'グループ名',
      PRODUCTNAME: '製品名',
      MANAGEMENTSCOPE: '管理範囲',
      REGION: '地域',
      LOCATION: '位置',
      CONFIGURATION: '設定',
      APS: 'APの総数',
      TOTALCLIENTS: 'クライアントの総数',
      EXCELLENTCLIENTS: '優秀なクライアント',
      GOODCLIENTS: '良好なクライアント',
      POORCLIENTS: '不良なクライアント',
      EXCELLENT: '優秀',
      GOOD: '良好',
      POOR: '不良',
      EXCELLENT_DESCRIPTION: 'RSSI > -65dBm',
      GOOD_DESCRIPTION: '-65dBm < RSSI < -80dBm',
      POOR_DESCRIPTION: 'RSSI < -80dBm',
      TOTALCLIENTSTABLE: 'クライアントテーブル',
      CLIENTS: 'WiFiクライアント情報',
      ONLINEAPS: 'オンラインAP',
      TAGS: 'タグ',
      GROUPSLOCATION: 'グループの位置',
      GROUPSLOCATION_DESCRIPTION: 'マップ上にすべてのグループの位置と基本情報を表示。',
      DEVICESLOCATION: "デバイスの位置",
      DEVICESLOCATION_DESCRIPTION: "すべてのデバイスの位置と基本情報を地図上に表示します。",
    },
    DEVICES: {
      WIFICLIENT: 'WiFiクライアント',
      WIFIAPNAME: 'WiFi AP 名',
      WIFIAPROLE: 'WiFi AP 役割',
      WIFIAPCONFVERSION: 'WiFi AP 設定バージョン',
      WIFIAPNCONFVERSION: 'WiFi APN 設定バージョン',
      TAGS: 'タグ',
      LIST: 'デバイスリスト',
      SERIAL_NUMBER: 'シリアル番号',
      MODEL_NAME: 'モデル名',
      FIRMWARE: 'ファームウェア',
      LABEL: 'ラベル',
      GROUP: 'グループ',
      PRODUCT: '製品',
      LAST_CONNECTED: '最終接続',
      LAST_EVENT: '最終イベント',
      UPTIME: '稼働時間',
      TIME_ZONE: 'タイムゾーン',
      ACTIVE: 'アクティブ',
      BAND: '周波数帯域',
      CHANNEL: 'チャンネル',
      BANDWIDTH: '帯域幅',
      UTILIZATION: '利用率',
      RECEIVED: '受信',
      SENT: '送信',
      DOWNLINK_RATE: 'ダウンリンク速度',
      UPLINK_RATE: 'アップリンク速度',
      MODE: 'モード',
      CONNECTTIME: '接続時間',
      ERRORCODE: 'エラーコード',
      ERRORDESCRIPT: 'エラー説明',
      ERRORTIME: 'エラー時刻',
      DAILYSENT: '日次送信',
      DAILYRECEIVED: '日次受信',
      ACCESSCOUNT: '訪問回数',
      UNINSTALLEDTIME: 'アンインストール時刻',
      DATASIZE: 'データサイズ',
      CACHESIZE: 'キャッシュサイズ',
      SERVICEDISCOVERYSERVER: 'サービス検出サーバ',
      ACTICE_BCG_SERVERS: 'アクティブBCGサーバー',
      POWERCONSUMPTION: '消費電力',
      STATE: '状態',
      CONTAINERVERSION: 'コンテナバージョン',
      APPLICATIONVERSION: 'アプリケーションバージョン',
      ENABLE: '有効',
      CELL_RESERVED_FOR_OPERATOR_USE: 'オペレーター用に予約されたセル',
      EUTRA_CARRIER_ARFCN: 'EUTRAキャリアARFCN',
      BLACKLISTED: 'ブラックリスト入り',
      VENDORCLASSID: 'ベンダークラスID',
      EARFCNDOWNLOAD: 'EARFCNダウンロード',
      DOWNLOADBANDWIDTH: 'ダウンロード帯域幅',
      UPLOADBANDWIDTH: 'アップロード帯域幅',
      REFERENCESIGNALPOWER: '参照信号電力',
      SECURITY: '安全性',
      SEVERITY: '重大度',
      ALARMID: 'アラームID',
      EVENTTYPE: 'イベントタイプ',
      EVENTTIME: 'イベント時刻',
      PROBABLECAUSE: '発生原因',
      SPECIFICPROBLEM: '具体的な問題',
      ACKUSER: '確認ユーザー',
      ACKTIME: '確認時刻',
      ADDITIONALTEXT: '追加テキスト',
      ADDITIONALINFORMATION: '追加情報',
      PEER: 'ピア',
      DURATION: '期間',
      CONNECT: '接続',
      START: '開始',
      END: '終了',
      UPLOAD: 'アップロード ',
      DOWNLOAD: 'ダウンロード',
      DOWNLOADDATAMODEL: 'データモデル全体をダウンロード',
      DOWNLOADALL: 'すべてダウンロード',
      DOWNLOADSELECT: '選択したコンテンツのダウンロード',
      TIME: '時間',
      UPLOADRESULT: 'アップロードテスト結果',
      DOWNLOADRESULT: 'ダウンロードテスト結果',
      EVENT: 'イベント',
      LOGLEVEL: 'ログレベル',
      REQUEST: 'リクエスト',
      CREATED: '作成日',
      IMEI: 'IMEI',
      MAC: 'MAC',
      IP: 'IP',
      RSSI: 'RSSI',
      SSID: 'SSID',
      BSSID: 'BSSID',
      APSN: 'AP S/N',
      APNAME: 'AP Name',
      APMAC: 'AP MAC',
      APIP: 'AP IP',
      LISTDESCRIPTION: 'シリアル番号、MAP、IPアドレスなどの一般情報を持つすべての許可されたデバイスをリストします。',
      SMALLCELL_LIST: 'スモールセルリスト',
      SMALLCELL_LISTDESCRIPTION: 'UE、PCI、GNB IDなどの特定の情報を持つ、無線アクセスネットワークに属するすべての許可されたスモールセルをリストします。',
      WIFI_AP_LIST: 'WiFi APリスト',
      WIFI_AP_LISTDESCRIPTION: 'クライアント、チャネル、チャネル利用率などの特定の情報を持つ、WiFi APネットワークに属するすべての許可されたWiFi APをリストします。',
      WIFI_MESH_LIST: 'WiFiメッシュリスト',
      WIFI_MESH_LISTDESCRIPTION: 'クライアント、チャネル、チャネル利用率などの特定の情報を持つ、WiFiメッシュネットワークに属するすべての許可されたWiFiメッシュAPをリストします。',
      CURRENTNUMBERS: '現在のアラーム番号。',
      ALARMMGMTDESCRIPTION: "デバイスによって報告されたすべてのアラームを、クリアされたもの、未クリアのもの、重大度、イベント時間、可能性のある原因を含めてリストします。",
      REGISTERDEVICE: 'デバイスを登録',
      REGISTERSMALLCELLDEVICE: 'スモールセルデバイスを登録',
      REGISTERAPDEVICE: 'WiFi APデバイスを登録',
      REGISTERNESHDEVICE: 'WiFiメッシュデバイスを登録',
      LIVEUPDATE: 'ライブアップデート',
      SPEEDTEST: '速度テスト',
      SPEEDTESTDESCRIPTION: "デバイスはTR-143を使用してアップロードおよびダウンロードの速度テストを行い、ネットワークパフォーマンスを測定し、最適なデータ転送速度を確保します。",
      FIVECORE: '5G コアネットワーク',
      FIVECORENETWORK: '5Gコアネットワーク',
      FIVECORENETWORK_DESCRIPTION: 'システム/設定で構成された5Gコアへのリンクを提供し、5GコアのURLを指定します。',
      CELL_THROUGHPUT: 'セルスループット',
      CELL_THROUGHPUT_DESCRIPTION: "スモールセルのダウンロードとアップロードのスループットに関する情報。",
      UE_LIST: 'UEリスト',
      UE_LIST_DESCRIPTION: '5Gコアネットワークに接続されているUE（ユーザー機器）および切断されたUEの状態、IMSI、IMEI、GNB ID、IPアドレスなどの詳細情報を含む。',
      UE_5QI_PACKET: 'UE 5QIパケット',
      UE_5QI_PACKET_DESCRIPTION: 'UEの5QIパケットのリストで、アップリンクおよびダウンリンクのさまざまなトラフィックメトリックとドロップ率を含む。',
      BULKDATAPROFILE_DESCRIPTION: "エイリアス、ステータス、URL、パラメータ、およびエンコードされたタイプなどの詳細情報を持つバルクデータプロファイルをリストします。",
      SOFTWAREMODULES_DESCRIPTION: "UUID、エイリアス、名前、URL、最終更新日時などの詳細情報を持つソフトウェアモジュールのリスト。",
      ONLINE_COUNT_DISTRIBUTION: 'オンラインデバイス',
      ONLINE_COUNT_DISTRIBUTION_DESCRIPTION: '各製品のオンラインデバイス数の分布チャート。',
      REGISTERED_COUNT_DISTRIBUTION: '登録デバイス',
      REGISTERED_COUNT_DISTRIBUTION_DESCRIPTION: '各製品の登録デバイス数の分布チャート。',
      CONNECTIVITYTEST: '接続テスト',
      REBOOT: '再起動',
      FACTORYRESET: '工場出荷時設定を適用',
      UPLOADLOG: 'ログをアップロード',
      UPGRADEFIRMWARE: 'アップグレード',
      GENERATEREPORT: 'レポートを生成',
      ADDFILE: 'ファイルポインタを追加',
      SETTING: '設定を管理',
      GENERAL: '一般',
      GENERALSTATUS: '一般的なステータス',
      OPERATION: '操作を割り当て',
      PROTOCOL: 'プロトコル',
      SELECTPROTOCOL: 'プロトコルを選択',
      NETCONFAUTH: 'NETCONFはパスワードおよび秘密鍵の認証をサポートし、パスワードまたは秘密鍵を入力します。',
      REGISTER: '登録',
      CONNECTIONREQ: '接続要求',
      TELEMETRY: 'テレメトリ',
      INFO: '情報',
      MAP: '地図',
      FAPCONNECTEDCLIENTS: 'FAP接続クライアント',
      FAPCONNECTEDCLIENTSDESCRIPTION: '接続されたFAPクライアント。',
      GENERALINFO: '一般情報',
      GENERALINFODESCRIPTION: "デバイスの一般情報（シリアル番号、モデル名、ソフトウェアバージョンなど）。",
      CELLULARSTATUS: 'セルラー状況',
      CELLULARSTATUSDESCRIPTION: 'サービス状況、アクセス技術、バンド、RSRP、RSRQ、RSRIなどのセルラー情報。',
      WORKFLOWLIST: 'ワークフローリスト',
      WORKFLOWLISTDESCRIPTION: '現在このデバイスに適用されているワークフローのリスト。',
      DATAUSAGE: 'セルラーデータ使用量',
      DATAUSAGEDESCRIPTION: 'セルラーデータ使用量の履歴チャート。',
      CLIENTS: 'クライアント',
      CLIENTDESCRIPTION: 'モバイル、ノートパソコン、タブレットの数などのクライアント。',
      UE: 'UE',
      UEDESCRIPTION: 'スモールセルに接続されているUEの総数。',
      SIMCARDINFO: 'SIMカード情報',
      SIMCARDINFODESCRIPTION: 'ステータス、ICCID、IMSI、IMPI、IMPUなどのSIMカード情報。',
      WIFISTATUS: 'WiFiラジオ状況',
      WIFISTATUSDESCRIPTION: "利用可能なすべてのWiFiバンド/インターフェースの現在のラジオ設定と状況。",
      WIFICHANNELUTLILIZATION: 'WiFiチャネル利用率',
      WIFICHANNELUTLILIZATIONDESCRIPTION: 'すべての利用可能なバンドのトラフィックからのチャネル負荷。',
      CONNECTEDHOSTS: '接続されたホスト',
      CONNECTEDHOSTSDESCRIPTION: 'デバイスに接続されているホストに関する情報。',
      REGSTATUS: '登録状況',
      REGSTATUSDESCRIPTION: '最終登録時間、最終切断時間、最終切断理由などの登録情報。',
      ERRORSTATUS: 'エラー状況',
      ERRORSTATUSDESCRIPTION: 'エラーの説明やエラー発生時刻など、デバイスが報告したエラーのリスト。',
      SPECTRUMDESCRIPTION: "NRバンド、ARFCN、TDDタイムスロットなど、特定のスペクトルでのスモールセルの展開モードに関する情報。",
      APPLIST: 'アプリケーションリスト',
      APPLISTDESCRIPTION: 'デバイスのアプリケーションに関する情報。',
      SERVICEPRO: 'サービスプロバイダー',
      SERVICEPRODESCRIPTION: 'デバイスのサービスプロバイダーリスト。',
      SPECTRUM: 'スペクトル',
      PLMNNEIGHBORLIST: '隣接/PLMNリスト',
      NEIGHBORLIST: '隣接リスト',
      NEIGHBORLISTDESCRIPTION: 'ANR隣接リスト、ハンドオーバーリスト、PLMNリストに関する情報。',
      HANDOVERLIST: '設定された隣人リスト',
      ANRNEIGHBORLIST: 'ANR隣接リスト',
      UTRANEIGHBORLIST: 'Utra隣接リスト',
      PLMNLIST: 'PLMNリスト',
      APPSTATUS: 'デバイスアプリの状況',
      APPSTATUSDESCRIPTION: 'デバイスのアプリケーションの電力消費。',
      LXCSTATUS: 'LXC状況',
      LXCSTATUSDESCRIPTION: 'デバイスのLinuxコンテナの状況。',
      SUBSCRIPTION: 'USP サブスクリプション',
      SUBSCRIPTIONDESCRIPTION: 'エイリアス、ステータス、通知タイプ、受信者などの詳細情報を含むUSP経由のサブスクリプションアイテムのリスト。',
      BULKDATAPROFILE: 'バルクデータプロファイル',
      SOFTWAREMODULES: 'ソフトウェアモジュール',
      CONTROLLERTRUSTROLE: 'コントローラ信頼ロール',
      CONTROLLERTRUSTROLEDESCRIPTION: '「別名、状態、権限、エントリなどの詳細を含むコントローラの信頼性のある役割を一覧表示する」',
      FIRMWAREIMAGES: 'ファームウェアイメージ',
      FIRMWAREIMAGESDESCRIPTION: '別名、ステータス、bootFailureLogなどの詳細を含むファームウェアイメージをリストする',
      BOOTFAILURELOG: "起動失敗ログ",
      AVAILABLE: "使用可能",
      DEVICEACTION: 'デバイスアクション',
      CURRENTALARMLIST: '現在のアラームリスト',
      ALARMLIST: 'アラームリスト',
      ACKALARM: 'アラーム確認',
      ALARMMGMT: 'アラーム管理',
      ALARMMGMDESCRIPTION: 'デバイスによって報告されたすべてのアラームを、クリアされたもの、未クリアのもの、重大度、イベント時間、可能性のある原因を含めてリストします。',
      CLEAREDTIME: 'クリアされた時間',
      HISTORYALARM: '履歴アラーム管理',
      CURRENTYALARM: '現在のアラームリスト',
      DATAMODEL: 'データモデル',
      SELECTDATAMODEL: 'データモデルを選択',
      DATANODE: 'データノード',
      DATANODEDESCRIPTION: "デバイスがAMPに報告したすべてのパラメータノードをツリー構造で表示します。",
      PARAMETERDATADESCRIPTION: "選択したパラメータノードの詳細情報、子ノード、パラメータ名、属性、パス、値など。",
      PARAMETERDATA: 'パラメータデータ',
      SELECTDATANODE: 'データノードからパラメータを選択してください',
      LOGS: 'ログ',
      LOG: 'ログ',
      SESSIONLOG: 'セッションログリスト',
      SESSIONLOGDESCRIPTION: 'AMPとデバイス間のセッションログのリスト。',
      SESSIONLOGRATE: '報告率',
      SESSIONLOGRATEDESCRIPTION: "過去24時間におけるデバイスの定期的な報告率（カウント）の統計。",
      SESSLOG: 'セッションログ',
      PENDINGLOG: '保留中の操作ログリスト',
      PENDINGLOGPENDINGLOGS: 'デバイス受信を待つ保留中の操作タスクのリスト。',
      OPERATIONLOGS: '操作ログリスト',
      OPERATIONLOGSDESCRIPTION: 'デバイスが実行したAMPから割り当てられた操作タスクのリストと報告された結果。',
      CALLLOG: '通話ログリスト',
      CALLLOGDESCRIPTION: 'ピア、タイプ、期間などの通話記録のリスト。',
      SPEEDTESTHISTORY: '速度テスト履歴',
      CONNECTIVITYTESTHISTORY: '接続テスト履歴',
      CONNECTIVITYTESTHISTORYDESCRIPTION: '接続テストの履歴。',
      DEVICEREPORTLIST: 'デバイスレポートリスト',
      DEVICEREPORTLISTDESCRIPTION: 'デバイスの重要なパラメータを含む生成された要約レポートのリスト。',
      PMLOG: 'PM KPIログ',
      PMLOGDESCRIPTION: 'スモールセルによって報告されたKPIデータの記録のリスト。',
      ADVANCE: '高度な',
      CBSDSTATUS: 'CBSD状況',
      CBSDSTATUSDESCRIPTION: 'SASプロバイダー、CBSD ID、GPSステータス、グラント状態など、市民ブロードバンドサービスデバイスに関する情報。',
      CBSDCONDIGS: 'CBSD構成',
      CBSDCONDIGSDESCRIPTION: 'CBSDシリアル番号、モデル、ソフトウェアバージョンなど、市民ブロードバンドサービスデバイスの構成。',
      TERMINAL: 'ターミナル',
      TERMINALDESCRIPTION: 'ターミナル経由でリモートリアルタイム操作をサポートするデバイスを提供。',
      COMMANDXML: 'コマンドXML',
      COMMANDXMLDESCRIPTION: 'デバイスコマンドXML。',
      ACSSTATUS: 'ACS状況',
      DEVICESTATUS: 'デバイス状況',
      SASTATUS: 'セル状況',
      RFCONTROL: 'セルRF制御',
      RFCONTROLDESCRIPTION: 'RF関連の設定とスイッチを提供。',
      ANTENNABEAM: 'アンテナビーム',
      BEAMMENU: 'ビームメニュー',
      ANTENNABEAMDESCRIPTION: "スモールセルのアンテナビーム角度を変更するための提供。",
      BEAMID: 'ビームID',
      GPSANTENNA: 'GPSアンテナ',
      GPSANTENNAPATH: 'GPSアンテナパス',
      ANTENNAPATH: 'アンテナパス',
      GPSANTENNADESCRIPTION: '外部または内部としてGPSアンテナパスを選択する提供。',
      DEPLOYMENTMODE: '展開モード',
      DEPLOYMENTMODEDESCRIPTION: 'デバイスの展開モード。',
      HWMONITOR: 'マシンダイアグノスティクス',
      HWMONITORDESCRIPTION: "スモールセルの診断を有効にする、CPU、温度、消費電力など。",
      RECONNECT: '再接続',
      DISCONNECT: '切断',
      DOWNLOADLOG: 'ログをダウンロード',
      REPEATLASTCMD: '最後のコマンドを繰り返す',
      CLEARSCROLLBACK: 'スクロールバックをクリア',
      CELLULARDIAGNOSTIC: 'Cellularインターフェース診断の要求',
      WIFIDIAGNOSTIC: 'WiFiインターフェイス診断をリクエスト',
      SYSHEALTHREBOOT: 'システム状態再起動の要求',
      DIFFRENTPROTOCOLNOTALLOWED: '2つ以上の異なるプロトコル・デバイスの一括登録は許可されていません',
      DEVICEERRCODE1: "発生しない製品へのデバイスの追加は許可されていません",
      DEVICEERRCODE2: "デバイスはすでに登録されています",
      DEVICEERRCODE3: 'シリアル番号が不正です',
      INVALIDPRODUCT: '無効な製品',
      DOFURBISHMENT: 'が更新され、すべてのデバイスデータが削除されます。',
      CONFURBISHMENT: '更新プロセス',
      MAXUES: '同時接続UEの最大数:',
      OSMERROR: "地図の読み込みエラー。インターネット接続を確認するか、後でもう一度お試しください。",
      GOOGLEMAPERROR: "Google Maps API の読み込みに失敗しました。Google Maps API キーまたはインターネット接続を確認してください。",
      PERMISSIONNUMBEROFENTRIES: "エントリ数",
      ACTION: {
        ASSIGN_OPERATION: '操作が割り当てられました',
        ASSIGN_OPERATION_SUCC: '操作の割り当てが成功しました。',
        ASSIGN_OPERATION_FAIL: '操作の割り当てに失敗しました。',
        ASSIGN_OPERATION_SUCCESS_MESSAGE: '操作が成功しました。',
        ASSIGN_OPERATION_WARNING_MESSAGE1: '待機中',
        ASSIGN_OPERATION_WARNING_MESSAGE2: '操作の取得を待っています。',
        ASSIGN_OPERATION_FAIL_MESSAGE: '操作の割り当てに失敗しました。',
        CONFIRM_LIVE_UPDATE: 'ライブアップデートを確認しますか?',
        DO_LIVE: 'デバイスの即時更新しますか',
        DO_LIVE_UPDATE: '選択したデバイスのライブアップデートしますか?',
        LIVESUCCESS: 'ライブアップデート成功!',
        LIVEFAIL: 'ライブアップデート失敗!',
        CONFIRM_REBOOT: '再起動 ',
        DOREBOOT: '再起動中は、デバイスが提供するすべてのサービス（リモート管理を含む）が数分間一時的に中断されます。',
        SELECTREBOOT: '再起動中は、選択されたデバイスが提供するすべてのサービス（リモート管理を含む）が数分間一時的に中断されます。',
        ABOUT_TO_REBOOT: '再起動します...',
        REBOOT_SUCCESS: '再起動成功!',
        WAIT_REBOOT: '再起動を待機しています...',
        REBOOTFAIL: '再起動失敗!',
        REBOOT_TIMED_OUT: '再起動がタイムアウトしました。',
        SHUTDOWN_SUCCESS: 'シャットダウン成功！',
        CONFIRMFACTORYRESET: '工場出荷時設定にリセット ',
        DOFACTORYRESET: 'デバイスは元の製造状態に復元され、すべてのユーザー設定が消去されます。',
        SELECTFACTORYRESET: '選択されたデバイスは元の製造状態に復元され、すべてのユーザー設定が消去されます。',
        ACTION_CONFIRM: '続行しますか？',
        ABOUT_TO_FACTORYRESET: '工場出荷時設定にリセットしようとしています...',
        FACTORYRESETSUCC: '工場出荷時設定に戻し成功!',
        FACTORYRESETFAIL: '工場出荷時設定に戻し失敗!',
        FACTORYRESET_TIMED_OUT: '工場出荷時設定へのリセットがタイムアウトしました。',
        CONFIRMUPLOADLOG: 'ログをアップロード',
        DOUPLOADLOG: 'デバイスにシステムログをリモートURLにアップロードするようリクエストします。アップロードURLは、製品およびシステムページの設定のファイルセクションで構成できます。',
        SELECTUPLOADLOG: '選択されたデバイスにシステムログをリモートURLにアップロードするようリクエストします。アップロードURLは、製品およびシステムページの設定のファイルセクションで構成できます。',
        UPLOADLOGSUCC: 'ログのアップロード成功!',
        UPLOADLOG: "ログをアップロード",
        UPLOADLOGSUCCMESSAGE: "デバイスの操作が正常に作成されました！",
        UPLOADLOGFAIL: 'ログのアップロード失敗!',
        ABOUT_TO_UPLOADLOG: 'ログをアップロードしようとしています...',
        UPLOADLOG_TIMED_OUT: 'アップロードログがタイムアウトしました。',
        CONFIRMFIRMWARE: 'アップグレード',
        SUCCESSFIRMWARE: 'アップグレード成功！',
        UPGRADE_BEING_DOWNLOADED: 'ダウンロード中...',
        ASSIGN_UPGRADE_OPERATION: "アップグレード操作を割り当て",
        UPGRADE_STATUS_INSTALLING: 'インストール中...',
        UPGRADE_BEING_ACTIVATED: 'アクティブ化中...',
        UPGRADE_DEVICE_BEING_RESTARTED: 'デバイスを再起動しています...',
        WAIT_UPGRADE: 'インフォームを待機中...',
        UPGRADING: 'アップグレード中...',
        UPGRADE_COMPLETE_REBOOTING: 'アップグレードが完了しました。再起動しています',
        UPGRADE_TIMED_OUT: 'アップグレードがタイムアウトしました!',
        UPGRADEFIRMWARE: 'アップグレード',
        UPGRADEFIRMWARESUCCMESSAGE: "ファームウェアのアップグレードのデバイス操作が正常に作成されました！",
        FAILFIRMWARE: 'アップグレード失敗！',
        FAILFIRMWAREPENDING: 'アップグレード保留操作ログ。',
        REBOOTPENDING: '再起動は保留中の操作ログです。',
        FACTORYRESETPENDING: '工場出荷時設定に戻しは保留中の操作ログです。',
        UPLOADLOGPENDING: 'ログのアップロードは保留中の操作ログです。',
        DOFIRMWARE: 'ファームウェアのアップグレード中は、デバイスが提供するすべてのサービス（リモート管理を含む）が数分間一時的に中断されます。',
        SELECTFIRMWARE: 'ファームウェアのアップグレード中は、選択されたデバイスが提供するすべてのサービス（リモート管理を含む）が数分間一時的に中断されます。',
        CONFIRM_GENERATE: '生成 ',
        CONFIRM_REPORT: ' レポート',
        CONFIRM_GENERATE_REPORT: 'レポートを生成',
        DO_GENERATE_REPORT: 'デバイスの主要なパラメータをまとめたレポートを生成します。レポートは、ログページのデバイスレポートリストウィジェットに表示されます。',
        SELECT_GENERATE_REPORT: '選択されたデバイスの主要なパラメータをまとめたレポートを生成します。レポートは、ログページのデバイスレポートリストウィジェットに表示されます。',
        SUCCESSGENE: 'レポート生成成功!',
        FAILGENE: 'レポート生成失敗!',
        CONFIRM_DELETE: '削除を確認',
        DO_DELETE: '選択したデバイスの削除しますか?',
        CONFIRM_APPLY: '適用を確認',
        DO_APPLY_CONFIGRUATION: 'この設定を適用しますか？',
        IMPORT_JSON_FILE_MESSAGE: '設定の JSON ファイルをインポートしてください。',
        APPLY_SUCC: '適用成功！',
        APPLY_FAIL: '適用失敗！',
        PLESESELECT: 'デバイスの選択してください',
        CONFIRMReset: 'リセットを確認',
        SUREReset: 'リセットしてもよろしいですか?',
        RESETPERSONALTHEME: 'すべてのページの個人テーマをリセットしてもよろしいですか?',
        RESETCURRENTPERSONALTHEME: '現在のページの個人用テーマをリセットしてよろしいですか?',
        RESETALL: '全てのページをリセット',
        RESETCURRENTPAGE: '現在のページをリセット',
        RESETSuccess: 'Widgetレイアウトをリセットしました',
        CONFIRMSave: '保存を確認',
        SURESave: '現在の編集を保存してもよろしいですか?',
        SAVESuccess: 'Widgetレイアウトの保存成功',
        DODELETE: '削除しますか',
        DELETESUCCESS: '削除成功',
        DELETEFAIL: '削除失敗!',
        CONFIRMBAN: '禁止を確認',
        BANSUCCESS: '禁止成功!',
        BANFAIL: '禁止失敗!',
        DOBAN: '禁止しますか',
        BANSELECT: '選択したデバイスの禁止しますか?',
        CONFIRMRegister: '登録を確認',
        ONLYVALID: '適法なデバイスが自動的に保持されるようにチェックします',
        SUCCESSRegister: 'デバイスの登録成功!',
        FAILRegister: 'デバイスの登録失敗!',
        DORegister: '登録しますか',
        FAIL: '失敗!',
        CONFIRMOPER: '操作ログの削除を確認',
        OPERSELECT: '選択した操作ログを削除しますか?',
        CONNECTIVITYTESTSELECT: '選択した接続テスト履歴を削除しますか?',
        CONNECTIVITYTEAllCONFIRM: '接続テスト履歴のクリーンアップを確認しますか?',
        CONNECTIVITYTEAll: '接続テスト履歴をクリーンアップしますか?',
        SPEEDTESTSTSELECT: '選択した速度テスト履歴を削除しますか？',
        SPEEDTESTAllCONFIRM: '速度テスト履歴を消去してもよろしいですか？',
        SPEEDTESTAll: '速度テスト履歴を消去しますか？',
        PLESEOPER: '完了した操作ログを選択してください',
        DOTAG: 'タグを削除しますか',
        TAGSUCC: 'タグの削除成功!',
        TAGFAIL: 'タグの削除失敗!',
        ADDTAGSUCC: 'タグの追加成功!',
        ADDTAGFAIL: 'タグの追加失敗!',
        UPDAGEDEVICE: 'デバイスの更新!',
        CONFIRMCANCEL: '操作キャンセルを確認',
        DOCANCEL: 'キャンセルしますか',
        CANCELSUCC: 'キャンセル成功!',
        CANCELFAIL: 'キャンセル失敗!',
        SELECTCANCEL: '選択した保留中の操作ログをキャンセルしますか?',
        PLEASECANCEL: '保留中の操作ログを選択してください',
        CONFIRMREPORT: 'レポートの削除を確認',
        SELECTREPORT: '選択したレポートログを削除しますか?',
        PLEASEREPORT: 'レポートログを選択してください',
        CONFIRMSESSION: 'セッションログの削除を確認',
        SELECTSESSION: '選択したセッションログを削除しますか?',
        PLEASESESSION: 'セッションログを選択してください',
        CONFIRMACK: 'アラームを確認する',
        SELECTACK: '選択したアラームを確認しますか?',
        DOACK: 'アラームを確認しますか',
        CONFIRMSAVELOCA: '場所の保存を確認',
        DOSAVELOCA: '場所を保存しますか?',
        CONFIRMRESETLOCA: '場所のリセットを確認',
        DORESETLOCA: '場所をリセットしますか?',
        SAVESUCC: '保存成功!',
        SAVEFAIL: '保存失敗!',
        RESETSUCC: "リセット成功！",
        RESETFAIL: "リセット失敗！",
        ACTIVESUCC: 'デバイスの有効化成功!',
        ACTIVEFAIL: 'デバイスの有効化失敗!',
        BANDEVSUCC: 'デバイスの禁止成功!',
        BANDEVFAIL: 'デバイスの禁止失敗!',
        WAITOTHER: '他の参加者がセッションに参加するのを待っています...',
        ACSTROUBL: 'ACSトラブルシューティング接続中',
        WAITCPE: 'CPEのトラブルシューティング接続を待機中',
        ACSCONNECT: 'ACSがトラブルシューティング接続を確立中',
        ACSSETTING: 'ACS設定のトラブルシューティング接続を待機中',
        CPEJOIN: 'CPEがトラブルシューティング接続に参加中',
        SESSESTABLISH: '接続が確立されました',
        CONFIRMTROUB: 'トラブルシューティングの切断を確認',
        DODISCONNECT: 'セッションを切断しますか',
        CONFIRMUSER: 'ユーザーの削除を確認しますか?',
        DOUSER: 'ユーザーを削除しますか',
        SUCCESS: '成功!',
        ERROR: 'エラー!',
        CONFLOGENTRY: 'ログエントリの削除を確認',
        UPLOAD_CONNECTIVITYTEST: '接続テストをアップロード中、お待ちください...',
        DOWNLOAD_CONNECTIVITYTEST: '接続テストをダウンロード中、お待ちください...',
        COMPLETE_CONNECTIVITYTEST: '接続テストが完了しました!',
        CONNECTIVITYTEST_URL: 'テストファイルのURL',
        UPLOAD_SPEEDTEST: 'アップロード速度テスト中、お待ちください...',
        DOWNLOAD_SPEEDTEST: 'ダウンロード速度テスト中、お待ちください...',
        COMPLETE_SPEEDTEST: '速度テストが完了しました！',
        SPEEDTEST_URL: 'テストファイルのURL',
        REQUESTUPDATEFAIL: 'アップデートのリクエストが失敗しました',
        CONFVERSION_NOTCHANGED: "構成バージョンが変更されていません。",
      },
      OPERATION_ACTION: {
        SELECT_OPERATION: 'プロファイルの選択',
        SELECT_WORKFLOW: 'ワークフローを選択',
        SELECT_Action_OR_WORKFLOW: "プロファイルまたはワークフローを選択します",
        ADD_OPERATION: 'プロファイルに追加',
        OPERATION_COUNT: 'カウント',
        EDIT_OPERATION: '操作を編集',
        EDIT_CONFIGURATION: '編集設定',
        OPERATION_DETAILS: '操作の詳細',
        SETUP_OPERATION: 'セットアッププロファイルの詳細',
        ADD_OPERATION_TO: 'プロファイルにオペレーションを追加',
        OPERATION_ACTIONS: '動作設定ファイル',
        ENTER_OPERATION_ACTIONS: '操作プロファイルを入力してください。',
        OPERATION_TYPE: '操作タイプ',
        SELECT_OPERATIONTYPE: '操作タイプを選択',
        EDIT_ACTIONS: '操作プロファイルの編集',
        MANUAL_OPERATION: '手動設定ファイル',
        MANUAL_WORKFLOW: '手動ワークフロー',
        COMPLETION_RATE: "完成率です"
      },
      AMP: 'AMP',
      GO: 'Go',
      COLLAPSE: '折りたたむ',
      COLLAPSEALL: 'すべて折りたたむ',
      EXPAND: '展開',
      EXPANDALL: 'すべて展開',
      DISCOVER: '検出',
      DISCOVERDATAMODEL: '発見: デバイスに選択したパラメータを更新するよう依頼',
      PATH: 'パス',
      REQUIRED: '必須',
      OPTIONALPARAM: 'オプションのパラメータはありません',
      SENDRESP: '応答を送信',
      ACCESSLIST: 'アクセスリスト',
      SELECTACCESSLIST: 'アクセスリストを選択',
      REPORTEDVALUE: '報告された値',
      INPUTLIST: '入力リスト',
      ADDEDITTAG: 'タグの追加/編集',
      ADDTAGS: 'タグを追加',
      ADDTAG: 'タグを追加',
      INPUTTAGNAME: 'タグ名を入力',
      TAGSLIST: 'タグリスト',
      EXISTTAG: '同じタグがすでに存在します!',
      ADDEDITLABEL: 'ラベルの追加/編集',
      INPUTLABELNAME: 'ラベル名を入力',
      LOCATION: '位置情報',
      HEALTHYSTATUS: '健全性状態',
      INFORMHISTORY: 'Inform履歴',
      SESSIONLOGINTERVAL: 'セッションログインターバル',
      SESSIONLOGINTERVALDESCRIPTION: "過去24時間におけるデバイスのAMPへの定期報告の間隔の履歴チャート。",
      TIMEINTERVAL_BETWEEN_CONSECUTIVE_INFORMS: "連続するインフォーム間の時間間隔",
      LOCATIONDESCRIPTION: "地図上のデバイスの位置を表示。",
      ONLINERATE: 'オンライン率',
      ONLINERATEDESCRIPTION: 'ペンタゴンレーダーチャートは、デバイスの健康状態を表示します。',
      RESET: 'リセット',
      EDITCOMPONENT: 'ウィジェットを追加',
      BULKLOGLIST: 'バルクログリスト',
      EDITFILE: 'ファイルを編集',
      FILTERNODE: 'ノードをフィルタ',
      SELECTACTION: '選択したデバイスへのアクション',
      PRODUCTINFO: '製品情報',
      CONNHISTORY: '接続履歴',
      LAST24: '(過去24時間)',
      WIFIUSAGE: 'WiFiチャネル使用状況',
      WIFIANALYZER: 'WiFiアナライザー',
      WIFIANALYZERDESCRIPTION: 'このデバイスの利用可能なチャネル/バンドの隣接APのWiFi RSSIチャート。',
      CELLSTATUS: '小型セルの状態',
      CELLSTATUSDESCRIPTION: "小型セルの状態、例えば、無線サービスの状態、PC、gNB ID、MCC、MNC、TACなど。",
      CELLHANDOVERSTATUS: '小型セルのハンドオーバー状態',
      CELLHANDOVERSTATUSDESCRIPTION: '小型セルのハンドオーバー状態。',
      COVERMAP: 'カバレッジマップ',
      COVERMAPDESCRIPTION: 'グループ内のデバイスの位置と無線カバレッジを表示します。',
      TOTALALARMSDESCRIPTION: "デバイスによって報告された警報の総数。",
      ALARMSDESCRIPTION: "重大度の異なる警報の総数、例えば、クリティカル、メジャー、マイナー、警告など。",
      CREATEMAP: '作成',
      SAVEMAP: '保存',
      SELECTIMAGE: '画像を選択',
      IMAGEPRE: '画像のプレビュー',
      MAPX: 'マップの寸法（X）',
      MAPY: 'マップの寸法（Y）',
      EDITMAP: '編集',
      M: 'm',
      ENGMODE: 'エンジニアモード',
      CLIMODE: 'CLIモード',
      CONFIG: '設定',
      PROVISIONING: 'プロビジョニング',
      DEVICEALARM24HRS: '24時間以内のアラーム',
      PMALARM24HRS: '過去24時間内のPMデータにおける異常検知。',
      PMSTATUS_GOOD: '過去24時間内に異常なPMデータはありません。',
      PMSTATUS_BAD: '過去24時間内にPMデータに異常があります。',
      PMSTATUS_NORMAL: '過去24時間内にPMデータはありません。',
      PMSTATUS_NOPERMISSION: 'PMデータへのアクセスが制限されています。',
      ADDTOGROUP: 'グループに参加',
      ADDDEVTOGROUP: 'デバイスをグループに追加',
      SELECTDEVICE: '選択したデバイス',
      RESTARTNOW: '変更を適用するために今すぐ再起動してください。',
      TAGSDESCRIPTION: 'デバイス管理を容易にするためのデバイスのカスタムタグ。',
      BATCHSAVE: 'バッチ保存',
      CPUUSAGEDESCRIPTION: "デバイスのCPU使用率のパーセンテージ。",
      MEMORYUSAGEDESCRIPTION: "デバイスのメモリ使用率（パーセンテージ）。",
      CPUUSAGECHART_DESCRIPTION: "デバイスのCPU使用率の歴史的チャート。",
      MEMORYUSAGECHART_DESCRIPTION: "デバイスのメモリ使用状況の歴史的チャート。",
      KPIDESCRIPTION: "小型セルのKPIの歴史的チャート、例えばRRC、UEコンテキスト、スループットなど。",
      PMPARAMDESCRIPTION: "小型セルのKPI、例えばRRC、UEコンテキスト、スループットなど。",
      ALARM_CURRENTNUMBERS: 'デバイスによって報告された警報の総数。',
      PRODUCTMODEL: '製品モデル',
      PRODUCTMODELDESCRIPTION: '現在のデバイスがどの製品に属するかを示します。',
      DEVICEOFFLINETIPS: 'デバイスは現在オフラインです。AMPは、デバイスによって最後に報告された情報を保持します。',
      DEVICENOTREGISTERTIPS: 'デバイスは新しく追加されたデバイスで、まだオンライン/プロビジョニングされていません。',
      ONLINESTATUS: 'オンライン状態',
      ONLINESTATUSDESCRIPTION: "デバイスのオンライン状態の歴史的チャート。",
      REGISTERSINGLEDEVICE: "単一デバイスの登録",
      REGISTERBATCHDEVICE: "バッチ・デバイスの登録",
      DOWNLOADSESSIONGCSV: 'CSV ダウンロード（すべてのフィールド）',
      DOWNLOADSESSIONGJSON: 'JSON ダウンロード（全内容）',
      DOWNLOADLATESTSESSIONGJSON: '最新のセッションログをダウンロードする',
      TOTALCLIENTHISTORY: "このデバイスの接続された WiFi クライアント",
      TOTALCLIENTHISTORY_DESCRIPTION: "クライアント接続数の歴史的チャート。",
      CLIENTSPERSSID: 'SSID ごとの WiFi クライアント',
      CLIENTSPERSSID_DESCRIPTION: 'SSID ごとに分類された WiFi クライアント接続数の概要を示す履歴チャート。',
      CLIENTSPERRADIO: 'ラジオごとの WiFi クライアント',
      CLIENTSPERRADIO_DESCRIPTION: "このデバイスのラジオ/バンドごとに分類されたWiFiクライアント接続数の概要の歴史チャート。",
      TRAFFICPERSSID: 'SSID ごとの WiFi トラフィック',
      TRAFFICPERSSID_DESCRIPTION: 'SSID ごとに分類された WiFi トラフィックの概要を示す履歴チャート。',
      TRAFFICPERRADIO: 'ラジオごとの WiFi トラフィック',
      TRAFFICPERRADIO_DESCRIPTION: 'このデバイスのラジオ/バンドごとに分類された WiFi トラフィックの概要を示す履歴チャート。',
      VIEWCHART: 'チャートを表示',
      LOGDETAIL: 'ログ詳細',
      TXPOWER: '送信出力',
      BEACONPERIOD: 'ビーコン周期',
      SSIDSWITHCLIENTS: 'WiFi SSID分布。',
      SSIDSWITHCLIENTS_DESCRIPTION: 'このデバイスに関連する WiFi クライアントの SSID 分布ネットワーク。',
      RSSIDISTRIBUTION: 'WiFi RSSI 分布',
      RSSIDISTRIBUTION_DESCRIPTION: 'このデバイスに関連する WiFi クライアントの RSSI 分布範囲。',
      WIFIRADIOTHROUGHPUT: 'WiFi ラジオデータ使用量',
      WIFIRADIOTHROUGHPUT_DESCRIPTION: "利用可能なすべてのWiFiバンド/インターフェースから現在のラジオのプロトコルとデータ使用量を表示するため。",
      OPERATINGCHANNELBANDWIDTH: '帯域幅',
      BYTESSENT: '送信バイト数',
      BYTESRECEIVED: '受信バイト数',
      PACKETSSENT: '送信パケット数',
      PACKETSRECEIVED: '受信パケット数',
      ERRORSSENT: '送信エラーパケット数',
      ERRORSRECEIVED: '受信エラーパケット数',
      SSIDLIST: 'WiFi SSID リスト',
      SSIDLIST_DESCRIPTION: "このデバイスのプリセットSSIDにおける有効化、WMM、帯域幅、およびデータ使用についての現在のWiFi状態。",
      MAXCLIENTS: '最大クライアント数',
      WMM: 'WMM',
      UTILIZATION_GREEN: '緑',
      UTILIZATION_YELLOW: '黄',
      UTILIZATION_RED: '赤',
      UTILIZATION_GREEN_DESCRIPTION: '利用率 <= 30%',
      UTILIZATION_YELLOW_DESCRIPTION: '30% < 利用率 <= 60%',
      UTILIZATION_RED_DESCRIPTION: '利用率 > 60%',
      MCS: 'MCS',
      STATUS: 'ステータス',
      INACTIVE: '非アクティブ',
      DISABLE: '無効化',
      WHITELISTED: 'ホワイトリスト',
      REFURBISHMENT: '整備する',
      NETWORKTOPOLOGY: "ネットワークトポロジー",
      NETWORKTOPOLOGY_DESCRIPTION: "すべての関連ノード情報をツリービューで表示します。",
      core_5g: {
        ue_info: 'UE情報',
        ue_info_DESCRIPTION: 'UE情報は、5Gコアネットワークに接続されているユーザー機器（UE）の詳細な情報を提供します。',
      },
      healthyStatus: {
        alarm_description: '24時間以内に発生した各クリティカルアラームについては10ポイントの減点が行われ、各メジャーアラームについては5ポイントの減点が行われます。',
        session_log_rate_description: 'デバイスの通常の運用サイクルと、過去24時間にAMPに提出されたレポートとの比率。',
        online_rate_description: '過去24時間の機器のオンライン率は、機器が稼働し接続されていた時間の割合を示します。',
        service_quality_description: "WiFi機器は現在接続されている品質の悪いクライアントごとに5ポイントの減点を適用し、スモールセルは24時間の平均RRCレートを測定してサービス品質を評価します。",
        service_active_description: '過去24時間にデバイスが再起動（ブートストラップ）された場合、20ポイントの減点が行われます。'
      },
      cableMedemInfo: {
        cable_medem_info: 'DOCSISステータス',
        cable_medem_info_description: 'ケーブルアクセスネットワークからのケーブルモデム情報を表示する'
      },
      ONTMediaInfo: {
        ont_media_info: 'ONTメディアステータス',
        ont_media_info_description: 'ONTデバイスから八進メディア情報を表示する'
      },
      batteryStatus: {
        battery_information: 'バッテリステータス',
        battery_description: 'バッテリーの情報（状態、温度、レベルなど）',
      },
      WIFIAPINFO: 'WiFi AP 情報',
      WIFIAPINFO_DESCRIPTION: '',
      WIFIAPINFO_APCONFIGVERSION: 'AP 設定',
      APCONFIG: 'AP 設定',
      APNCONFIG: 'APN 設定',
      CONFIGVER: "設定バージョン",
      WIFIAPINFO_APNAME: 'AP 名称',
      WIFIAPINFO_APROLE: 'AP 役割',
      WIFIAPINFO_APNCONFIGVERSION: 'APN 設定',
      EDITAPNAME: 'AP 名称の編集',
      WIFIAPINFODESCRIPTION: 'WiFi AP 情報、AP 役割、AP 名称、AP 設定バージョン、および APN 設定バージョンを含みます。',
      FIVEGLICENSE: 'ユニットソフトウェアライセンス',
      FIVEGLICENSEDESCRIPTION: "小型セルのライセンス情報（有効期限とサポートされているアイテムなど）",
      OPERATION_LOCATION: 'プロファイルの場所',
      OPERATION_SETUPLOCATION: 'プロファイル設定の場所',
      SEARCH_KEYWORD: "検索キーワード",
      SERVICE_STATUS: "サービス状態",
      NCI: 'NCI',
      GNB_ID: 'GNB ID',
      GNB_ID_LENGTH: 'GNB ID Length',
      MCC: 'MCC',
      MNC: 'MNC',
      AMF_IP_CONTROL_PLANE: 'AMF IP-Control-Plane',
      CELL_ID: 'Cell ID',
      TAC: 'TAC',
      NSSAI: 'NSSAI',
      PCI: 'PCI',
      PARAMETER: '「パラメータ」',
      REPORTINTERVAL: 'レポート間隔',
      TIMEREFERENCE: '時間参照',
      NUMBEROFRETAINEDFAILEDREPROTS: '「保持失敗レポート数」',
      ENCODINGTYPE: "「符号化タイプ」",
      REQUESTURIPARAMETER: "「URIパラメータを要求する」",
      NOTIFTYPE: '「通知タイプ」',
      REFERENCELIST: '参照リスト',
      RECIPIENT: 'コンテナ',
      COMPLETED_DESCRIPTION: "デバイスがAMPから割り当てられた操作を完了しました。",
      FAIL_DESCRIPTION: "デバイスによって実行されたすべての操作が失敗しました。",
      PARTIAL_DESCRIPTION: "デバイスによって実行された操作の一部が失敗しました。",
      CANCEL_DESCRIPTION: "実行前にAMPによって割り当てられた操作がキャンセルされました。",
      PENDING_DESCRIPTION: "デバイスがAMPから割り当てられた操作を受信するのを待っています。",
      INPROCESS_DESCRIPTION: "デバイスが実行した操作の結果をAMPに報告するのを待っています。",
      LOADMAPFAIL: 'マップタイルの読み込みに失敗しました。後でもう一度お試しください。',
      MISMATCHPROFILE: 'パラメーター "conGlobalProfile" と "conDeviceSpecificProfile" は構成と一致しません。',
      ENERGYSAVING: 'エネルギー管理',
      STARTTIME_MUST_BE_EARLIER: "開始時刻は終了時刻より前でなければなりません",
      STARTDATE_MUST_BE_EARLIER: "開始日は終了日より前でなければなりません。",
    },
    GROUPS: {
      WIFICLIENT: 'WiFiクライアント',
      TITLE: 'グループ',
      TOTAL: '合計グループ',
      LIST: 'グループリスト',
      LISTDESCRIPTION: 'AMP 内にデバイスを含む独立したユニットであり、バッチ操作、ネットワーク状況の監視などの特定の機能を担当します。',
      ADDGROUP: 'グループを追加',
      EDITGROUP: 'グループを編集',
      NAME: 'グループ名',
      ACCEPT: '受け入れる',
      MEMBERSTITLE: 'メンバー',
      MEMBERS: 'デバイス',
      PENDINGLOGS: '保留中のログリスト',
      PENDINGLOGSDESCRIPTION: 'デバイスの受信を待っている保留操作タスクの一覧。',
      OPERATIONLOGS: '操作ログリスト',
      OPERATIONLOGSDESCRIPTION: 'AMP によって割り当てられた、グループ内のデバイスが実行した操作タスクと報告された結果のリスト。',
      IMPORTTYPE: 'インポートタイプを選択',
      SELECTGROUP: 'グループ名を選択',
      CREATE: '作成',
      ADDDEVICE: 'デバイスを追加',
      INPUTTYPE: '入力タイプを選択',
      INPUTVALUE: '入力値',
      GROUPUPDATE: 'グループの更新',
      FILTERING: '不正なデバイスの消去',
      ADDTOGROUP: 'デバイスグループに追加',
      SELECTGROUPTYPE: 'グループタイプを選択',
      SEARCH: '検索',
      PENDINGOPER: '保留中の操作',
      OPERLOG: '操作ログ',
      PARAMNOTIFICATION: 'パラメータ通知',
      SELECTPARAMNOTIFICATION: 'パラメータ通知を選択',
      NONEXISTINGMEN: '存在しないデバイスを自動的に除外します',
      BYINPUT: '入力による',
      BYIMPORTFILE: 'インポートファイルによる',
      ADDMEMBER: 'デバイスを追加',
      FILTERMEMBERIN: '製品内のフィルタ条件',
      ENTERKEYWORDS: 'キーワードを入力',
      BYPRODUCT: '製品ごと',
      CONFIRM: '確認',
      DEVICECOUNT: 'デバイス',
      ONLINERATE: 'オンライン率',
      SERVICEAVAILABILITY: 'サービスの利用可能性',
      CREATEBY: '作成者',
      CREATETIME: '作成日時',
      ALARMCOUNT: 'アラームの数',
      ALARMCOUNTDESCRIPTION: 'クリティカル、メジャー、マイナー、警告などの異なる重大度のアラームの総数。',
      DRAGANDDROP: '選択した操作をドラッグして配置操作のシーケンスを設定します',
      GROUPACT: 'グループアクション',
      ADDMEMBY: 'インポートによるデバイスの追加',
      ADDMEMINPUT: '入力によるデバイスの追加',
      NETWORKTOPOLOGY: 'ネットワークトポロジー',
      CANTDOOPERATION: 'グループ内にデバイスが存在しません',
      ASSIGNOPERATION: '操作の割り当て',
      REASSIGNOPERATION: 'オペレーションの再実行',
      DIFFRENTPROTOCOLNOTALLOWED: '1つのグループに2つ以上の異なるプロトコルデバイスを追加することは許可されていません。',
      CONFIRMADDTOGROUPMEMBER: 'グループへの追加の確認',
      ONLYVALID: '合法的なデバイスのみが追加されます',
      DEVICEERRCODE1: '製品にはこのデバイスは含まれていません',
      DEVICEERRCODE2: 'デバイスはすでにグループに存在します',
      DEVICEERRCODE3: 'このデバイスは不正です',
      UE: 'UE',
      UEDESCRIPTION: 'このグループに含まれる小型セルに接続された UE の総数。',
      ONLINEDEVICESTITLE: 'オンラインデバイス',
      ONLINEDEVICESDESCRIPTION: 'グループに含まれるオンラインデバイスの総数。',
      WIFICLIENTDESCRIPTION: 'グループに含まれる WiFi クライアントの総数。',
      ALARMSDESCRIPTION: 'グループ内のデバイスから報告されたアラームの総数。',
      KPIDESCRIPTION: 'このグループに含まれる小型セルの RRC、UE コンテキスト、スループットなどの KPI の履歴チャート。',
      DEVICESLISTDESCRIPTION: 'このグループに含まれるすべてのデバイスのリスト。',
      GROUPSDELETED: '選択したグループは正常に削除されました。',
      RFMAP_MAX_WARNING: 'カバレッジマップは最大50台のデバイスのみをサポートしています。',
      SSIDLIST: 'WiFi SSIDリスト',
      SSIDLIST_DESCRIPTION: 'このグループに含まれるすべての SSID のリスト。',
      SSID: 'SSID',
      SECURITY: 'セキュリティ',
      CAPTIVEPORTAL: 'キャプティブポータル',
      MACACL: 'MAC ACL',
      ATF: 'ATF イネーブル',
      ATFPERCENTAGE: 'ATF',
      BEAMFORMING: 'ビームフォーミング',
      MAXCLIENTS: '最大クライアント数',
      WMM: 'WMM',
      BAND: 'バンド',
      BANDWIDTHCONTROL: '帯域幅制御',
      DOWNLOAD: '送信',
      UPLOAD: '受信',
      FOREIGNAPSLIST: '他のAPリスト',
      FOREIGNAPSLIST_DESCRIPTION: 'このグループに含まれない WiFi AP のリスト。',
      VLAN: 'VLAN',
      BSSID: 'BSSID',
      CHANNEL: 'チャンネル',
      RSSI: 'RSSI',
      NEARBYAPS: '近隣のAP',
      TIME: '時間',
      SSIDSWITHCLIENTS: '最も多くのクライアントを持つトップ6のWiFi SSID',
      SSIDSWITHCLIENTS_DESCRIPTION: 'このグループ内で最も多くのクライアント接続を持つWiFi SSID。',
      APSWITHCLIENTS: '最も多くのクライアントを持つトップ6のWiFi AP',
      APSWITHCLIENTS_DESCRIPTION: 'クライアントによって最も接続されたトップ6のWiFi AP。',
      APSWITHTRAFFIC: '最も高いトラフィックを持つトップ6のWiFi AP',
      APSWITHTRAFFIC_DESCRIPTION: 'データ送受信量の上位6つのAPの総数値。',
      CLIENTSPERSSID: "WiFi SSIDごとのクライアント数",
      CLIENTSPERSSID_DESCRIPTION: "グループに含まれるデバイスのWiFi SSID別クライアント接続数の概要を示す歴史的チャート。",
      CLIENTSPERRADIO: "WiFi ラジオごとのクライアント数",
      CLIENTSPERRADIO_DESCRIPTION: "グループに含まれるデバイスの無線周波数別クライアント接続数の概要を示す歴史的チャート。",
      TRAFFICPERSSID: "WiFi SSIDごとのトラフィック",
      TRAFFICPERSSID_DESCRIPTION: "グループに含まれるデバイスのSSID別トラフィックの概要を示す歴史的チャート。",
      TRAFFICPERRADIO: "ラジオごとのトラフィック",
      TRAFFICPERRADIO_DESCRIPTION: "グループに含まれるデバイスの無線周波数別トラフィックの概要を示す歴史的チャート。",
      BYTESRECEIVED: '受信',
      BYTESSENT: '送信',
      ENABLE: '有効にする',
      DISABLE: '無効にする',
      TAGSGROUPDESCRIPTION: 'デバイス管理を簡単にするためのグループのカスタムタグ。',
      COUNTRY: '国',
      SELECTCOUNTRY: '国を選択',
      STREETADDRESS: '住所',
      CITY: '市',
      STATE: '州',
      ZIPCODE: '郵便番号',
      TAGS: 'タグ',
      INPUTTAGNAME: 'タグ名を入力',
      ROAD: '道路/通り',
      HOUSE: '建物番号',
      GROUPDETAILS: 'グループ詳細',
      GROUPLOCATION: 'グループの場所',
      GROUPLOCATIONDESCRIPTION: 'マップ上でグループの位置を表示します。',
      EDITLOCATION: '場所を編集',
      INPUTLAT: '緯度を入力',
      INPUTLNG: '経度を入力',
      RESET: 'リセット',
      LATLNG: '緯度と経度',
      LAT: '緯度',
      LNG: '経度',
      LOCATION: '場所',
      VIEWLOCATION: 'グループ位置を表示',
      VIEWCOVERAGEMAP: 'カバレッジマップを表示',
      VIEWLOCATIONDESCRIB: 'グループ位置ウィジェットが開いているとき、列内のアイコンをクリックすると、グループの位置と情報を表示できます。',
      VIEWCOVERAGEMAPDESCRIB: 'カバレッジマップウィジェットが開いているとき、列内のアイコンをクリックすると、グループ内のデバイスの分布と情報を表示できます。',
      WIDGETNAME: 'ウィジェット名、クラスまたはサブクラス',
      COVERMAP: 'カバレッジマップ',
      COVERMAPDESCRIPTION: 'デバイスグループに属するすべてのデバイスの位置と無線カバレッジを表示します。',
      TOTALALARMSDESCRIPTION: 'グループに含まれるデバイスによって報告されたアラームの総数。',
      ALARMMGMT: 'アラーム管理',
      ALARMMGMTDESCRIPTION: 'クリア、未クリアのアラームとその重大度、イベント時間、推定原因を含む、グループ内のデバイスが報告したすべてのアラームを一覧表示します。',
      NETWORK: 'ネットワーク'
    },
    PRODUCTS: {
      LIST: '製品リスト',
      LISTDESCRIPTION: '包含設備的單位，具有特定功能，如訪問列表控制、默認配置參數和用戶帳戶的管理範圍。設備註冊和上線時必須提供此單位。',
      NETWORKRADIOACCESSLIST: '無線アクセスネットワークリスト',
      NETWORKRADIOACCESSLISTDESCRIPTION: '用於管理特定設備（如小型基站）的單位。出於網絡管理目的，還會創建同名的群組。',
      NETWORKWIFIAPLIST: 'WiFi AP ネットワークリスト',
      NETWORKWIFIAPLISTDESCRIPTION: '用於管理特定設備（如 WiFi AP）的單位。出於網絡管理目的，還會創建同名的群組。',
      NETWORKWIFIMESHLIST: 'WiFi メッシュネットワークリスト',
      NETWORKWIFIMESHLISTDESCRIPTION: '用於管理特定設備（如 WiFi Mesh AP）的單位。出於網絡管理目的，還會創建同名的群組。',
      ACTION: '製品アクション',
      ADD: '製品を追加',
      ADDRADIOACCESSNETWORK: '無線アクセスネットワークを追加',
      ADDWIFIAPNETWORK: 'WiFi AP ネットワークを追加',
      ADDWIFIMESHNETWORK: 'WiFi メッシュネットワークを追加',
      PERMISSION: '権限',
      DEVICEPERMISSION: 'デバイス権限',
      ADDPERMISSION: '許可リスト',
      PERMITTEDTYPE: 'アクセス制御',
      PROVISIONINGTYPE: '設定タイプ',
      SELECTTYPE: 'アクセス制御の選択',
      ALLOWALL: 'すべて許可',
      WHITELIST: '許可リスト',
      CREATEWHITELIST: '許可リストを作成',
      LABEL: 'ラベル名',
      LABELREQUIRED: 'このラベル名は必須です!',
      NAMEREQUIRED: 'この名前は必須です!',
      PRODUCTTYPE: '製品タイプ',
      SELECTPRODUCTTYPE: '製品タイプを選択',
      PRODUCTPICTURE: '製品画像',
      PRODUCTNAME: '製品名',
      ISPRODUCTNAME: 'この製品名は必須です!',
      ISPRODUCTCLASS: 'この製品クラスは必須です!',
      TARGETPRODUCT: '対象製品名',
      OBJECTNAME: 'オブジェクト名',
      ENTEROBJECTNAME: 'オブジェクト名を入力',
      PRODUCTDETAILS: '製品詳細',
      DETAILS: '詳細',
      CHOOSEFILE: 'ファイルを選択',
      PERMITEDDEVICE: '許可されたデバイス',
      DEVICELIMITS: 'デバイス制限',
      UPDATEDTIME: '更新された時間',
      EDITPRODUCT: '製品を編集',
      EDITRAN: '無線アクセスネットワークを編集',
      EDITAPN: 'WiFi AP ネットワークを編集',
      EDITMESH: 'WiFi Mesh ネットワークを編集',
      PRODUCTMODEL: '製品モデル',
      PRODUCTMODELDESCRIPTION: '製品モデル、説明、および画像',
      BYDEFAULT: 'デフォルトで選択',
      BYUPLOAD: '画像をアップロード',
      DATACOLLECT: 'データ収集',
      DATACOLLECTDESCRIPTION: 'データ収集を開始するにはオンにしてください。関連するデバイスのKPIチャートWidgetをデバイス情報ページで作成できます。',
      DATAEXPORTDESCRIPTION: "サードパーティのテレメトリシステムにパフォーマンス指標をエクスポートするためにオンにします。",
      PRODUCTNOTEXIST: '現在の製品は存在しません',
      DEVICETEXIST: 'デバイスはすでに存在します',
      DEVICEERRCODE1: 'プロジェクトに不正な文字列が含まれている',
      DEVICEERRCODE2: 'DelItemは存在しない',
      DEVICEERRCODE3: "このデバイスはすでに製品に存在します：",
      LABELORPATHEXISTS: "ラベル名またはパラメータパスはすでに存在します",
      PARSINGFAILED: "ファイルの解析に失敗しました.",
      FILETYPEERROR: "CSVファイルのみアップロード可能",
      EXPORTINITDEFAULTTOJSON: "JSONのダウンロード (デフォルト設定）",
      EXPORTINITDEFAULTTOCSV: "CSVのダウンロード (一括登録例）",
      EDITPROVISIONINGDEFAULTVALUE: "設定のデフォルト値の編集",
      TAGS: 'タグ',
      LOCATION: '場所',
      REGISTERDEVICE: 'デバイスを登録',
      NAME: '名前',
      NAMETOOLTIP: 'ユーザーが定義した製品名を入力',
      NETWORKNAME: 'ユーザーが定義したネットワーク名を入力',
      PICTURE: '画像',
      UEFORPRODUCT: '製品ごとのUE数',
      OUI: 'デバイスに従って正しいOUIを入力',
      DESCRIPTION: '説明',
      NETWORKDESCRIPTION: 'ネットワークに属するデバイスの特性や固有の属性を入力',
      PRODUCTDESCRIPTION: '製品に属するデバイスの特性や固有の属性を入力',
      PRODUCTCLASS: 'デバイスに従って正しい製品クラスを入力',
      PROCPELIMIT: '製品の容量を入力',
      NETCPELIMIT: 'ネットワークの容量を入力',
      OUIDESCRIPTION: 'AMPがアクセスデバイスを確認できるように、OUIは正確でなければならない',
      ALLOWALLDESCRIPTION: 'デバイスのOUI、製品クラスを確認する必要がある',
      PROALLOWLISTDESCRIPTION: 'デバイスのOUI、製品クラス、シリアル番号を確認する必要があり、製品作成後はシリアル番号でデバイスを登録する必要がある',
      NETALLOWLISTDESCRIPTION: 'デバイスのOUI、製品クラス、シリアル番号を確認する必要があり、ネットワーク作成後はシリアル番号でデバイスを登録する必要がある',
      PRODUCTCLASSDESCRIPTION: 'AMPがアクセスデバイスを確認できるように、製品クラスは正確でなければならない。例: Femtocell_5G_SA、EAP等',
      PRODEVICELIMITDESCRIPTION: '製品内のデバイスの容量数',
      NETDEVICELIMITDESCRIPTION: 'ネットワーク内のデバイスの容量数',
      PROVISIONINGTYPEDESCRIPTION: 'AMPがアクセスデバイスを確認できるように、プロビジョニングタイプは正確でなければならない'
    },
    ALARMS: {
      TOTAL: '総アラーム数',
      ALARMCOUNT: 'クリティカル/メジャー/マイナー/アラームの数',
      CRITICAL: '重大',
      MAJOR: '主要',
      WARNING: '警告',
      INDETERMINATE: '未定',
      MINOR: '軽微',
      CURRENTNUMBERS: '現在のアラームする数',
      SYSTEM: 'システムイベント',
      LIST: 'システムイベントリスト',
      DEVICE: 'デバイスのアラームする',
      DEVICELIST: 'デバイスアラームするリスト',
      ACKALARM: 'アラームする確認',
      ERRORLIST: 'デバイスエラーリスト',
      DEVICEEVENTTRACKING: 'デバイスイベントの追跡',
      DEVICEEVENTTRACKINGDESCRIPTION: 'デバイスに関連するAMPによって追跡される再起動、オフライン、リセットなどの重要なイベントのリスト。',
      NOTIFICATION: '通知',
      NOTIFICATIONLIST: '通知リスト',
      NOTIFICATIONLISTDESCRIPTION: 'ユーザーが特定の条件に基づいてSMTPまたはSNMPトラップ経由で通知を受け取るために作成したルールのリスト。',
      FORK: 'フォーク',
      FORKNOTIFICATION: '通知のフォーク',
      CLONE: 'クローン',
      CLONENOTIFICATION: '通知のクローン',
      EDITNOTIFICATION: '通知の編集',
      SETUPDETAILS: '詳細(セットアップの詳細)',
      ENTERDETAILS: '詳細を入力してください',
      GENERIC_TRAP_TYPE: "汎用トラップタイプ",
      SPECIFIC_TRAP_OID: "特定のトラップOID",
      VARIABLE_BINDINGS: '変数バインディング（オプション）',
      ALIASVALUE: 'エイリアス値',
      OlDNAME: '古い名前/名前',
      VARIABLE_BINDINGS_DESCRIPTION: 'Varbindsの値は、ステージで構成されたエイリアスリストに関連付けられています。',
      TARGET: '目標',
      EDITTARGET: '目標の編集',
      SELECTTARGET: '目標を選択',
      ENTERTARGET: '目標を入力してください。',
      TARGETREQUIRED: 'この目標ファイル名は必須です!',
      TARGETTYPE: '目標タイプ',
      TARGETDEVICESN: '目標デバイスSN',
      ISTARGETDEVICESN: 'この目標デバイスSNは必須です!',
      SCHEDULE: 'スケジュール',
      EDITSCHEDULE: 'スケジュールの編集',
      ENTERSCHEDULE: 'スケジュールを入力してください。',
      SCHEDULEDOWNLOAD: 'スケジュールのダウンロード詳細',
      STARTDATE: '開始日',
      ENDDATE: '終了日',
      STARTTIME: '開始時刻',
      ENDTIME: '終了時刻',
      ENTEROPERATION: '操作を入力してください',
      TRIGGER: 'トリガータイプ',
      SELECTTRIGGER: 'トリガータイプを選択',
      INFORMPARAM: 'Informパラメータ',
      CONDITION: '条件',
      SELECTCONDITION: '条件を選択',
      BUILDCONDITION: '条件を構築',
      PARAMCONDITION: 'パラメータ条件',
      SELECTPARAMCONDITION: 'パラメータ条件を選択',
      ATTACHED: '添付メッセージ',
      ADDITIONALPARAMINFO: '追加パラメータ情報',
      ADDITIONALPARAMINFO_DESCRIPTION: '通知メッセージに特定のパラメータ値を含む詳細情報。',
      NODE: 'ノード',
      ENTERNODE: 'ノードを入力',
      SELECTNODE: 'ノードを選択',
      VIEWNODE: 'NetConfノードを表示',
      REFERNODE: 'ノードを参照',
      REFERNODEREQUIRED: 'この参照ノードは必須です!',
      PARENTNODE: '親ノード',
      SELECTPARENTNODE: '親ノードを選択',
      CHILDNODE: '子ノード',
      ADDCHILDNODE: '子ノードを追加',
      SELECTCHILDNODE: '子ノードを選択',
      CHILDCONTENT: '子コンテンツ',
      CONTENT: 'コンテンツ',
      ENTERCONTENT: 'コンテンツを入力',
      CONFIG: '設定',
      NAMESPACE: '名前空間',
      ENTERNAMESPACE: '名前空間を入力',
      ALIAS: 'エイリアス',
      ENTERALIAS: 'エイリアスを入力',
      ADDATTACHED: '添付メッセージを追加',
      ADDDEVICEFALUT: 'デバイス障害パラメータを追加',
      SELECTDEVICEFAULTNAME: 'デバイス障害名を選択',
      BUILD: '操作の構築(オプション)',
      BUILDREQUIRED: '操作',
      PROGRESS: '進捗',
      ACTIONS: 'アクション',
      REPEAT: '繰り返し',
      REPEATTYPE: '繰り返しタイプ',
      UPLOADNOTIFI: 'アラーム通知をアップロード',
      DROPFILE: 'ここにファイルをドロップまたはクリック',
      UPLOADFORMATSARESUPPORTED: '.tar .tar.gz .tgz .zip .gzip 形式でのアップロードのみをサポートします。',
      UPLOADALL: 'すべてアップロード',
      UPLOAURL: 'デバイスログアップロードURL',
      BANDWIDTH: 'アップロード帯域幅',
      QUEUEPROGRESS: 'キュー進行状況',
      PARAMLIST: 'パラメータリスト',
      SELECT: '選択',
      ENTERSELECT: '選択(XPath式を入力)',
      SOURCE: 'ソース',
      SELECTSOURCE: 'ソースを選択',
      FILTERSTATE: 'フィルタ状態',
      SELECTFILTERSTATE: 'フィルタ状態を選択',
      FILTERTYPE: 'フィルタタイプ',
      SELECTFILTERTYPE: 'フィルタタイプを選択',
      REMOVEALL: 'すべて削除',
      REMOVE: '削除',
      UPDATEUSER: 'ユーザーを更新',
      UPDATEDBY: '更新者',
      LASTACTIVE: '最終アクティビティ',
      UPDATELOG: '更新ログ',
      NOTIF: '通知',
      ONLYONCE: '一度だけ',
      ALLSTATE: 'すべての状態',
      ALLSEVERITY: 'すべての重要度',
      ALLGROUPS: 'すべてのグループ',
      SEVERITY: '重要度',
      STATE: '状態',
      CLEAREDTIME: 'クリア時間',
      CLEARED: 'クリア',
      NOTCLEARED: 'クリアされていません',
      CLEAREDALARM: 'アラームをクリアしました',
      UNCLEAREDALARM: '未クリア',
      CHANGEDALARM: '変更アラーム',
      NEWALARM: '新しいアラーム',
      NOTIFICATIONTYPE: "通知タイプ",
      PROBABLECAUSE: "原因の可能性",
      SPECIFICPROBLEM: "具体的な問題",
      ADDITIONALTEXT: "追加テキスト",
      ADDITIONALINFORMATION: "追加情報",
      ALARMID: 'アラームID',
      EVENTTYPE: 'イベントタイプ',
      EVENTTIME: 'イベント時刻',
      ACKUSER: 'アクノリッジユーザー',
      ACKTIME: 'アクノリッジ時刻',
      ERRORCODE: 'イベント＃イベント＃',
      DEVICEFAULTPARAM: 'デバイス障害パラメータ',
      ATTACHEDMES: '添付メッセージ',
      BYTAG: 'ラベル別',
      RECEIVERLIST: '受信者リスト',
      RECEIVERCCLIST: '受信者CCリスト',
      RECEIVETAGSTOOLTIP: '特定のラベルを持つ受信者を追加',
      RECEIVERCCTAGSTOOLTIP: '特定のラベルを持つCCを追加する',
      EMAILSUBJECT: 'メールの件名',
      EMAILCONTENT: 'メールの内容',
      WITHACTIVEHOURS: 'アクティブ時間を含む',
      PRIPHONENUM: 'プライマリ電話番号',
      SECPHONENUM: 'セカンダリ電話番号',
      TEXTMESSAGE: 'テキストメッセージ',
      ACK: '確認',
      EDIT_STAGE_NOTIFICATIONS: 'ステージ/通知を使用して通知を構築する',
      TOTALALARMSDESCRIPTION: 'システムによって報告されたアラームの総数。',
      ALARMMGMT: 'アラーム管理',
      ALARMMGMTDESCRIPTION: 'システムによって報告されたすべてのアラームのリスト。クリア済み、未クリア、深刻度、イベント時間、推定原因が含まれています。',
      ALARMCOUNTDESCRIPTION: '重大、主要、軽度、警告といった異なる深刻度を持つアラームの総数。',
      TARGETDEVICETAG: 'ターゲットデバイスタグ',
    },
    PROVISIONING: {
      COLLAPSIBLE: 'プロビジョニング',
      WORKSFLOW: 'ワークフロー',
      WORKSFLOWLIST: 'ワークフローリスト',
      WORKSFLOWLISTDESCRIPTION: '特定の条件に基づいて特定のデバイスに対して操作を実行するためにユーザーが作成したルールのリスト。',
      CONFIGURATIONS: '構成',
      CONFIGURATIONLIST: '構成リスト',
      CONFIGURATIONLISTDESCRIPTION: '特定の条件に基づいてデバイスに対して設定を実行するために自動的またはユーザーによって作成されたルールのリスト。',
      POLICYLIST: 'エネルギー政策',
      POLICYLISTDESCRIPTION: '自動的に作成された省エネルールを一覧表示します。これらのルールは、省エネモードが有効な場合にデバイスの構成設定を実行します。',
      CLONEPOLICY: 'クローンポリシー',
      POLICYS: 'ポリシー',
      FROMWORKSFLOW: 'ワークフローから',
      FROM: 'から',
      VALIDFROM: '有効期間',
      CLONEWORKSFLOW: 'ワークフローの複製',
      CLONECONFIGURATION: 'クローニングワークフロー',
      EDITWORKSFLOW: 'ワークフローの編集',
      FORKWORKSFLOW: 'ワークフローの分岐',
      UPLOADWORKSFLOW: 'ワークフローのアップロード',
      UPLOADQUEUE: 'アップロードキュー',
      OPERATIONS: '操作',
      PROFILES: 'プロファイル',
      ACTIONS: 'アクション',
      CLONEOPERATIONS: 'プロファイルのクローンを作成する',
      FORKOPERATIONS: 'フォークプロファイル',
      EDITOPERATIONS: '操作の編集',
      OPERATIONLIST: 'プロファイルリスト',
      OPERATIONLISTDESCRIPTION: '特定の条件に基づいて特定のデバイスに対して操作を実行するためにユーザーが作成したルールのリスト。',
      DUOPERATION: 'DU操作の追加',
      PARAPATH: 'パラメータパス',
      ENTERPARAPATH: 'パラメータパスを入力',
      ISPARAPATH: 'パラメータパスは必須です。',
      NEXTLEVEL: '次のレベル',
      PRODUCT: '製品',
      SCRIPTS: 'スクリプト',
      SCRIPTLIST: 'スクリプトリスト',
      EDITSCRIPT: 'スクリプトの編集',
      SCRIPTNAME: 'スクリプト名',
      FILES: 'ファイル',
      FILELIST: 'ファイルリスト',
      FILELISTDESCRIPTION: 'デバイスがファイルのアップロードまたはダウンロードを行う際に必要な情報（ファイルタイプ、URL、認証など）を含む、ユーザーが作成したリスト。',
      FILETYPE: 'ファイルタイプ',
      SELECTFILETYPE: 'ファイルタイプを選択',
      ENTERFILETYPE: 'ファイルタイプを入力',
      ISFILETYPE: 'このファイルタイプは必須です!',
      ISURL: 'このURLは必須です!',
      DELAYSECONDS: '遅延秒数',
      TARGETNAME: 'ターゲットファイル名',
      ENTERTARGETNAME: 'ターゲットファイル名を入力',
      FILESIZE: 'ファイルサイズ',
      DESCRIPTION: '説明',
      SUBSCRIBE: '購読',
      SUBSCRIBETOPIC: '購読トピック',
      SELECTTOPIC: '購読トピックを選択',
      VENDORFILE: 'ベンダー固有のファイル',
      VENDORFILEDESCRIPTION: 'デバイスがファイルのアップロードまたはダウンロードを行う際に必要な、ベンダー固有の情報（ファイルタイプ、URL、認証など）を含む、ユーザーが作成したリスト。',
      ADDVENDORFILE: 'ベンダー固有のファイルを追加',
      EDITVENDORFILE: 'ベンダー固有のファイルの編集',
      LATESTFIRMWARE: '最新のファームウェア',
      AVAILABLEFILES: '利用可能なファイル',
      SETUPDETAILS: 'セットアップの詳細',
      CODEDISTRIBUTION: 'Provisioning Code配布',
      OPERATENAME: '操作名',
      ENTEROPERATENAME: '操作名を入力',
      ADDINFORM: 'Informパラメータの追加',
      PUBLICTOPIC: '公開トピック',
      SELECTPUBLICTOPIC: '公開トピックを選択',
      ISDATAMODEL: 'このデータモデルは必須です!',
      CPELIMIT: 'CPE制限',
      ISCPELIMIT: 'このCPE制限は必須です!',
      SUMMARYACTION: 'サマリーアクション',
      ADDSUMMARYREPORT: 'サマリーレポートの追加',
      SUMMARYREPORT: 'サマリーレポート',
      SUMMARYREPORTSETTING: 'サマリーレポートの設定',
      PLEASESELECTTEMPLATE: '続行する前にテンプレートを選択してください',
      PLEASEFILLPARAMS: '続行する前にラベル名とパラメータパスを入力してください',
      INFORMLIST: 'Informパラメータリスト',
      SELECTTRIGGERREQ: 'トリガーを選択(必須)',
      DEVICEPARAMLIST: 'デバイスパラメータリスト',
      ADDSTAGE: 'ステージ',
      ENTERSTAGENAME: 'ステージ名を入力',
      SELECTFILE: 'ファイルを選択',
      SUCCESSURL: '成功URL',
      FAILURL: '失敗URL',
      NOTIFYTYPE: '通知タイプ',
      SELECTNOTIFYTYPE: '通知タイプを選択',
      NOTIFYPARAMS: '通知パラメータ',
      SHOWDETAILS: '詳細を表示',
      NOTIFYTYPEREQU: '通知タイプを選択してください(必須)',
      EDITNOTIFYPARA: '通知パラメータを編集',
      OBJECTPATH: 'オブジェクトパス',
      ENTEROBJECTPATH: 'オブジェクトパスを入力',
      ALLOWPAR: '部分的な許可',
      ADDCREATEOBJ: 'オブジェクトの作成を追加',
      ISOBJECTNAME: 'オブジェクト名は必須です!',
      FILETARGET: 'ファイルターゲット',
      SELECTSN: 'SNを選択',
      GENERSUMMARYREPORT: 'サマリーレポートの生成',
      SCRIPT: 'スクリプト',
      SELECTSCRIPT: 'スクリプトを選択',
      ACTIONSLIST: 'アクションリスト',
      PARAMTYPE: 'パラメータタイプ',
      ADDITIONALCON: 'トリガー条件',
      ADDCONDITION: '追加の条件を追加',
      EDITCONDITION: '追加の条件を編集',
      DEVICEPARAMTRIGGER: 'デバイスパラメータトリガー',
      INFORM: 'Inform',
      DURATION: '期間',
      FIELD: 'フィールド',
      TRIGGEREVENTS: 'トリガーエ​​ベント',
      TRIGGERTYPE: 'トリガータイプ',
      INFORMEVENT: 'Informイベント',
      SELECTINFORMEVENT: 'Informイベントを選択',
      EVENTNAME: 'イベント名',
      ENTEREVENTNAME: 'イベント名を入力',
      PARAMETERSKEY: 'パラメータパラメータキー',
      ENTERPARAMETERSKEY: 'パラメータキーを入力',
      PARAMETERSVALUE: 'パラメータ値',
      ENTERPARAMETERSVALUE: 'パラメータ値を入力',
      PROTOCOLVER: 'プロトコルバージョン',
      SOURCEURL: 'ソースURL',
      TARGETURL: 'ターゲットURL',
      SESSIONID: 'セッションID',
      OPERATEMODE: '操作モード',
      SELECTOPERATEMODE: '操作モードを選択',
      INPUTURLFORMATE: 'サポートされる形式: http / https / ftp / ftps / sftp',
      HISTORY: '歴史',
      WORKFLOWHISTORY: 'プロセス履歴',
      CONFIGURATIONHISTORY: '構成履歴',
      POLICYHISTORY: 'ポリシー履歴',
      TRIGGERTIME: "トリガ時間",
      TARGETPRODUCT: '対象商品',
      TARGETGROUP: 'ターゲットグループ',
      TARGETSOFTWAREVERSION: 'ターゲットソフトウェアバージョン',
      TARGETSN: 'ターゲットデバイスのシリアル番号',
      TARGETSV: 'ターゲットデバイスのソフトウェアバージョン',
      SUPPORTEDPRODUCT: '対応製品',
      MANAGEDEDPRODUCT: '管理製品',
      ALWAYSACTIVE: "常にアクティブ",
      EXIMMEDIATELY: "すぐに実行",
      EXMODE:"実行モード",
      SELECTACTIVEDATERANGE: "アクティブな日付と時間の範囲を選択してください",
      DAYOFWEEK: '曜日あり',
      WITHONLYONCE: '一度だけで',
      ACTIVEDATERANGE: 'アクティブな日付範囲',
      ACTIVETIMERANGE: '一日のアクティブ時間範囲',
      EVERYDAY: '毎日',
      SUNDAY: '日曜日',
      MONDAY: '月曜日',
      TUESDAY: '火曜日',
      WEDNESDAY: '水曜日',
      THURSDAY: '木曜日',
      FRIDAY: '金曜日',
      SATURDAY: '土曜日',
      EXECUTIONSTATUS: '実行ステータス',
      EXECUTIONTIME: '実行時間',
      EDITACTIONS: '編集操作',
      DOWNLOADASMULTI: 'マルチファイルとしてダウンロード',
      DOWNLOADASONE: '1つのファイルとしてダウンロード',
      LASTEXECUTIONTIME: '最終実行時間',
      SEARCHSN: '検索SN',
      ACTIVATE: '有効化',
      DEACTIVATE: '無効化',
      LOADING: '読み込み中',
      STATETYPE: '状態タイプ',
      STAGE: 'ステージ',
      EDIT_STAGE_OPERATIONS_DESCRIPTION: 'ステージ/操作を使用してワークフローを構築する',
      EDIT_CONFIGURATION_STAGE_OPERATIONS_DESCRIPTION: 'フェーズ/アクションを使用した構成の構築',
      EDIT_POLICY_STAGE_OPERATIONS_DESCRIPTION: 'ステージ/オペレーションによるポリシーの構築',
      TRIGGERCONDITIONS: 'トリガー条件',
      BUILD: 'ビルド',
      SETUP_SCHEDULE_DESCRIPTION: 'アクティブな時間範囲の設定',
      INVALID_VALUE_MESSAGE: '無効な値があります。フォームを確認してください。',
      RESET_TOOLTIP: 'デフォルトにリセット',
      RESET_CONFIRM: '初期設定値をすべてリセットしますか？',
      COUNT: '執行次數',
      COMPLETEDCOUNT: '完了',
      PARTIALFAILEDCOUNT: '部分的に失敗しました',
      CANCELEDCOUNT: 'キャンセル',
      INPROGRESS: '処理中',
      FAILCOUNT: 'に失敗',
      ADDTAGFAIL: 'タグの追加に失敗しました!',
      ADDTAGSUCC: 'タグの追加に成功しました!',
      DELTAGFAIL: 'タグの削除に失敗しました!',
      DELTAGSUCC: 'タグの削除に成功しました!',
      STEPAFTERSUCCESS: "失敗した場合は、次のタスクを停止します。",
      WORKFLOWOPERATIONLOG: 'ワークフロー操作ログ',
      OPERATIONLOGS: '操作ログ'
    },
    USERS: {
      ACCOUNT: 'アカウント',
      ONLINEUSERS: 'オンラインユーザー',
      ONLINEUSERSDESCRIPTION: "現在使用中または30分以内にログインしたユーザーの総数",
      PROFILE: 'プロファイル',
      PROFILEDESCRIPTION: '現在ログインしているユーザーの詳細情報。',
      STATUS: '状態',
      ALLSTATUS: 'すべてのステータス',
      ALLTYPE: '全タイプ',
      ROLE: '役割',
      ROLELIST: 'ロールのリスト',
      ROLELISTDESCRIPTION: 'ユーザー権限管理のための役割リスト。',
      CANNOTFINDROLE: "ロール権限リストが見つかりません",
      CHANGE: '変更',
      ACCOUNTLIST: 'アカウントリスト',
      ACCOUNTLISTDESCRIPTION: '現在ログインしているユーザーが管理できるアカウントリスト。',
      EDITUSER: 'ユーザーの編集',
      EXPIRATION: '有効期限日',
      DEPLOYEXPIRATION: '配備の期限切れ',
      CONTROLLIST: 'アクセス制御リスト',
      ALLEDITABLE: 'すべて編集可能',
      EDITABLE: '編集可能',
      ALLREADONLY: 'すべて読み取り専用',
      READONLY: '読み取り専用',
      ALLDISABLED: '全員無効化',
      DISABLED: '無効',
      SUBMIT: '提出',
      AMPNODE: 'AMPノード',
      SUPERADMINPASSWORD: 'スーパー管理者のパスワード',
      ACTIVITIES: 'アカウントログ',
      ACTIVITIESLIST: 'アカウントアクティビティリスト',
      ACTIVITIESLISTDESCRIPTION: '現在ログインしているユーザーが管理できるアカウントのすべてのリクエストイベントを記録。',
      DATERANGE: '日付範囲の選択',
      BEGINTIMEDATERANGE: '開始日を選択',
      ENDTIMEDATERANGE: '終了日を選択',
      DASHPERMISSION: 'ダッシュボード権限',
      CONFIRMPASSWORD: 'パスワードの確認',
      NOTMATCH: 'パスワードと確認用パスワードが一致しません。',
      NOTMATCH2: '新しいパスワードと確認用パスワードが一致しません。',
      ISPASSWORD: 'パスワードが必要です。',
      ISUSERNAME: 'ユーザー名は必須です。',
      ADDUSER: 'ユーザーの追加',
      USERROLE: 'ユーザーの役割',
      CONFIRMPSW: 'パスワードは8～128文字で、少なくとも次の文字、数字、記号を含んでいなければなりません。',
      SPECIALSYMBOLS: '使用できる文字、数字、特殊記号(@!#?$/\\_-.)のみ1〜32文字を入力してください。',
      SPECIALSYMBOLS_NO_DASH: '使用できる文字、数字、特殊記号(@!#?$/\\_.)のみ1〜128文字を入力してください。',
      ADDNEWUSER: '新しいユーザーの追加',
      USERACTION: 'ユーザーアクション',
      LANGUAGE: '言語',
      AUTHORITYLIST: 'Widgetクラスの権限リスト',
      CHANGEPASSWORD: 'パスワードの変更',
      APIDOCUMENT: 'APIドキュメント',
      USERMANUAL: 'マニュアル',
      OLDPASSWORD: '旧パスワード',
      NEWPASSWORD: '新しいパスワード',
      PREVIOUSTIME: '最終ログイン時間',
      PREVIOUSLOCATION: '最終ログイン場所',
      LASTLOGINLOCATION: '最後のログイン場所',
      CURRENTTIME: '最新のログイン時間',
      CURRENTLOCATION: '最新のサインイン場所',
      EDITROLE: '役割の編集',
      ADDNEWROLE: '新しい役割の追加',
      AUTHORITY: 'ロールテンプレート',
      EMAIL: 'メールアドレス',
      ACTIONTYPE: 'アクションタイプ',
      EMAILERROR: 'メールボックスのフォーマットが正しくありません',
      ISMAIL: 'メールは必要です。',
      TAGHASCREATED: 'このラベルは他のユーザーによって作成されました',
      DEVICEADMIN_TITLE: 'デバイス管理',
      DEVICEADMIN_DESCRIPTION: 'デバイスクラス/デバイス管理サブクラスに分類されるデバイス関連Widgetの編集/読み取り権限。含まれるWidget：デバイスリスト、ライブ更新、再起動、ファームウェアのアップグレード。',
      PRODUCTADMIN_TITLE: '製品管理',
      PRODUCTADMIN_DESCRIPTION: 'デバイスクラス/製品管理サブクラスに分類される製品関連Widgetの編集/読み取り権限。含まれるWidget：登録済みデバイス、製品リスト。',
      GROUPADMIN_TITLE: 'グループ管理',
      GROUPADMIN_DESCRIPTION: 'デバイスクラス/グループ管理サブクラスに分類されるグループ関連Widgetの編集/読み取り権限。含まれるWidget：グループリスト、デバイスをグループに追加、操作を追加。',
      GENERALDATA_TITLE: '一般データ',
      GENERALDATA_DESCRIPTION: 'デバイスクラス/一般データサブクラスに分類されるデバイス一般データ関連Widgetの編集/読み取り権限。含まれるWidget：オンラインデバイス、グループ、製品モデル、一般情報、KPI。',
      ALARMMANAGEMENT_TITLE: 'アラーム管理',
      ALARMMANAGEMENT_DESCRIPTION: 'デバイスクラス/アラーム管理サブクラスに分類される特定デバイスのアラーム管理Widgetの編集/読み取り権限。含まれるWidget：アラーム管理。',
      REMOTETROUBLESHOOTING_TITLE: 'リモートトラブルシューティング',
      REMOTETROUBLESHOOTING_DESCRIPTION: 'デバイスクラス/リモートトラブルシューティングサブクラスに分類されるリモート高度関連Widgetの編集/読み取り権限。含まれるWidget：端末、コマンドXML。',
      DATAMODEL_TITLE: 'データモデル',
      DATAMODEL_DESCRIPTION: 'デバイスクラス/データモデルサブクラスに分類されるデータモデル関連Widgetの編集/読み取り権限。含まれるWidget：データノード、パラメータデータ。',
      NETWORKLOCATION_TITLE: 'ネットワークロケーション',
      NETWORKLOCATION_DESCRIPTION: 'デバイスクラス/ネットワークロケーションサブクラスに分類されるネットワークロケーション関連Widgetの編集/読み取り権限。含まれるWidget：ロケーション、ネットワークトポロジ、カバレッジマップ。',
      LOGCOLLECTION_TITLE: 'ログ収集',
      LOGCOLLECTION_DESCRIPTION: 'デバイスクラス/ログ収集サブクラスに分類されるログ収集関連Widgetの編集/読み取り権限。含まれるWidget：セッションログリスト、レポート生成、操作ログリスト。',
      STATISTICALANALYSIS_TITLE: '統計分析',
      STATISTICALANALYSIS_DESCRIPTION: 'デバイスクラス/統計分析サブクラスに分類される統計分析Widgetの編集/読み取り権限。',
      WIFISPECIFIC_TITLE: 'WiFi特定',
      WIFISPECIFIC_DESCRIPTION: 'デバイスクラス/WiFi特定サブクラスに分類されるWiFi特定関連Widgetの編集/読み取り権限。含まれるWidget：WiFiクライアント、WiFi無線状態、WiFiアナライザー、WiFi隣接リスト。',
      CELLULARSPECIFIC_TITLE: 'セルラー特定',
      CELLULARSPECIFIC_DESCRIPTION: 'デバイスクラス/セルラー特定サブクラスに分類されるセルラー特定関連Widgetの編集/読み取り権限。含まれるWidget：UE、セル状態、アンテナビーム、隣接/PLMNリスト。',
      PMKPICOUNTER_TITLE: 'PM KPIカウンタ',
      PMKPICOUNTER_DESCRIPTION: 'デバイスクラス/PM KPIカウンタサブクラスに分類されたPM KPIカウンタウィジェットの権限を編集/読み取ります。含まれるウィジェット：PMグラフ、PMパラメータ。',
      FAPSPECIFIC_TITLE: 'FAP特定',
      FAPSPECIFIC_DESCRIPTION: 'デバイスクラス/FAP特定サブクラスに分類されるFAP特定関連Widgetの編集/読み取り権限。',
      APPSPECIFIC_TITLE: 'アプリケーション特定',
      APPSPECIFIC_DESCRIPTION: 'デバイスクラス/アプリケーション特定サブクラスに分類されるアプリケーション特定関連Widgetの編集/読み取り権限。含まれるWidget：アプリケーションリスト、サービスプロバイダー、デバイスアプリケーションステータス。',

      YANGMODULE_TITLE: 'Yang モジュール',
      YANGMODULE_DESCRIPTION: 'デバイスクラス/Yang モジュールサブクラスに分類されるYang モジュールWidgetの編集/読み取り権限。',
      POWERSAVING_TITLE: '省電',
      POWERSAVING_DESCRIPTION: 'デバイスクラス/省電サブクラスに分類される省電関連Widgetの編集/読み取り権限。含まれるWidget：エネルギー管理',
      DOCSIS_TITLE: 'Docsis 特定',
      DOCSIS_DESCRIPTION: 'デバイスクラス/Docsis 特定サブクラスに分類されるDocsis 特定関連Widgetの編集/読み取り権限。含まれるWidget：DOCSISステータス',
      DEVICEALARM_TITLE: 'デバイスアラーム',
      DEVICEALARM_DESCRIPTION: 'アラームクラス/デバイスアラームサブクラスに分類されるデバイスアラームWidgetの編集/読み取り権限。含まれるWidget：総アラーム数、アラーム管理（ダウンロード/確認）、デバイスイベントトラッキング。',
      NOTIFICATIONMANAGMENT_TITLE: '通知管理',
      NOTIFICATIONMANAGMENT_DESCRIPTION: 'アラームクラス/通知管理サブクラスに分類される通知管理Widgetの編集/読み取り権限。含まれるWidget：通知リスト。',
      WORKFLOWSETUP_TITLE: 'ワークフロー/構成設定',
      WORKFLOWSETUP_DESCRIPTION: 'プロビジョニングクラス/ワークフロー設定サブクラスに分類されるワークフロー/構成設定Widgetの編集/読み取り権限。含まれるWidget：ワークフロー/構成リスト、ワークフロー/構成履歴。',
      OPERATIONSETUP_TITLE: 'オペレーション設定',
      OPERATIONSETUP_DESCRIPTION: 'プロビジョニングクラス/オペレーション設定サブクラスに分類されるオペレーション設定Widgetの編集/読み取り権限。含まれるWidget：アクションリスト。',
      POLICYSETUP_TITLE: 'ポリシー設定',
      POLICYSETUP_DESCRIPTION: 'プロビジョニングクラス/ポリシー設定サブクラスに分類されるポリシー設定Widgetの編集/読み取り権限。含まれるWidget：エネルギー政策。',
      SCRIPTSETUP_TITLE: 'スクリプト設定',
      SCRIPTSETUP_DESCRIPTION: 'スクリプト設定Widgetの編集/読み取り権限。',
      FILESETUP_TITLE: 'ファイル設定',
      FILESETUP_DESCRIPTION: 'プロビジョニングクラス/ファイル設定サブクラスに分類されるファイル設定Widgetの編集/読み取り権限。含まれるWidget：ファイルリスト、特定ベンダーのファイル。',
      ACCOUNTADMIN_TITLE: 'アカウント管理',
      ACCOUNTADMIN_DESCRIPTION: 'ユーザークラス/アカウント管理サブクラスに分類されるアカウント管理Widgetの編集/読み取り権限。含まれるWidget：プロファイル、アカウントリスト。',
      ACCOUNTLOG_TITLE: 'アカウントログ',
      ACCOUNTLOG_DESCRIPTION: 'ユーザークラス/アカウントログサブクラスに分類されるアカウントログWidgetの編集/読み取り権限。含まれるWidget：アクティビティリスト。',
      ACCOUNTROLE_TITLE: 'アカウントロール',
      ACCOUNTROLE_DESCRIPTION: 'ユーザークラス/アカウントロールサブクラスに分類されるアカウントロールWidgetの編集/読み取り権限。含まれるWidget：ロールリスト。',
      DEVICESTATISTICS_TITLE: 'デバイス統計',
      DEVICESTATISTICS_DESCRIPTION: '解析クラス/デバイス統計サブクラスに分類されるデバイス統計Widgetの編集/読み取り権限。含まれるWidget：オンラインデバイス、新規デバイス、イベントコード。',
      SYSTEMSTATISTICS_TITLE: 'システム統計',
      SYSTEMSTATISTICS_DESCRIPTION: '解析クラス/システム統計サブクラスに分類されるシステム統計Widgetの編集/読み取り権限。含まれるWidget：DB ステータス、デバイスセッション時間、デバイスセッションレート、空きメモリ。',
      PROVISIONINGSTATISTICS_TITLE: 'プロビジョニング統計',
      PROVISIONINGSTATISTICS_DESCRIPTION: '解析クラス/プロビジョニング統計サブクラスに分類されるプロビジョニング統計Widgetの編集/読み取り権限。含まれるWidget：プロビジョニングコード、ソフトウェアバージョン、SIM ステータス。',
      PMSTATISTICS_TITLE: 'PM 統計',
      PMSTATISTICS_DESCRIPTION: '解析クラス/PM 統計サブクラスに分類されるPM 統計Widgetの編集/読み取り権限。含まれるWidget：PM、PM ステータス、パフォーマンスレポート。',
      SERVERSETTING_TITLE: 'サーバ設定',
      SERVERSETTING_DESCRIPTION: 'システムクラス/サーバ設定サブクラスに分類されるサーバ設定Widgetの編集/読み取り権限。含まれるWidget：一般、ライブ更新、CWMP、Netconf、パフォーマンスサービス。',
      SERVERPREFERENCE_TITLE: 'サーバ設定',
      SERVERPREFERENCE_DESCRIPTION: 'システムクラス/サーバ設定サブクラスに分類されるサーバ設定Widgetの編集/読み取り権限。含まれるWidget：SMTP 通知、SNMP トラップ通知、レポート、統計、ログ。',
      SERVERLICENSE_TITLE: 'サーバライセンス',
      SERVERLICENSE_DESCRIPTION: 'システムクラス/サーバライセンスサブクラスに分類されるサーバライセンスWidgetの編集/読み取り権限。含まれるWidget：ライセンス。',
      SERVERREPORT_TITLE: 'サーバレポート',
      SERVERREPORT_DESCRIPTION: 'アドオンクラス/レポートエクスポートサブクラスに分類されるサーバレポートWidgetの生成/読み取り権限。含まれるWidget：サマリーレポートの生成。',
      SYSTEMEVENTS_TITLE: 'システムイベント',
      SYSTEMEVENTS_DESCRIPTION: 'システムクラス/システムイベントサブクラスに分類されるシステムイベントWidgetの編集/読み取り権限。含まれるWidget：システムイベント、登録ログ。',
      SYSTEMNODES_TITLE: 'システムノード',
      SYSTEMNODES_DESCRIPTION: 'システムクラス/システムノードサブクラスに分類されるシステムノードWidgetの編集/読み取り権限。含まれるWidget：ノード。',
      PERSONALTHEME_TITLE: '個人テーマ',
      PERSONALTHEME_DESCRIPTION: 'アドオンクラス/個人テーマサブクラスに分類される個人テーマWidgetの編集/読み取り権限。',
      REPORTEXPORT_TITLE: 'レポートエクスポート',
      REPORTEXPORT_DESCRIPTION: 'アドオンクラス/レポートエクスポートサブクラスに分類されるレポートエクスポートWidgetの編集/読み取り権限。含まれるWidget：サマリーレポートの生成。',
      SYSTEMINFORMATION_TITLE: 'システム情報',
      SYSTEMINFORMATION_DESCRIPTION: 'システムクラス/システム情報サブクラスに分類されるAMPシステム情報Widgetの編集/読み取り権限。含まれるWidget：システム情報。',
      FURBISHMENTSTATISTICS_TITLE: '更新統計',
      GENERAL_TITLE: '一般',
      GENERAL_DESCRIPTION: '5Gコアクラス/一般サブクラスに分類された一般ウィジェットを編集/読み取る権限。',
      UE_TITLE: 'ユーザ機器',
      UE_DESCRIPTION: '5Gコアクラス/ユーザ機器サブクラスに分類されたユーザ機器ウィジェットを編集/読み取る権限。',
      CELL_TITLE: 'セル',
      CELL_DESCRIPTION: '5Gコアクラス/セルサブクラスに分類されたセルウィジェットを編集/読み取る権限。',
      ALARM_TITLE: 'アラーム',
      ALARM_DESCRIPTION: '5Gコアクラス/アラームサブクラスに分類されたアラームウィジェットを編集/読み取る権限。',
      SYSTEM_TITLE: "システム",
      SYSTEM_DESCRIPTION: "5GCシステムに関連するアクションやウィジェットを編集/閲覧する権限。",
      TOTALREQUESTS_TITLE: 'トータルリクエスト',
      TOTALREQUESTS_DESCRIPTION: '過去24時間の総リクエスト数',
      EXTERNAL_DEVICE_TITLE: 'デバイス',
      EXTERNAL_DEVICE_DESCRIPTION: '外部クラス/デバイスサブクラスのNBI権限の編集/読み取り権限。',
      CBSD_DEVICE_TITLE: 'デバイス',
      CBSD_DEVICE_DESCRIPTION: 'CBSDクラス／デバイスサブクラスNBI権限の編集／読み取り権限。',
      REQUESTSHISTORY_TITLE: "リクエスト履歴",
      REQUESTSHISTORY_DESCRIPTION: "過去24時間の総リクエスト数の履歴チャート",
      IPREQUESTDISTRIBUTION: "IP要求プロファイル",
      IPREQUESTDISTRIBUTION_DESCRIPTION: "過去24時間で各IPアドレスのリクエスト数トップ5",
    },
    ANALYSIS: {
      COLLAPSIBLE: '分析',
      SYSTEM: 'システム統計',
      SESSIONDURATION: 'デバイスセッション時間',
      SESSIONRATE: 'デバイスセッションレート',
      LATENCY: 'デバイス要求レイテンシ',
      REQUESTRATE: 'デバイス要求レート',
      PARSING: 'デバイス要求解析',
      MEMORY: 'メモリ使用',
      SPACEUSAGE: 'スペース使用',
      CPUUTILIZE: 'CPU使用率',
      MEMORYUSAGECHART: 'メモリ使用グラフ',
      FREEMEMORY: '空きメモリ',
      CPUUSAGE: 'CPU使用率',
      CPUUSAGECHART: 'CPU使用率グラフ',
      FREEDISK: '空きディスク',
      DEVICE: 'デバイス統計',
      PM: 'PM統計',
      TOTALDEVICE: '総デバイス数',
      NEWDEVICE: '新デバイス',
      SESSIONS: 'セッション',
      EVENTCODE: 'イベントコード',
      MEMORYUTILIZATION: 'メモリ利用率',
      DISKUTILIZATION: 'ディスク利用率',
      MEMORYUTILIZATIONDESCRIPTION: 'メモリ利用率の履歴グラフ。',
      DISKUTILIZATIONDESCRIPTION: 'ディスク利用率の履歴グラフ。',
      SESSIONDURATIONDESCRIPTION: 'すべてのデバイスのセッション時間の平均履歴グラフ。セッション時間: デバイスとAMP間でのセッションに費やされた合計時間。',
      SESSIONRATEDESCRIPTION: 'すべてのデバイスのセッションレートの平均履歴グラフ。セッションレート: デバイスがAMPに毎秒開始するセッション数。',
      LATENCYDESCRIPTION: 'すべてのデバイスの要求レイテンシの平均履歴グラフ。要求レイテンシ: デバイスとAMP間の要求に費やされた合計時間。',
      REQUESTRATEDESCRIPTION: 'すべてのデバイスの要求レートの平均履歴グラフ。要求レート: デバイスがAMPに毎秒開始する要求数。',
      PARSINGDESCRIPTION: 'すべてのデバイスの要求解析の平均履歴グラフ。要求解析: デバイスからの要求をAMPが解析するのに費やされた合計時間。',
      MEMORYDESCRIPTION: '空きメモリの履歴グラフ。',
      CPUUSAGEDESCRIPTION: 'CPU使用率の履歴グラフ。',
      FREEDISKDESCRIPTION: '空きディスクの履歴グラフ。',
      TOTALDEVICEDESCRIPTION: '定期的に計測されたデバイス総数の履歴グラフ。',
      ONLINEDEVICEDESCRIPTION: '定期的に計測されたオンラインデバイス総数の履歴グラフ。',
      NEWDEVICEDESCRIPTION: '定期的に計測された新しく登録されたデバイス数の履歴グラフ。',
      SESSIONSDESCRIPTION: '各製品ごとのセッション総数の履歴グラフ。',
      EVENTCODEDESCRIPTION: '各製品ごとのイベントコード総数の履歴グラフ。',
      PROVISIONING: 'プロビジョニング統計',
      PMSTATISTICS: 'PM統計',
      STATUSFORDEVICES: 'オンラインデバイスのステータス',
      RATE: 'レート',
      NUMBER: '数',
      VERSIONDISTRIBUTION: 'ソフトウェアバージョン分布',
      CODEDISTRIBUTION: 'プロビジョニングコード分布',
      XMPPSTATUS: 'XMPPステータス',
      XMPPSTATUS_DESCRIPTION: 'XMPPサービスのステータス。',
      IMSSTATUS: 'IMS登録ステータス',
      SIMSTATUS: 'SIMステータス',
      IPSECSTATUS: 'IPSecトンネルステータス',
      STATUSFORDEVICE: 'デバイスのステータス',
      DBSTATUS: 'データベースステータス',
      DBSTATUS_DESCRIPTION: 'データベースサービスのステータス。',
      SELECTDURATION: '期間を選択',
      PMSTATUS: 'PMステータス',
      PMSTATUS_DESCRIPTION: 'PMサービスのステータス。',
      REFURBISHMENETHISTORY: '再整備履歴'
    },
    SYSTEM: {
      COLLAPSIBLE: 'システム',
      EXTERNALSERVICE: '外部サービス',
      PERFORMANCESERVICE: 'パフォーマンスサービス',
      PERFORMANCESERVICEDESCRIPTION: 'デバイスがKPIファイルをアップロードするためのデフォルトURLなど、PM関連設定。',
      PREFERENCE: '設定',
      DBSERVER: 'DBサーバー',
      SOURCES: 'データソース',
      SNMPTRAP: 'SNMPトラップ通知',
      SNMPTRAP_DESCRIPTION: 'SNMP経由の通知に関する設定。',
      SMTP: 'SMTP通知',
      SMTP_DESCRIPTION: 'SMTP経由の通知に関する設定。',
      SMS: 'SMS通知',
      STATISTICSPRE: '統計',
      STATISTICSPRE_DESCRIPTION: 'AMPにおけるデータ収集の回転に関する設定。',
      LOGPRE: 'ログ',
      LOGPRE_DESCRIPTION: 'デバイスセッションおよび操作のログ回転設定。',
      FAULT: 'アラーム',
      FAULT_DESCRIPTION: '自動確認やアラームの回転など、アラームに関する設定。',
      REPORTS: '報告書',
      REPORTS_DESCRIPTION: 'デバイス概要報告書の回転設定。',
      SYSREPORT: 'サーバー報告書リスト',
      GENERATESYSREPORT: 'サーバー報告書を生成',
      CONFIRMDELETE: 'すべてのサーバー報告書を削除することを確認',
      DOCONFIRMDELETE: 'すべてのサーバー報告書を削除してもよろしいですか',
      CONFIRMDELETESELECT: '選択したサーバー報告書を削除することを確認',
      DOCONFIRMDELETESELECT: '選択したサーバー報告書を削除してもよろしいですか',
      DOWNLOADCSV: 'CSVをダウンロード',
      DOWNLOADTXT: 'TXTをダウンロード',
      DOWNLOADXLSX: 'XLSXをダウンロード',
      LICENSE: 'ライセンス',
      LICENSE_DESCRIPTION: '状態、種類、サポートされるプロトコル、有効期限などのAMPライセンスに関する情報。ユーザーがライセンスを更新して有効期限を延長したり、AMPの機能を有効にしたりできます。',
      LICENSETYPE: 'バージョン',
      LICENSESTATE: 'ライセンスの状態',
      KEY_DESCRIPTION: "鍵を入力した後にAMPを起動する機能と、鍵に関する情報を提供します。",
      CPENUM: 'せつびのようりょう',
      SESSIONNUM: 'セッション番号',
      REMAININGTIME: '残り時間',
      VALIDTO: '有効期限',
      DISPLAY: '表示',
      GENERAL: '一般',
      GENERAL_DESCRIPTION: 'セッションログレベル、ログアウトタイムアウト、サーバー報告などのシステム一般設定。',
      CWMP: 'CWMP',
      CWMP_DESCRIPTION: 'CWMPプロトコルに関連する設定（セッションタイムアウトやオンライン基準など）。',
      NETCONF: 'Netconf',
      NETCONF_DESCRIPTION: 'NETCONFプロトコルに関連する設定（リトライ間隔やキープアライブ間隔など）。',
      USP: 'USP',
      USP_DESCRIPTION: 'WebsocketまたはMQTT経由でのUSPプロトコルに関連する設定。',
      LIVEUPDATE: 'ライブ更新',
      LIVEUPDATE_DESCRIPTION: 'AMPと特定のデバイス間の通信を即座に開始するための機能。',
      MQTT: 'システムMQTT',
      FILES: 'ファイル',
      FILES_DESCRIPTION: 'FOTAおよびデバイスログURLと認証など、システムデフォルトファイル設定。',
      TELEMETRY: 'システムテレメトリ',
      SUMMARYREPORT: '概要報告書',
      SUMMARYREPORT_DESCRIPTION: '地図に関連する設定（地図データプロバイダーやソースの選択など）。',
      EVENTS: 'イベント',
      SYSEVENTS: 'システムイベント',
      SYSEVENTS_DESCRIPTION: 'AMP関連のすべてのイベントをリストし、重大性、特定の問題、考えられる原因、イベント時間などの詳細情報を提供。',
      REGISTRATIONLOG: '登録ログ',
      REGISTRATIONLOG_DESCRIPTION: 'USP MQTT レジスタログ',
      NODES: 'ノード',
      DEL_TITLE_NODES: 'ノードを削除しますか',
      NODES_DESCRIPTION: 'ノード名、IPアドレス、AMPバージョン、稼働時間などのAMPノード情報をリスト。',
      ENTERLICENSEKEY: 'ライセンスキーを入力',
      LICENSEKEY: 'ライセンスキー',
      KEY: 'かぎ本',
      UNIQUEID: '一意のID',
      KEYVALIDITY: '鍵の有効性',
      EDITURL: 'URLを編集',
      VERIFYSMTP: 'メール送信テスト',
      VERIFYSNMP: 'SNMP送信テスト',
      SERVICESTATUSTRACKING: 'サービスステータストラッキング',
      KPIFACTORS: 'KPI要素',
      KPIFACTORSTRACKING: 'トラッキング用KPI要素',
      VERIFYXMPP: 'XMPPテスト',
      MAP_DESCRIPTION: '地図',
      PROCESS: 'プロセス',
      NODEID: 'ノードID',
      SEVERITY: '重大性',
      LOCATIONMAP: '位置マップ',
      LOCATIONMAP_DESCRIPTION: '地図に関連する設定（地図データプロバイダーやソースの選択など）。',
      FIVECORESERVICE: '5Gコアサービス',
      FIVECORESERVICE_DESCRIPTION: '5GコアベンダーおよびサーバーURLなど、5Gコアサービスに関連する設定。',
      ENERGYMANAGEMENT: "エネルギー管理",
      ENERGY_DESCRIPTION: "電力制限やスリープ間隔など、エネルギー管理に関する設定です。",
      NIDS: 'NIDS',
      NIDS_DESCRIPTION: 'ネットワーク侵入検知システム。',
      PROMETHEUS: 'メトリック収集 - Prometheus',
      PROMETHEUS_DESCRIPTION: 'Prometheusを使用したデータ収集に関連する設定。',
      PROMETHEUS_PARAMETER_DESCRIPTION: {
        PULL_PATH: 'メトリクスを収集するためのURL。',
        USERNAME: '各Prometheusメトリクスの取得要求におけるAuthorizationヘッダーのユーザー名。',
        PASSWORD: '各Prometheusメトリクスの取得要求におけるAuthorizationヘッダーのパスワード。',
      },
      KAFKA: 'メトリック収集 - Kafka',
      KAFKA_DESCRIPTION: 'Kafkaを使用したデータ収集に関連する設定。',
      KAFKA_PARAMETER_DESCRIPTION: {
        BROKERS: 'Kafka ブローカーの URL。',
        TOPIC: 'プロデューサー メッセージの Kafka トピック。',
        ROUTING_KEY: 'メッセージ ルーティング メカニズム。',
        ACCESS_TOKEN: '認証用のトークン。'
      },
      NODE_DESCRIPTION: {
        UPGRADE: 'アップグレード',
        ADDRESS: 'アドレス',
        HASHKEY: 'ハッシュキー',
        TYPE: 'タイプ',
        USERNAME: 'ユーザー名',
        PASSWORD: 'パスワード',
        TARGEVERSION: 'Targe 版',
        COMPOENT: 'コンポーネント',
        RUNTOOL: '実行ツール',
        UPGRADESUCC: 'こうしんせいこう',
        UPGRADEFAIL: 'アップグレードの失敗',
        WAITFORUPDATE: 'アップグレードファイルをダウンロード',
        STARTUPDATING: 'アップグレードを開始します...',
        BEINGUPDATED: 'アップグレードが完了し、再起動を待機しています',
        SERVERRESTART: 'サーバーを再起動しています...',
        TIMEOUT: 'アップグレードタイムアウト!',
        ACSNODEREBOOT: 'この AMP ノードを再起動すると、データが失われる可能性があります。',
        ACSNODESHUTDOWN: "この AMP ノードをシャットダウンすると、データが失われる可能性があります。",
      },
      SETTING_DESCRIPTION: {
        GENERAL: {
          SESSIONTIMEOUT: 'CWMPセッションの設定可能なタイムアウトです。',
          TRANSACTIONTIMEOUT: 'トランザクションタイムアウトは、要求と応答のタイムアウトです。',
          REFRESHINTERVAL: 'Netconfリトライ間隔',
          KEEPALIVEINTERVAL: 'Netconfキープアライブ間隔',
          SESSIONLOGLEVEL: 'この設定はデバイスセッションの詳細なログ表示に影響します:ORIGINALの場合、セッションはSOAPエンベロープとして表示されます。RPCの場合、解析されたSOAPメッセージとして表示されます。',
          DEVICE: 'この設定では、「Device」の送信を切り替えることができます。次のすべてのパラメータは、デバイスの最初のオンライン BootStrap メソッドで使用されます。',
          DEVICE_XMPP_CONNECTION_1: 'Device.XMPP.Connection.1をオン/オフに切り替える設定。BootStrapメソッド用です。',
          DEVICE_MQTT_CLIENT_1: 'Device.MQTT.Client.1をオン/オフに切り替える設定。BootStrapメソッド用です。',
          DEIVCE_DEVICEINFO_XVENDOR_HOLD: 'Devcie.DeviceInfo.X_VENDOR.HOIDをオン/オフに切り替える設定。BootStrapメソッド用です。',
          DEVICE_MANAGEMENTSERVER_PERIODICINFORMTIME: 'Device.ManagementServer.PeriodicInformTimeをオン/オフに切り替える設定。BootStrapメソッド用です。',
          TYPEOFINFORMRECEIVEDWITHIN: 'デバイスのオンライン状態を判断するために使用される設定。',
          INTERVALOFINFORMRECEIVEDWITHIN: 'ドロップ条件の期間を設定するための設定。',
          SERVERREPORTENABLED: 'サーバーレポートの生成をオン/オフに切り替える設定:オンの場合、ACSによってサーバーレポートが生成されます。オフの場合、ACSによってサーバーレポートは生成されません。',
          SERVERREPORTEMAILNOTIFICATION: 'システムレポートの電子メール通知機能を有効または無効にします。',
          SERVERREPORTPERIOD: 'サーバーレポートの生成頻度(日単位)を設定するための設定。',
          SERVERREPORTCONTENT: 'システム レポートのオプション コンテンツである AMP は、MONGODB、XMPP、MQTT、および PM サーバーの情報収集をサポートします。',
          PRIORITY: 'CPEのIP検出ソースの優先度を定義します:RemoteAddress - IPは指定されたデータノードから取得されます。Eq.Device.DeviceInfo.X_Vendor_GlobalIPAddress; X-Forwarded-For - IPはXForwarded-For HTTPヘッダーから取得されます。Custom - IPはカスタムHTTPヘッダーから取得され、[Custom Header]の設定で指定された名前です。',
          CUSTOMHEADER: 'CPEの実際のIPアドレスを含むカスタムHTTPヘッダーの名前。',
          IDLETIMEOUT: 'システムセッションの有効期限',
          SWAGGER_ENABLED: 'RESTful API UI を有効/無効にします。',
          CLIENT_URL: 'CPEがCPE WAN管理プロトコルを使用してACSに接続するためのURL。'
        },
        CONNECTIONREQUEST: {
          USERNAME: 'CRメカニズムの設定可能なユーザー名。',
          PASSWORD: 'CRメカニズムの設定可能なパスワード。',
          RETRYTIMEOUT: 'CRの設定可能なタイムアウト。',
          NUMBEROFRETRY: '最大CR試行回数。',
          TYPE: 'CRのタイプ。',
          // XMPPADDRESS:'',
          XMPPDOMAIN: 'JID自動生成で使用される設定可能なドメイン名。',
          XMPPPORT: 'XMPPサーバーのポート。 ejabberdサーバー用に指定されたデフォルト値。',
          XMPPACSUSERNAME: 'XMPP CR呼び出しのための設定可能なACSユーザー名。',
          XMPPACSPASSWORD: 'XMPP CR呼び出しのための設定可能なACSパスワード。',
          XMPPADMINPORT: 'サーバー管理者用のXMPPサーバーのポート。 ejabberdサーバー用に指定されたデフォルト値。',
          XMPPADMINUSERNAME: '自動XMPPユーザー登録で使用される設定可能なXMPP管理者のユーザー名資格情報。',
          XMPPADMINPASSWORD: '自動XMPPユーザー登録で使用される設定可能なXMPP管理者のパスワード資格情報。',
          XMPPRESOURCE: 'XMPP CRの設定可能なリソース値。',
          XMPPUSETLS: 'XMPP CRのTLS使用を切り替える設定:オンの場合、TLSが有効になります。オフの場合、TLSは無効になります。'
        },
        USP: {
          BINDING: 'バインディングタイプwebsocketまたはmqtt。',
          ADDRESS: 'MTPサーバードメイン',
          PORT: 'MTP接続ポート',
          APIKEY: "MQTTでは、サーバーの状態を問い合わせるためにAPIキーが利用されます。",
          USERNAME: 'ブローカーによって必要とされるユーザー名(存在する場合)。',
          PASSWORD: 'ブローカーによって必要とされるパスワード(存在する場合)。',
          USE_TLS: 'MTPか化をTLS。',
          EXADDRESS: 'デバイスはWebSocket/MQTT/CWMP/XMPPサービスのアドレスまたはドメインに接続できます。',
          EXPORT: 'デバイスはWebSocket/mqttサービスのポートに接続できます。',
          USETLS: 'デバイスがTLSを使用してWebSocket/MQTT/CWMP/XMPPサービスに接続しているかどうか。',
          EXURL: 'デバイスはCWMPサービスのアドレスまたはドメインに接続できます。',
        },
        FILES: {
          DOWNLOAD: {
            LATESTFIRMWARE: '設定により、CSRユーザーが最新のファームウェアバージョンを指定できます。',
            FIRMWARESERVERURL: 'APがファームウェアをダウンロードするために使用するファイルサーバーへのパス。',
            FIRMWARESERVERUSERNAME: 'ファイルサーバーで認証するためのユーザー名資格情報。',
            FIRMWARESERVERPASSWORD: 'ファイルサーバーで認証するためのパスワード資格情報。'
          },
          UPLOAD: {
            FILETYPE: 'デバイスのアップロードファイルタイプ',
            INSTANCEPATH: 'データ・モデル内のユーザーがログ・ファイルをアップロードできるパス。',
            LOGUPLOADURL: 'CSRユーザーがAPログファイルをアップロードできるURL。この機能には追加のサーバー構成が必要です。',
            USERNAME: 'ファイルのアップロード用の設定可能なユーザー名。',
            PASSWORD: 'ファイルのアップロード用の設定可能なパスワード。'
          },
          CONF_DOWNLOAD: {
            DEFAULT_FILE: 'ファイルから選択されたデフォルト構成ファイル（タイプ: 3 Vendor Configuration File）。',
            DEFAULT_FILE_URL: 'デフォルト構成ファイルのURLです。',
            FILETYPE: 'デバイス構成ファイルのダウンロードタイプ。',
            CONFURL: 'AMPから構成ファイルをダウンロードするためのURL。',
            USERNAME: 'デバイスダウンロード用に設定可能なユーザー名。',
            PASSWORD: 'デバイスダウンロード用に設定可能なパスワード。'
          },
        },
        TELEMETRY: {
          TELEMETRYSERVERREDIRECTION: 'デバイス情報ページの第三者ウェブサイトリンクボタンのオン/オフ切り替え設定です。オンの場合、第三者ウェブサイトリンクボタンが表示されます。オフの場合、第三者ウェブサイトリンクボタンは表示されません。',
          VENDOR: 'Druid、DNMM、HP、Open 5GC などのオプションを備えた、特定のテクノロジー、サービス、または製品を提供するサプライヤー。',
          TYPE: 'オープンソースのネットワーク侵入検知システムタイプ、オプションにはSuricataが含まれる',
          SERVERURL: '第三者ウェブサイトのURL。',
          SERVERUSERNAME: '第三者ウェブサイトへのログインのための設定可能なユーザー名。',
          SERVERPASSWORD: '第三者ウェブサイトへのログインのための設定可能なパスワード。',
          KPIFACTORS: 'KPI要因はユーザー定義のルールに基づいてPMパラメータの状態を監視できます。',
          UEINTERVAL: '設定可能な UE タイマー間隔。',
          CELLINTERVAL: '設定可能なセル タイマー間隔。',
          ALARMINTERVAL: '設定可能なアラーム タイマー間隔。',
          COMMONINTERVAL: '設定可能な共通タイマー間隔。',
          APIURL: 'パフォーマンスサービスの北向インターフェースアドレス。',
          APIUSERNAME: 'パフォーマンスサービスの北向インターフェースの身分識別用ユーザー名。',
          APIPASSWORD: 'パフォーマンスサービスの北向インターフェースの身分識別用パスワード。'
        },
        MAP: {
          TYPE: 'マップ サーバー タイプ、AMP は Google マップとオープン ストリート マップをサポートします。  デフォルトはGoogleマップです。',
          URL: 'マップサーバーのURL。',
          APIKEY: 'マップ API キー。',
        }
      },
      PREFERENCE_DESCRIPTION: {
        SMTP: {
          MAIL_HEALTHY: 'システムの健全性監視、CPU負荷、ディスク使用率、クラッシュ、ライセンスなど。',
          MAIL_FORM: '電子メール送信元のユーザー名',
          MAIL_HOST: 'メールボックスに対応するSMTPサーバーアドレス。',
          MAIL_USERNAME: '電子メール送信者のアドレス',
          MAIL_PASSWORD: '電子メール送信者のパスワード',
          MAIL_PORT: '電子メール送信ポート',
          MAIL_TO: '受信者アドレス',
          MAIL_SMTP_AUTH: 'SMTPプロトコル関連の設定、認証が必要かどうか。',
          MAIL_SMTP_SECURITY: 'SMTPプロトコル関連の設定。',
          MAIL_TRANSPORT_PROTOCOL: '現在は使用されていません',
        },
        SNMPTRAP: {
          SNMPTRAP_TARGET: 'SNMPトラップの送信先アドレス',
          SNMPTRAP_PORT: 'リクエストを送信するためのUDPポート、デフォルトは161です。',
          SNMPTRAP_RETRIES: 'リクエストを再送信する回数、デフォルトは1です。',
          SNMPTRAP_TIMEOUT: '再試行または失敗する前にレスポンスを待機するミリ秒数、デフォルトは5000です。',
          SNMPTRAP_TRANSPORT: '使用するトランスポートを指定できます。udp4またはudp6で、デフォルトはudp4です。',
          SNMPTRAP_TRAPPORT: 'トラップとInformを送信するためのUDPポート、デフォルトは162です。',
          SNMPTRAP_VERSION: 'snmp.Version1またはsnmp.Version2c',
          SNMPTRAP_BACKOFF: 'リトライごとにタイムアウトを増加させるための係数、増加しない場合はデフォルトで1です。',
          SNMPTRAP_COMMUNITY: '通信と認証のセキュリティを確保するために使用されます。',
        },
        REPORTS: {
          DEVICE_REPORT_CLEANUP_ENABLE: 'オンの場合、自動デバイスレポートストレージのクリーンアップが有効になります。オフの場合、自動デバイスレポートストレージのクリーンアップは無効になります。',
          DEVICE_REPORT_RETENTION_PERIOD: 'デバイスレポートエントリをストレージに保持する日数を指定します。',
          SERVER_REPORT_CLEANUPZZ_ENABLE: 'オンの場合、自動サーバーレポートストレージのクリーンアップが有効になります。オフの場合、自動サーバーレポートストレージのクリーンアップは無効になります。',
          SERVER_REPORT_RETNETION_PERIOD: 'サーバーレポートエントリをストレージに保持する日数を指定します。'
        },
        STATISTICS: {
          CPU_COLLECTION_ENABLE: '有効になっている場合は、ライセンスによりCPUインジケータの収集が許可されます。無効になっている場合、ライセンスではCPUインジケータの収集は許可されません。',
          DISK_COLLECTION_ENABLE: '有効にされている場合、ライセンスはディスクインジケータの収集を許可します。無効にされている場合、ライセンスはディスクインジケータの収集を許可しません。',
          MEMORY_COLLECTION_ENABLE: '有効にされている場合、ライセンスはメモリ・メトリックの収集を許可します。無効にされている場合、ライセンスではメモリインジケータの収集は許可されません。',
          REPORT_ENABLE: '有効になっている場合、ライセンスは統計メトリックのレポートを許可します。無効にした場合、ライセンスは統計メトリックのレポートを許可しません。',
          REPORT_PERIOD: '設定はメトリクスの収集頻度を指定します。',
          PM_KPI_COLLECTION_RETENTION: 'この設定では、PM KPI DB データを保持する日数を指定します。',
          PM_KPI_FILE_RETENTION: 'この設定では、PM KPI ファイルを保持する日数を指定します。'
        },
        LOGCONFIG: {
          DEVICE_GROUP_OPERATION_LOG_CLEANUP_ENABLE: '有効にされている場合、ライセンスはデバイスグループの操作データを自動的に消去することを許可します。無効にされている場合、ライセンスではデバイスグループの操作データを自動的に消去することはできません。',
          DEVICE_GROUP_OPERATION_LOG_RETENTION_PERIOD: '設定はログ内のグループ操作データを保持する日数を指定します。',
          DEVICE_OPERATION_LOG_CLEANUP_ENABLE: '有効にされている場合、ライセンスによりデバイス操作データの自動消去が可能になります。無効にされている場合、ライセンスではデバイス操作データの自動消去は許可されません。',
          DEVICE_OPERATION_LOG_RETENTION_PERIOD: '設定はログ内のデバイス操作データを保持する日数を指定します。',
          SESSION_LOG_CLEANUP_ENABLE: '有効にすると、ライセンスはイベントログを自動的に消去できます。無効にすると、ライセンスはイベントログを自動的に消去することを許可しません。',
          SESSION_LOG_RETENTION_PERIOD: '設定はログ内のセッションレコードを保持する日数を指定します。',
          STATISTICS_LOG_CLEANUP_ENABLE: '有効にされている場合、ライセンスは自動統計ログクリーンアップを許可します。無効にした場合、ライセンスでは自動統計ログのクリーンアップは許可されません。',
          STATISTICS_LOG_RENTENTION_PERIOD: '設定は統計ログを保持する日数を指定します。'
        },
        FAULTMANAGEMENT: {
          EVENT_ALARM_ACK_ENABLE: 'オンの場合、設定によりイベントの自動確認が有効になります。オフの場合、手動で確認が必要です。',
          EVENT_ALARM_CLEANUP_ENABLE: 'オンの場合、設定によりイベントログの自動クリーンアップが有効になります。オフの場合、自動イベントログのクリーンアップは無効になります。',
          EVENT_ALARM_EMAIL_ENABLE: 'オンの場合、設定によりイベントの発生時に自動電子メール通知が有効になります。オフの場合、アラーム電子メール通知はありません。',
          EVENT_ALARM_RETENTION_PERIOD: '設定はイベントログを保持する日数を指定します。'
        }
      }
    },
    COMMON: {
      DEVICES: 'デバイス',
      DEVICE: 'デバイス',
      CLIENTS: 'クライアント',
      CLIENT: 'クライアント',
      USERS: 'ユーザー',
      ALARMS: 'アラーム',
      TOTALALARMS: '総アラーム',
      HISTORYALARMS: '過去のアラーム',
      CRITICALALARMS: '重大',
      MAJORALARMS: '主要',
      WARNINGALARMS: '警告',
      MINORALARMS: '軽微',
      PRODUCTS: '製品',
      PRODUCTSDISTRIBUTION: '製品分布',
      REGISTERDEVICECOUNT: "登録デバイス数",
      REGISTERDEVICECOUNTDISTRIBUTION: "各製品ごとのデバイス数の分布グラフ",
      ONLINEDEVICE: 'オンラインデバイス',
      HISTORYONLINEDEVICE: "過去のオンラインデバイス",
      APPLY: '適用',
      DELETE: '削除',
      DELETEALL: 'すべて削除',
      CANCEL: 'キャンセル',
      OK: 'OK',
      CLOSE: '閉じる',
      ADD: '追加',
      EDIT: '編集',
      FAIL: '失敗',
      SERIAL_NUMBER: 'シリアル番号',
      PRODUCT_CLASS: '製品クラス',
      ACTION: 'プロフィール',
      NEW: '新規',
      SELECTACTION: 'アクションを選択',
      IMPORT: 'インポート',
      DOWNLOAD: 'ダウンロード',
      DOWNLOADLOG: 'ダウンロードログ',
      SAVE: '保存',
      DONTSAVE: '保存しない',
      UPLOAD: 'アップロード',
      NAME: '名前',
      ENTERNAME: '名前を入力',
      VERSION: 'バージョン',
      PRIORITY: '優先度',
      ENTERVERSION: 'バージョンを入力',
      SOFTVERSION: 'ソフトウェアバージョン',
      TYPE: 'タイプ',
      SELECTTYPE: 'タイプを選択',
      PREVIOUS: '前へ',
      NEXT: '次へ',
      USERNAME: 'ユーザー名',
      PASSWORD: 'パスワード',
      USERNAME1: 'ユーザー名',
      PASSWORD1: 'パスワード',
      ENTERUSERNAME: 'ユーザー名を入力',
      ENTERPASSWORD: 'パスワードを入力',
      UPDATE: '更新',
      UNINSTALL: 'アンインストール',
      PARAMETERS: 'パラメータ',
      PARAMNAME: 'パラメータのパス',
      ENTERPARAMNAME: 'パラメータのパスを入力してください',
      PARAMTYPE: 'パラメータタイプ',
      SELECTPARAMTYPE: 'パラメータタイプを選択',
      PARAMVALUE: 'パラメータ値',
      ENTERPARAMVALUE: 'パラメータ値を入力',
      ADDPARAM: 'パラメータを追加',
      EXECUTE: '実行',
      SIZE: 'サイズ',
      CANCELALL: 'すべてキャンセル',
      FIELDREQUIRED: 'このフィールドは必須です!',
      DETAILS: '詳細',
      SELECTPRODUCTNAME: '製品名を選択',
      SELECTPRODUCT: '製品を選択',
      AND: 'および',
      EDITPARAM: 'パラメータを編集',
      VALUE: '値',
      EXPANDCOLLROW: '行の展開/折りたたし',
      PORT: 'ポート',
      HOST: 'ホスト',
      THECUSTOMIZE: 'テーマカスタマイザー',
      CUSTOMIZEREALTIME: 'リアルタイムでカスタマイズしてプレビュー',
      SKIN: 'スキン',
      LIGHT: 'ライト',
      BORDERED: 'ボーダー',
      DARK: 'ダーク',
      RED: "赤",
      BLUE: "青",
      SEMIDARK: 'セミダーク',
      ROUTETRA: 'ルートトランジション',
      FADEINLEFT: '左からフェードイン',
      ZOOMIN: 'ズームイン',
      FADEIN: 'フェードイン',
      NONE: 'なし',
      MENULAYOUT: 'メニューレイアウト',
      VERTICAL: '垂直',
      HORIZONTAL: '水平',
      MENUCOLL: 'メニュー折りたたみ',
      MENUHIDDEN: 'メニュー非表示',
      NAVBARCOLOR: 'ナビゲーションバーカラー',
      NAVBARTYPE: 'ナビゲーションバータイプ',
      MENYTYPE: 'メニュータイプ',
      FLOATING: 'フローティング',
      STICKY: 'スティッキー',
      STATIC: 'スタティック',
      FOOTERTYPE: 'フッタータイプ',
      WIDGETS: 'widgetのカスタマイズ',
      EDITMODE: 'Widget編集モード',
      CUSWIDGETS: 'widgetのカスタマイズ',
      LOGOUT: 'ログアウト',
      RECENTNOTIF: '最近の通知',
      NOTIFICATIONS: '通知',
      READMORE: '続きを読む',
      TOTAL: '合計',
      SELECTED: '選択済み',
      CREATED: '作成日時',
      SELECTCOLUMN: '列を選択',
      ACTIVE: 'アクティブ',
      ALLOW: '許可',
      YES: 'はい',
      CLIENTLIST: 'クライアントリスト',
      WIFICLIENTLIST: 'WiFiクライアントリスト',
      WIFICLIENTLIST_DESCRIPTION: 'このグループに含まれるすべての WiFi クライアントのリスト。',
      WIFICLIENTLISTDESCRIPTION: 'このデバイスの利用可能なラジオ/バンドに関連するクライアントの現在のWiFi状態。',
      ONLINE: 'オンライン',
      OFFLINE: 'オフライン',
      EXPORT: 'エクスポート',
      MQTT: 'MQTT',
      CWMP: 'CWMP',
      NETCONF: 'NETCONF',
      CURRENTNODE: '現在のノード',
      CHILDNODE: '子ノード',
      EDITSTAGE: 'ステージ名の編集',
      STAGENAME: 'ステージ名',
      ENTERSTAGENAME: 'ステージ名を入力',
      OPERATIONNAME: '操作名',
      ADDOPERATION: '操作を追加する',
      ALLPROTOCOL: 'すべてのプロトコル',
      ALLPRODUCTS: 'すべての製品',
      ALLERROR: 'すべてのエラー',
      USER: 'ユーザー',
      ALLFILETYPES: 'すべてのファイルタイプ',
      ALLTARGETTYPES: 'すべてのターゲットタイプ',
      TRANSMISSTIONTYPE: 'トランスミッションタイプ',
      SELECTTRANSMISSTIONTYPE: '転送タイプの選択',
      REMOVEFROMGROUP: 'グループから削除',
      SHUTDOWN: 'ていし',
      NOPERMISSION: '現在のユーザーには、このページの内容を読み取る権限がありません。',
      SEPARATED_BY_SEMICOLONS: 'セミコロンで区切る',
      SEPARATED_BY_COMMAS: 'カンマで区切る',
      MAIL_SEPARATED_BY_SEMICOLONS: 'セミコロンで区切る (<EMAIL>;<EMAIL>;)',
      SN_SEPARATED_BY_COMMAS: 'カンマで区切る (sn1,sn2)',
      WIDGETNAME: 'ウィジェット名、クラス、またはサブクラスを入力してください。',
      APNAMEEDITSUCC: 'AP 名称の編集に成功しました！',
      APNAMEEDITFAIL: 'AP 名称の編集に失敗しました。',
      LOCATE: '位置',
      RELOAD: "リロード",
      DATA: "データ",
      STATE: "状態",
      REGISTER: '登録',
      GROUP: 'グループ',
      SELECTCHARTTYPE: 'チャートタイプを選択',
      OPEN_MAXIMIZE: "最大化を開く",
      SELECTORENTER: 'オプションを選択または入力してください',
      INVALIDFILETYPE: 'ファイルの種類が無効です。次のタイプのいずれかを選択してください: ',
    },
    CONFIRM: {
      CONF: '確認 ',
      REMOVAL: ' 削除?',
      REBOOT: ' 再起動?',
      SHUTDOWN: ' ていし?',
      ADDFAIL: '追加に失敗しました!',
      NAMEEXIST: '名前は既に存在します!',
      ALARMNOTIF: 'アラーム通知が正常に更新されました',
      CONFREMGROUP: 'グループの削除を確認しますか?',
      CONFGROUP: 'グループの削除を確認しますか?',
      CONFGROUPS: 'グループの削除を確認しますか？',
      DOGROUP: 'グループから削除しますか',
      FROMGROUP: ' から?',
      IMPORTSUCCESS: "インポートが成功しました！",
      IMPORTFAIL: 'インポートに失敗しました!',
      FILEEMPTY: 'ファイルは空です',
      NOTSUPPORT: 'このファイル形式はサポートされていません',
      DODELETEGROUP: 'グループを削除しますか',
      DODELETEGROUPS: 'これらのグループを削除しますか？',
      GROUPOPER: 'デバイスグループ操作!',
      WORKFLOWOPER: 'デバイスのワークフローアクション！',
      WORKFLOWOPERATION: 'デバイスのワークフロー操作 ',
      WORKFLOWDOREMOVEALL: 'ワークフロー操作ログからすべてのエントリを削除しますか？',
      WORKFLOWCLEANSUCC: 'ワークフロー操作ログは正常にクリーンアップされました',
      WORKFLOWNOTCLEAN: 'ワークフロー操作はクリーンアップされませんでした',
      SETGROUPOPERSUCC: 'グループ操作の設定が成功しました!',
      RENAMESUCC: '名前の変更が成功しました',
      CONFNIT: 'アラーム通知のダウンロードを確認しますか?',
      DODOWNLOADNIT: 'アラーム通知をダウンロードしますか',
      ALARMNIT: '通知',
      DOWNLOADSUCC: 'ダウンロードに成功しました!',
      PLESELECT: '最初にアラーム通知を選択してください!',
      CONFDOWNLOADNIT: '選択したアラーム通知のダウンロードを確認しますか?',
      DODOWNLOADSELECT: '選択したアラーム通知をダウンロードしますか?',
      DOWNLOADSELECT: '選択したアラーム通知のダウンロードに成功しました!',
      DOWANT: '次の操作を行いますか: ',
      THEALARMNIT: 'アラーム通知',
      SUCC: ' 正常に完了しました!',
      CONFDELETENIT: 'アラーム通知の削除を確認しますか?',
      DODELETENIT: 'アラーム通知を削除しますか',
      NITDELSUCC: 'アラーム通知が正常に削除されました',
      NITID: 'アラーム通知(ID: ',
      WANDEL: ')が削除されました!',
      NITDELETEFAIL: 'アラーム通知の削除に失敗しました',
      NOTDEL: ')は削除されませんでした!',
      SELECTFIRST: '最初にアラーム通知を選択してください!',
      STATEITEMS: '選択したアラーム通知には1つ以上のアクティブな状態アイテムが含まれています!',
      CONFSELECTNIT: '選択したアラーム通知の削除を確認しますか?',
      DOSELECTNIT: '選択したアラーム通知を削除しますか?',
      SELECTNITSUCC: '選択したアラーム通知が正常に削除されました!',
      GROUPOPERATION: 'デバイスグループ操作 ',
      CANCELSUCC: '正常にキャンセルされました',
      REMOVEDLOG: 'ログから正常に削除されました',
      CONFCLEANUP: 'ログのクリーンアップを確認しますか',
      DOREMOVEALL: 'グループ操作ログからすべてのエントリを削除しますか?',
      GROUPCLEANSUCC: 'デバイスグループ操作ログは正常にクリーンアップされました',
      GROUPNOTCLEAN: 'デバイスグループ操作はクリーンアップされませんでした',
      CONFPRODUCTREM: '製品の削除を確認しますか?',
      DOPRODUCT: '製品を削除しますか ',
      CONFRANREM: '無線アクセスネットワークの削除を確認しますか？',
      CONFAPNREM: 'WiFi AP ネットワークの削除を確認しますか？',
      CONFMESHREM: 'WiFi メッシュネットワークの削除を確認しますか？',
      DORAN: 'この無線アクセスネットワークを削除しますか ',
      DOAP: 'この WiFi AP ネットワークを削除しますか ',
      DOMESH: 'この WiFi メッシュネットワークを削除しますか ',
      CONFPRODUCTBAN: '製品の禁止を確認しますか',
      CONFRANBAN: '無線アクセスネットワークの禁止を確認',
      CONFAPNBAN: 'WiFi AP ネットワークの禁止を確認',
      CONFMESHNBAN: 'WiFi メッシュネットワークの禁止を確認',
      PRODUCTACCESS: '? この製品のデバイスはサーバーにアクセスできなくなります。',
      PRODUCTSACCESS: '? これらの製品のデバイスはサーバーにアクセスできなくなります。',
      RANSACCESS: '？この無線アクセスネットワークのデバイスはサーバーにアクセスできなくなります。',
      APSACCESS: '？この WiFi AP ネットワークのデバイスはサーバーにアクセスできなくなります。',
      MESHSACCESS: '？この WiFi メッシュネットワークのデバイスはサーバーにアクセスできなくなります。',
      CONFFILE: 'ファイルの削除を確認しますか?',
      DELFILESUCC: 'ファイルの削除に成功しました',
      CONFSCRIPT: 'スクリプトの削除を確認しますか?',
      DOSCRIPT: 'スクリプトを削除しますか ',

      CONFFLOW: 'ワークフローのダウンロードを確認しますか?',
      WORKFLOW: 'ワークフロー',
      DOWORKFLOW: 'ワークフローをダウンロードしますか ',
      CONFSELECT: '選択したワークフローのダウンロードを確認しますか?',
      DOSELECTFLOW: '選択したワークフローをマルチファイルとしてダウンロードしますか?',
      DOSELECTFLOWASONE: '選択したワークフローを 1 つのファイルとしてダウンロードしますか?',
      DOWNSELECTFLOW: '選択したワークフローを正常にダウンロードしました!',
      DOWNSELECTFILESUCCESS: '選択したファイルのダウンロードに成功しました！',
      DELSELECTFLOW: '選択したワークフローを正常に削除しました!',
      PLEASEFLOWS: '最初にワークフローを選択してください!',
      THEFLOW: ' ワークフロー ',
      CONFDELFLOW: 'ワークフローの削除を確認しますか?',
      DODELFLOW: 'ワークフローを削除しますか',
      DELSUCC: 'ワークフローは正常に削除されました',
      FLOWID: 'ワークフロー(ID: ',
      FLOWDELFAIL: 'ワークフローの削除に失敗しました',
      FLOWITEM: '選択したワークフローには1つ以上のアクティブなステートアイテムが含まれています',
      CONFSELECTFLOW: '選択したワークフローの削除を確認しますか?',
      DODELSELECTFLOW: '選択したワークフローを削除しますか?',

      CONFCONFIGURATION: 'ダウンロード構成を確認しますか？',
      CONFIGURATION: '構成',
      DOWORKCONFIGURATION: '構成をダウンロードするかどうか',
      CONFSELECTCONFIGURATION: '選択した構成のダウンロードを確認しますか？',
      DOSELECTCONFIGURATION: '選択した構成をマルチファイルとしてダウンロードしますか？',
      DOSELECTCONFIGURATIONASONE: '選択した構成をファイルとしてダウンロードしますか？',
      DOWNSELECTCONFIGURATION: '選択した構成のダウンロードに成功しました！',
      DELSELECTCONFIGURATION: '選択した構成の削除に成功しました！',
      PLEASECONFIGURATIONS: 'まず構成を選択してください！',
      THECONFIGURATION: '構成',
      CONFDELCONFIGURATION: '構成の削除を確認しますか？',
      DODELCONFIGURATION: '構成を削除するかどうか',
      DELCONFIGURATIONSUCC: '構成は正常に削除されました',
      CONFIGURATIONID: '構成(ID:',
      CONFIGURATIONDELFAIL: '構成削除に失敗しました',
      CONFIGURATIONITEM: '選択した構成には1つ以上のアクティブな状態アイテムが含まれています',
      CONFDELSELECTCONFIGURATION: '選択した構成の削除を確認しますか？',
      DODELSELECTCONFIGURATION: '選択した構成を削除しますか？',

      CONFPOLICY: 'ダウンロードポリシーを確認しますか?',
      DOWORKPOLICY: 'ポリシーをダウンロードしますか',
      POLICYID: 'ポリシー (ID:',
      POLICYFLOWSUCC: 'ポリシーが正常に更新されました',
      POLICY: 'ポリシー',
      POLICYCLONESUCCESS: 'ポリシーが正常にクローンされました！',
      CONFDELPOLICY: 'ポリシーの削除を確認しますか？',
      DODELPOLICY: 'このポリシーを削除しますか？',
      DELPOLICYSUCC: 'ポリシーが正常に削除されました',
      POLICYDELFAIL: 'ポリシーの削除に失敗しました',
      PLEASEPOLICYS: 'まずポリシーを選択してください！',
      DOSELECTPOLICY: '選択したポリシーを複数のファイルとしてダウンロードしますか？',
      DOSELECTPOLICYSONE: '選択したポリシーを1つのファイルとしてダウンロードしますか？',
      CONFSELEPOLICY: '選択したポリシーのダウンロードを確認しますか？',
      DOWNSELECTPOLICY: '選択したポリシーが正常にダウンロードされました！',
      POLICYITEM: '選択したポリシーには1つ以上のアクティブな状態アイテムが含まれています',
      CONFDELSELECTPOLICY: '選択したポリシーの削除を確認しますか？',
      DODELSELECTPOLICY: '選択したポリシーを削除しますか？',
      DELSELECTPOLICY: '選択したポリシーが正常に削除されました！',

      CONPROFILE: '設定ファイルをダウンロードする?',
      PROFILE: '設定データ',
      DOPROFILE: '設定ファイルをダウンロードする必要があります',
      CONOSELECT: '選択したプロファイルファイルのダウンロードを確認しますか？',
      DOSELECTPROFILE: '選択した作業プロフィールを複数のファイルでダウンロードしたいですか?',
      DOSELECTPROFILEASONE: '選択した作業プロフィールをファイルとしてダウンロードしたいですか？',
      DOWNSELECTPROFILE: '選択したプロファイルを正常にダウンロードしました。',

      PROVISIONINGFILE: "ダウンロードファイルを確認しますか?",
      PROVISIONINGCONFILE: "ダウンロードファイルの確認?",
      PROVISIONINGSELECT: "選択したファイルのダウンロードを確認しますか?",
      PROVISIONINGDOSELECTFILE: "選択したファイルを複数のファイルとしてダウンロードしますか？",
      PROVISIONINGDOSELECTFILEONE: "選択したファイルを 1 つのファイルとしてダウンロードしますか?",
      PROVISIONINGCONOSELECT: "選択したファイルをダウンロードすることを確認しますか?",

      ENDGREATER: '終了日は現在の日付よりも大きい必要があります!',
      STARTHAVEVALUE: '開始日または終了日は値である必要があります!',
      ENDGREATERSTART: '終了日は開始日よりも大きい必要があります!',
      STARTENDALUE: '開始時間または終了時間は値である必要があります!',
      EXACTLYEQUAL: '時間は正確に等しいことはできません!',
      CONFSTAGE: 'ステージの削除を確認しますか?',
      BADREQ: '無効なリクエスト',
      PARAMNEED: 'パラメータを保存する前に入力する必要があります',
      FLOWSUCC: 'ワークフローが正常に更新されました',
      CONFIGURATIONSUCC: '構成の更新に成功しました',
      WASUPDATE: ') が更新されました!',
      CROSSCLICK: 'クロスクリック',
      FLOWADDSUCC: 'ワークフローが正常に追加されました',
      WASADD: ') が追加されました!',
      FORMFAIL: 'フォームの検証に失敗しました!',
      CONFOPER: '操作の削除を確認しますか?',
      VERSIONERROR: 'バージョンエラー!',
      ONLY16: '文字、数字、および特殊記号( _ - .)の1-16文字のみが許可されています。',
      FORKSUCC: 'ワークフローが正常にフォークされました!',
      WASFORK: ' 分岐していました。新しい優先順位: ',
      FLOWSPACE: 'ワークフロー ',
      OPERFORKSUCC: '操作が正常にフォークされました!',
      OPERFLOWSPACE: '操作 ',
      OPERATION: '操作',
      CONFDELOPER: 'プロファイルの削除を確認する?',
      DODELOPER: 'プロファイルを削除しますか?',
      DELSUCCESS: '正常に削除されました!',
      PLEOPERFIRST: '最初に操作を選択してください!',
      CONFSELECTOPER: '選択したプロファイルの削除を確認します？',
      DOSELOPER: '選択したプロファイルを削除しますか？',
      DELOPERSUCC: '選択したプロファイルが正常に削除されました!',
      CONFACTIONRE: '操作の削除を確認しますか?',
      STAGE: ' ステージ?',
      ACTION: ' プロフィール?',
      OPERUPDATESUCC: 'プロファイルが正常に更新されました',
      OPERADDSUCC: '設定ファイルが正常に追加されました',
      ALARMADDSUCC: 'アラーム通知が正常に追加されました',
      CONFLICT: '競合',
      ALARMNOTNAME: '名前のアラーム通知 ',
      ALREADYEXIST: ' は既に存在します',
      CONTAINDATA: ' 矛盾したデータを含んでいます',
      NAMEERROR: '名前エラー!',
      ONLYNAMESYM: '1～64文字の英字、数字、特殊文字のみを使用できます（_-space）',
      CLONENOTI: 'アラーム通知が正常にクローンされました!',
      WANCLONE: ' がクローンされました。新しいアラーム通知: ',
      CONFIGUPDATESUCC: '設定が正常に更新されました',
      SETOPERSUCC: 'デバイス操作の設定が成功しました!',
      SETOPERFAIL: 'デバイス操作の設定に失敗しました!',
      TASKSUCC: 'タスクの追加に成功しました!',
      LABELSUCC: 'ラベルの編集が成功しました!',
      LABELFAIL: 'ラベルの編集に失敗しました!',
      UPDATEDEV: '更新成功',
      CMDSENQUSUCC: 'コマンドが正常にキューに入れられました!',
      FORKNOT: 'アラーム通知が正常にフォークされました!',
      WANIMPORT: ') はインポートされませんでした。',
      NOTIIMPORTFAIL: 'アラーム通知のインポートに失敗しました',
      IMPORTSUCC: 'インポートが成功しました!',
      GROUPCREATESUCC: 'デバイスグループが正常に作成されました',
      IMPORTTOGROUP: ' デバイスがグループにインポートされました ',
      GNAMEEXIST: 'このグループ名はすでに存在します',
      SAVESRSSUCC: 'サマリーレポート設定の保存に成功しました',
      PLEASECONF: '設定後にトピックを変更できません、確認してください',
      ADDPRODSUCC: '製品の追加に成功しました。',
      ADDPARAMSUCC: 'パラメータの追加に成功しました。',
      UPDATEPRODSUCC: '製品が正常に更新されました。',
      UPDATERANSUCC: '無線アクセスネットワークが正常に更新されました。',
      UPDATEAPNSUCC: 'WiFi AP ネットワークが正常に更新されました。',
      UPDATEMESHSUCC: 'WiFi Mesh ネットワークが正常に更新されました。',
      UPDATEPARAMSUCC: 'パラメータの更新に成功しました。',
      PLEASEFILL: '星印の必要な項目を記入してください!',
      UPDATEPERM: '権限タイプの更新に成功しました',
      UPDATEPERMDEV: 'デバイスの権限の更新に成功しました',
      ADDSUCC: '追加成功',
      UPDATESUCC: '更新成功',
      DONE: '完了',
      WARNING: '警告!',
      PARAMNAMEEXIST: 'パラメータ名は既に存在します!',
      SCRITEMPTY: 'スクリプトリストは空です!',
      USPGROUPEMPTY: 'グループリストは空です!',
      GROUPNAMEREQ: 'グループ名が必要です',
      OPERMODEREQ: '操作モードが必要です',
      FLOWIMPORTFAIL: 'ワークフローのインポートに失敗しました',
      SYSSETSUCC: 'システム設定の保存に成功しました!',
      SYSSETFAIL: 'システム設定の保存に失敗しました!',
      DEVSETSUCC: 'デバイス設定の保存に成功しました!',
      DEVSETFAIL: 'デバイス設定の保存に失敗しました!',
      PROSETSUCC: '製品設定の保存に成功しました!',
      PROSETFAIL: '製品設定の保存に失敗しました!',
      SYSPRESUCC: 'システム設定の保存に成功しました!',
      SYSPREFAIL: 'システム設定の保存に失敗しました!',
      DELETEUSER: 'ユーザーを削除します ',
      CHANGEUSER: 'ユーザーを変更します ',
      SPFAIL: ' 失敗',
      ACTIVESUSS: ' アクティブ状態の変更に成功しました',
      ACTIVEFAIL: ' アクティブ状態の変更に失敗しました',
      IMAGEOVERSIZE: '画像がオーバーサイズです。',
      PWDNOTSAME: 'パスワードが一致しません。',
      PWDREQ: 'パスワードが必要です。',
      FORMATINCORRECT: '有効期限日の形式が正しくありません。',
      MUSTADMIN: 'ADMIN/CSRユーザーの製品は "ADMIN" である必要があります。',
      PRODUCTREQ: '製品が必要です。',
      SELECTADMIN: 'ADMINロールのみ "ADMIN" を選択できます。',
      ROLEREQ: 'ユーザーの役割が必要です。',
      AVATARSUCC: 'アバターが正常に更新されました。',
      AVATARFAIL: 'アバターの更新に失敗しました。',
      EMAILSUCC: 'メールボックスの更新に成功しました',
      EMAILFAIL: 'メールボックスの更新に失敗しました.',
      UPPERLIMIT: '追加できるデバイス数が上限を超えています!',
      MAXLIMIT: '最大デバイス制限は ',
      NOTSAVETITLE: '変更を保存するかどうか?',
      NOTSAVECONTENT: '行った変更は保存されません。',
      CONFIRMROLE: '役割の削除を確認しますか?',
      DOROLE: '役割を削除しますか?',
      DOSELECTROLE: '選択した役割を削除しますか?',
      DELROLESUCC: '役割が正常に削除されました。',
      ROLEUPSUCC: '役割が正常に更新されました。',
      ROLEADDSUCC: '役割が正常に追加されました。',
      CHANGEWILLBELOSE: "変更を保存しないと、変更は失われます。",
      SYSTEMRETURNLOGIN: "システムがログインページに戻る",
      SAVEEVENTTIP: 'この段階で保存する前にイベントを通知するを選択してください。',
      SAVENOTIFYORDEVICEPARAMTIP: 'この段階で保存する前に、通知パラメータまたはデバイスパラメータ条件を追加してください！',
      SAVEDEVICEFAULTTIP: 'この段階で保存する前に、デバイス故障パラメータ条件を追加してください。',
      ADDMEMBERSUC: 'グループメンバーの追加に成功しました。',
      ADDMEMBERFAIL: 'グループメンバーの追加に失敗しました!',
      SMTPHEALTHY: 'テストメールの成功',
      SNMPHEALTHY: 'SNMP Trapの成功をテストする。',
      XMPPHEALTHY: 'XMPPの成功をテストする。',
      GENSERVERREPORT: 'サーバーレポートが正常に生成されました。',
      WORKFLOWCLONESUCCESS: 'ワークフローのクローン作成に成功しました！',
      CONFIGURATIONWCLONESUCCESS: 'クローンの設定に成功しました！',
      HASBEENCLONED: 'クローン作成済み',
      HASBEENFORKED: '分断されました!',
      APNAMEEDITFAIL: 'AP 名の編集に失敗しました。',
      ADDAPNSUCC: 'WiFi APネットワークの追加に成功し、同じ名前のグループも同時に作成されました。',
      ADDRANSUCC: '無線アクセスネットワークの追加に成功し、同じ名前のグループも同時に作成されました。',
      ADDMESHSUCC: 'WiFiメッシュネットワークの追加に成功し、同じ名前のグループも同時に作成されました。',
      TAGERROR: '文字、数字、ハイフン(-)、アンダースコア(_)、ピリオド(.)、およびスペースのみを0～32文字使用できます。'
    },
    PM: {
      PMWORD: 'PM',
      PMPARAM: 'PMパラメータ',
      PMCHART: 'PMチャート',
      PERFORMANCEREPORT: 'パフォーマンスレポート',
      PMSTATEITEMS: '選択したメトリック ID には 1 つ以上の追跡項目が含まれています。',
      PERFORMANCEREPORT_DESCRIPTION: "デバイスやサービスのステータスなど、詳細情報を含む生成されたサーバー（AMP）レポートのリスト。",
      PMSTATISTICS: 'PM統計',
      SERIALNUMBER: 'シリアル番号',
      TARGETSERIALNUMBER: '対象シリアル番号',
      TARGETSN: '対象シリアル番号',
      TARGETGROUP: "ターゲットグループ",
      TARGET: "ターゲット",
      GROUP: 'グループ',
      PARAMNAME: 'パラメータ名',
      PARAMPATH: 'パラメータパス',
      CONDITION: '条件',
      CONDITIONS: '条件',
      PARAMVALUE: 'パラメータ値',
      FROM: '開始',
      TO: '終了',
      CREATEDBY: '作成者',
      BEGINTIME: '開始時間',
      ENDTIME: '終了時間',
      TIME: '時間',
      UPDATETIME: '更新時間',
      MODELNAME: 'モデル名',
      PRODUCTCLASS: '製品クラス',
      TIMERANGE: '時間範囲',
      OUI: 'OUI',
      METRICRULE: 'メトリックルール',
      ALL: '全て',
      CONFIRM_DELETE: '削除を確認',
      DO_DELETE: '選択したものを削除しますか',
      DODELETE: '削除しますか',
      DELETESUCCESS: '削除成功',
      DELETEFAIL: '削除失敗',
      PLESESELECT: '選択してください',
      CONFIRM_REFRESH: '再検索を確認する',
      DO_REFRESHSELECT: '選択された項目を再検索しますか',
      DO_REFRESH: '再検索しますか',
      REFRESHSUCCESS: '再検索成功',
      REFRESHFAIL: '再検索失敗',
      EXPIREDUPDATESUCCESS: "すべての期限切れデータが正常に更新されました。",
      DATAEXPIRED: "データの有効期限が切れました",
      ADDCONDITION: '条件を追加',
      DELETECONDITION: '条件を削除',
      SEARCH: '検索',
      REFRESH: '更新',
      REFRESHALL: "すべて更新",
      SEARCHRESULT: '検索結果',
      VIEWCHART: 'チャートを表示',
      CHART: 'チャート',
      SAVERULE: 'ルール名を保存',
      UPDATERULE: 'ルール名を更新',
      NAME: '名前',
      DESCRIPTION: '説明',
      CLOSE: '閉じる',
      SAVE: '保存',
      DELETE: '削除',
      DELETEALL: '全て削除',
      GOTDEVICEINFO: 'デバイス情報ページに移動してください ',
      VIEWALLCHART: "デバイスを選択してパフォーマンスチャートを表示してください。（最大：20）",
      DURATION: "継続時間",
      NOTIFICATIONTOOLTIP: '通知に変換',
      REFRESHRULETOOLTIP: '再検索ルール',
      DEVICECOUNT: 'デバイス数',
      FAIL: '失敗！',
      REFRESHLOADING: '再検索ローディング中...',
      DOWNLOAD: 'ダウンロード',
      CONFIRM_DOWNLOAD: 'ダウンロードの確認',
      DOWNLOADSUCCESS: 'ダウンロード成功',
      DOWNLOADFAIL: 'ダウンロード失敗',
      DO_DOWNLOAD: '選択されたデバイスをダウンロードしますか？',
      DODOWNLOAD: 'ダウンロードしますか ',
      OPENSEARCHBAR: '検索バーを開く',
      HIDESEARCHBAR: '検索バーを非表示',
      LASTMACHINGTIME: '最後のマッチング時間',
      RESEARCH: '再検索',
      RESEARCHRULETOOLTIP: '再検索ルール',
      TRACKING: 'トラッキング',
      RESULT: '結果',
      RESEARCHALL: 'すべて再検索',
      RESULTTOOLTIP: '条件を満たすデバイスの数',
      LASTMACHING: "最後のマッチング",
    },
    REFURBISHMENT: {
      REFURBISHMENTSTATISTICS: '更新統計',
      REFURBISHMENTTIME: 'さいせいじかん',
      REFURBISHMENTCOUNT: 'リフレッシュ数',
      INSTALLATIONTIME: 'インストール時間',
    },
    CARE: {
      TITLE: 'お手入れ',
      GENERALINFO: '一般的な情報',
      GENERALINFODESCRIPTION: 'デバイスの一般情報。',
      MAP: '位置',
      MAPDESCRIPTION: 'Google マップ上のデバイスの位置。',
      WIFICLIENTLIST: 'WiFiクライアントリスト',
      WIFICLIENTLISTDESCRIPTION: 'WiFi クライアントがデバイスに接続します。',
      ERRORSTATUS: 'エラーステータス',
      ERRORSTATUSDESCRIPTION: 'イベントコード、エラーの説明、エラー時刻を含むデバイスのエラーリスト。',
      ERRORSTCARE: "デバイスの取得",
      SELECTDEVICE: "デバイスの選択",
      SERIALNUMBER: "シリアルナンバー",
      PRODUCTNAME: "商品名",
    },
    FIVEGC: {
      CELL_CONNECTED: "セルが接続されています",
      CELL_CONNECTED_DESCRIPTION: "接続されている正常に機能している無線の数を表示します。",
      CELL_DISCONNECTED: "セルが切断されました",
      CELL_DISCONNECTED_DESCRIPTION: "切断された無線の数を表示します。",
      ACTIVE_UE: 'アクティブUE',
      ACTIVE_UE_DESCRIPTION: 'アクティブUEを持つセルの数。',
      NO_ACTIVE_UE: '非アクティブUE',
      NO_ACTIVE_UE_DESCRIPTION: '活動がないセルの数を示します。これはUEが接続されていないという意味ではなく、接続されていても活動していない可能性があります。',
      CELLS_WITHOUT_ATTACHED_UE: 'UEが接続されていないセル',
      CELLS_WITHOUT_ATTACHED_UE_DESCRIPTION: '接続されているがUEが接続されていない無線の数を示します。',
      CELLS_WITH_ACTIVE_UE: "アクティブなユーザーのセル",
      CELLS_WITH_ACTIVE_UE_DESCRIPTION: "このウィジェットは、アクティビティの状態を示す棒グラフ形式の指標を表示し、アクティビティなし、1〜5人のアクティブユーザー、6〜10人のアクティブユーザー、11〜20人のアクティブユーザー、20人以上のアクティブユーザーを含みます。",
      CELLS_LIST: "セルのリスト",
      CELLS_LIST_DESCRIPTION: "このウィジェットは、5Gコアに接続されているセルのリストを表示します。",
      ALARM_LIST: "アラームリスト",
      ALARM_LIST_DESCRIPTION: "アラームリストは、システム内のさまざまなアラームに関する情報を提供します。",
      FIVECORENETWORK: "5Gコアネットワーク",
      FIVECORENETWORK_DESCRIPTION: "このウィジェットは、システム/設定で5GコアURLを設定した5Gコアへのリンクとして機能します。",
      CELL_THROUGHPUT: "セルスループット",
      CELL_THROUGHPUT_DESCRIPTION: "このウィジェットは、セルの現在のダウンロードおよびアップロードスループットを表示します。",
      UE_THROUGHPUT_BY_CELL: "セル別UEスループット",
      UE_THROUGHPUT_BY_CELL_DESCRIPTION: "セル別UEスループットは、特定のセルに接続されたすべてのUEの総ダウンロードおよびアップロードスループットを表示し、データ伝送性能の概要を提供します。",
      UE_LIST: "UEリスト",
      UE_LIST_DESCRIPTION: "UE情報は、5Gコアネットワークに接続されたユーザー機器（UE）に関する詳細情報を提供します。",
      UE_5QI_PACKET: "UE 5QIパケット",
      UE_5QI_PACKET_DESCRIPTION: "このウィジェットは、UEの5QIパケットを表示し、上りおよび下りのさまざまなトラフィック指標およびドロップ率を含みます。",
      ACTIVE: 'アクティブ',
      INACTIVE: '非アクティブ',
      STATUS: 'ステータス',
      NAME: '名前',
      STATE: '状態',
      gNBID: 'gNB ID',
      BAND: 'バンド',
      SESSIONS: 'セッション数',
      DATA: 'データ',
      PLMN: 'PLMN',
      TAC: 'TAC',
      IP: 'IP',
      SEVERITY: '重大度',
      ALARM_ID: 'アラームID',
      EVENT_TYPE: 'イベントタイプ',
      EVENT_TIME: 'イベント時間',
      PROBABLE_CAUSE: '原因',
      SPECIFIC_PROBLEM: '具体的な問題',
      OBJ_CLASS: "オブジェクトクラス",
      ADD_TEXT: "テキストを追加",
      THROUGHPUT_HISTORY: "スループット履歴",
      STATUS_HISTORY: "ステータス履歴",
      HISTORY: '歴史',
      IMSI: 'IMSI',
      PHONENUMBER: "電話番号",
      IMEI: 'IMEI',
      SUB_TYPE: 'サブタイプ',
      REG_TYPE: "登録タイプ",
      LOCAL_ATTACHMENT: 'ローカルアタッチメント',
      LAST_ACTIVITY_TIME: "最終活動時間",
      REGISTRATION_TIME: "登録時間",
      DEREGISTRATION_TIME: "登録解除時間",
      UL_THROUGHPUT: '上りスループット',
      DL_THROUGHPUT: '下りスループット',
      SUPI: 'SUPI',
      FIVEQI: '5QI',
      UL_INGRESS: '上りイングレス',
      UL_EGRESS: '上りエグレス',
      UL_DROPPED: '上りドロップ',
      UL_TOTAL_INGRESS: '上りトータルイングレス',
      UL_TOTAL_EGRESS: '上りトータルエグレス',
      UL_TOTAL_DROPPED: '上りトータルドロップ',
      DL_INGRESS: '下りイングレス',
      DL_EGRESS: '下りエグレス',
      DL_DROPPED: '下りドロップ',
      DL_TOTAL_INGRESS: '下りトータルイングレス',
      DL_TOTAL_EGRESS: '下りトータルエグレス',
      DL_TOTAL_DROPPED: '下りトータルドロップ',
      ALARM: 'アラーム',
      ALARM_DESCRIPTION: '5Gコアネットワークからの最新のアラームをリアルタイムで表示します。アラームの重大度、タイムスタンプ、簡単な説明が含まれ、ネットワークの問題を迅速に特定して対応することができます。',
      ue_activity: '5GコアのUE活動',
      ue_activity_DESCRIPTION: 'この円グラフは、ユーザー機器（UE）の活動状況を示しています。',
      ue_presence: '5GコアのUE存在',
      ue_presence_DESCRIPTION: '接続されている（接続中）および切断されている（接続解除）UEの数を示します。',
      cells_info: '5Gコアのセル',
      cells_info_DESCRIPTION: '5Gコアネットワークに接続されているセルに関する詳細情報を提供します。',
      ACTIVITY_DESCRIPTION: {
        ACTIVE: 'アクティブなセグメントは、活動中の接続されたUEの数を示します。',
        DATA: 'データセグメントは、システム内のデータセッションの数を示します。UEは複数のデータセッションを持つことがあります。',
        CALLS: '通話セグメントは、現在通話中の接続されたUEの数を示します。',
        INACTIVE: '非アクティブなセグメントは、活動していない接続されたUEの数を示します。'
      },
      PRESENCE_DESCRIPTION: {
        ATTACHED: '「接続済み」の状態は、ユーザーデバイスがネットワークに正常に接続されていることを示します。',
        DETACHED: '「未接続」の状態は、ユーザーデバイスがネットワークから切断された、またはまだ接続されていないことを示します。'
      },
      LICENSE_DESCRIPTION: {
        UE: '使用中のUEライセンス席数と利用可能なUEライセンス席数を表示します。',
        NETWORK: '使用中のPDNライセンス席数と利用可能なPDNライセンス席数を表示します。',
        CELLS_4G: '使用中の4Gセルライセンス席数と利用可能な4Gセルライセンス席数の合計を示します。',
        CELLS_5G: '使用中の5Gセルライセンス席数と利用可能な5Gセルライセンス席数の合計を示します。',
      },
      ACTIVITY_CHART_DESCRIPTION: {
        ACTIVE_20PLUS: "20以上のアクティブなユーザーがいるセルの数を示します。",
        ACTIVE_11To20: "11から20のアクティブなユーザーがいるセルの数を示します。",
        ACTIVE_6To10: "6から10のアクティブなユーザーがいるセルの数を示します。",
        ACTIVE_1To5: "1から5のアクティブなユーザーがいるセルの数を示します。",
        NOACTIVE: "アクティブなユーザーがいないセルを示します。これは必ずしもユーザーが接続されていないことを意味するわけではなく、接続されていてもアクティブでない場合があります。"
      },
      ACTION: {
        RESTARTSYSTEM: 'システムを再起動する',
        DORESTARTSYSTEM: '続行すると、すべてのサービスが一時的に失われます！',
        RESTARTSYSTEM_SUCESS: 'システムが正常に再起動しました！',
        RESTARTSYSTEM_FAIL: 'システムの再起動に失敗しました！',
        BACKUPCONFIGURATION: '設定をバックアップ',
        BACKUPCONFIGURATION_SUCESS: '設定を正常にバックアップしました！',
        BACKUPCONFIGURATION_FAIL: '設定のバックアップに失敗しました！',
        RESTORECONFIGURATION: '設定を復元',
        RESTORECONFIGURATION_SUCESS: '設定を正常に復元しました！',
        RESTORECONFIGURATION_FAIL: '設定の復元に失敗しました！',
        FACTORYRESET: '工場出荷時の設定にリセット',
        DOFACTORYRESET: '設定を工場出荷時のデフォルトにリセットしようとしています！',
        FACTORYRESET_SUCESS: '工場出荷時の設定に正常にリセットしました！',
        FACTORYRESET_FAIL: '工場出荷時の設定へのリセットに失敗しました！',
        REFRESHALL: "すべて更新",
        REFRESHALL_SUCESS: "すべての更新が成功しました！",
        REFRESHALL_FAIL: "すべての更新に失敗しました！",
        SYSTEMMANAGEMENT: "システム管理",
      },
    },
    POWER: {
      ENERGYSAVING: 'エネルギー管理',
      STARTTIME_MUST_BE_EARLIER: "開始時刻は終了時刻より前でなければなりません",
      STARTDATE_MUST_BE_EARLIER: "開始日は終了日より前でなければなりません。",
      POWER_CONSUMPTION_SUMMARY: "電力消費の概要",
      REAL_AVG_ENERGY: "実際の平均エネルギー",
      NORMAL_STATE_ENERGY: "通常状態のエネルギー",
      ENERGY_CONSUMPTION_BY_POLICY: "ポリシー別エネルギー消費",
      POWER_CONSUMPTION: "電力消費",
      TX_POWER: "送信出力",
      NETWORK_USAGE: "ネットワーク使用量",
      UPLOAD: "アップロード",
      DOWNLOAD: "ダウンロード",
      UE_COUNT: "接続デバイス数",
      LOCATION: "位置",
      CURRENT_POLICY: "現在のポリシー",
      POLICY_SETTING: "ポリシー設定",
      ENERGY_POLICY: "省エネポリシー",
      MILD_SLEEP_DESCRIPTION: "デバイスを完全に動作させたまま送信出力を低減します。",
      MODERATE_SLEEP_DESCRIPTION: "電力を低減し、無線機能を一時的に無効にしてさらに省エネを図ります。",
      WAKEABLE_DEEPSLEEP_DESCRIPTION: "デバイスを徐々に電源オフにし、必要に応じて自動で復帰できるようにします。",
      DEEPSLEEP_DESCRIPTION: "デバイスを完全に電源オフにします。再起動には手動操作が必要です。",
      SCHEDULE_SETTING: "スケジュール設定",
      POLICY_LIST: "ポリシー一覧",
      DURATION: "持続時間",
      ENERGY: "電力消費",
      POWER_CONSUMPTION_PER_DEVICE: "デバイスごとの電力消費",
      DL_PRB_LOADING: "下り PRB 負荷",
      UL_PRB_LOADING: "上り PRB 負荷",
      TOTAL_POWER_CONSUMPTION: "総電力消費",
      NAME: "名前",
      SCHEDULE: "スケジュール",
      CONDITION: "条件",
      ACTIVE: "有効",
      NO_POLICIES_FOUND: "ポリシーが見つかりません。",
      NO_POLICY_CONFIGURED: "このデバイスには省エネポリシーが設定されていません。",
      ENABLE_TO_ADD_POLICIES: "省エネ制御を有効にするとポリシーを追加できます。",
      ENERGY_SAVING_NOT_ENABLED: "省エネ制御が有効になっていません。",
      NO_POLICY_SETTINGS_AVAILABLE: "このデバイスには使用可能なポリシー設定がありません。",
      ENABLE_TO_CONFIGURE_POLICY: "省エネ制御を有効にしてポリシーの詳細を設定または表示してください。",
      ENERGY_MODE: "省エネモード",
      TRAFFIC_LOADING: "トラフィック負荷",
      UE_CONTEXT: "UE コンテキスト",
      POLICY_DISABLE_TITLE: "省電力モードを無効にしますか？",
      POLICY_DISABLE_TEXT: "無効にすると、デバイスは通常の電力設定に戻ります。本当に無効にしてもよろしいですか？",
    }
  }
}
